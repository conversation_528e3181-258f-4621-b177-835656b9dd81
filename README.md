# LLM Agent Excel

*via DFP LLM dataframe processor.*

## TODOs

- [x] TODO -> Add a proper tests directory for unit and integration tests
- [x] TODO -> Standardize entry point between run.py and demo/main.py
- [x] TODO -> Create missing .env.sample and .env.google_creds.json.sample files
- [ ] TODO -> Clarify parallel processing implementation details in documentation
- [x] TODO -> Add details about column name matching implementation
- [ ] TODO -> Document error handling and recovery strategies
- [ ] TODO -> Create proper examples for different use cases
- [ ] TODO -> Clarify file format compatibility limitations

## Overview

LLMDataProcessor_v3 is an advanced tool for processing data using Language Models (LLMs). It's designed to be more organized, maintainable, and versatile compared to its predecessor, LLMDataProcessor_v2. This tool supports parallel processing of dataframes using various LLM models and processing chains, with capabilities to work with both Google Sheets and local Excel/CSV files.

## Setup

1. Clone the repository:  # TODO -> update this.

   ```markdown
   git clone https://github.com/invisible-tech/llm-agent-excel
   cd llm-agent-excel
   ```
2. Install the required dependencies:



Using uv for deps. Normally `make sync` or `uv sync` is all you need, then `uv run run.py`.

However Codex runs with restricted network access which prevents uv from
downloading wheels from `files.pythonhosted.org`. To work around this, download
all dependencies ahead of time and keep them in `wheelhouse/`:

```bash
# outside Codex with internet access
uv pip download -r pyproject.toml --dest wheelhouse

# inside Codex
scripts/codex_setup.sh
```

3. Setup .env and .env.google_creds.json (if google sheets)

```bash
cp .env.sample .env
cp .env.google_creds.json.sample .env.google_creds.json
```

Update the .env and .env.google_creds.json files with your API keys and google credentials.

(4) Run the tool using uv run run.py or checkout DFP (LLM Data Frame Processor) examples to understand the underlying engine.

## System Architecture

```
                    +-------------------------------+
                     |          run.py              |
                     | (Configuration & Entry Point) |
                    +-------------------------------+
                              |
                              v
                    +-------------------------------+
                    |         demo/main.py          |
                    |     (Command-line Interface)  |
                    +-------------------------------+
                              |
                              v
                    +-------------------------------+
                    | ParallelLLMDataFrameProcessor |
                    |      (Core Processing)        |
                    +-------------------------------+
                        /                    \
            +--------------+            +--------------+
            | Chain Steps  |            | Data Loading |
            +--------------+            +--------------+
                  |                           |
        +-------------------+      +-------------------+
        | Template Processor|      |  Google Sheets/   |
        | Python Function   |      |  Excel/CSV I/O    |
        | LLM Integration   |      +-------------------+
        +-------------------+
```

### Sequence Diagram

```mermaid
sequenceDiagram
    participant User
    participant Main as run.py
    participant Proc as Processor
    participant Data as Data Source (Sheet/File)
    participant LLM

    User->>Main: Run command
    Main->>Proc: Initialize processor
    Proc->>Data: Load data
    Data-->>Proc: Return data

    loop For each column to process
        Proc->>LLM: Send prompts
        LLM-->>Proc: Return responses
    end

    Proc->>Data: Save results
    Proc-->>Main: Return processed data
    Main-->>User: Display results
```

## DFP (Data Frame Processor) Architecture

```
+------------------+    +-------------------+    +----------------+
| DataFrame Input  |--->| Chain Definition  |--->| Parallel      |
| (CSV/Excel/Mem)  |    | (Processing Steps)|    | Processing    |
+------------------+    +-------------------+    +----------------+
                                                       |
                           +-------------------+       |
                           | Result DataFrame  |<------+
                           | (Processed Data)  |
                           +-------------------+
```

## Note on Entry Points

This repository provides two main entry points:

1. **root/run.py**: The primary entry point with centralized configuration
2. **demo/main.py**: Detailed implementation with command-line interface

Both files can be used to run the application. For most purposes, using the root run.py file is recommended.

## LLM Rules

// Manually written by Foster. Don't Delete.

1. Avoid bandaids, ask for direction if stuck. E.g., Do not switch to Excel / CSV if Gsheets fails.
2. Avoid creating quick temporary test files unless entirely required in which case name them with .temp_ prefix.
3. Put all tests in the appropriate tests folder (e.g., demo/tests/ for demo-related tests).

## Testing

Tests are located in the `demo/tests/` directory and use the unittest framework. To run the tests:

```bash
# Install dependencies with dev packages

make sync
or
uv sync --dev

# Activate the virtual environment
source .venv/bin/activate

# Run all tests
python demo/tests/run_tests.py
```

You can also use the included Makefile to run tests more easily:

```bash
# Run all tests
make test

# Run tests with verbose output
make test-v
```

All tests should pass with 100% success rate. Warning and error messages in the test output are expected and indicate that error handling functionality is working correctly.

## Key Features

1. Modular architecture for easier maintenance and extensibility
2. Support for various LLM models including OpenAI, Claude, Fireworks, Cohere, and Grok
3. Parallel processing capabilities for improved performance
4. Flexible chain steps with customizable parameters
5. Asynchronous API calls with rate limiting
6. Support for fanout operations in processing chains
7. Comprehensive error handling and retry mechanisms
8. Utility functions for data preprocessing and saving results
9. Support for both Google Sheets and local Excel/CSV files
10. Python function execution via LLM (`py-func` columns)
11. Rich text formatting for all console outputs using Rich and Loguru
12. Parallel batch processing for improved efficiency

## Key Features for Prompt Generation sheet

1. Generate prompts from a template
2. Generate values for variables in a prompt
3. Save results to a Google Sheet or local Excel/CSV file
4. Automatic caching of fetched Google Sheets data to local files
5. Execute Python functions via LLM to process data in Google Sheets
6. Sequential column processing to maintain dependencies between columns
7. Rich-formatted console output for better monitoring and debugging

## Core Structure

```
project_root/
├── llm_processor/
│   ├── __init__.py
│   ├── processor.py        # Core processing engine
│   ├── chain_step.py       # Processing step definitions
│   ├── models.py           # LLM model configurations
│   ├── google_sheets_utils.py  # Google Sheets integration
│   ├── grok.py             # Grok-specific implementations
│   ├── preprocessing.py    # Data preprocessing utilities
│   └── config.py           # Configuration settings
├── demo/
│   ├── main.py             # Main script for processing with rich formatting
│   ├── processors/         # Specialized processors for templates and functions
│   └── utils/              # Utility functions for file handling and configuration
├── dfp_llm_proc_examples/
│   ├── example_regular_chain.py   # Basic chain processing example
│   ├── example_fanout_chain.py    # Fanout processing example
│   └── 0_very_simple_examples.py  # Simple examples to get started
├── scripts/                # Feature development scripts
│   └── *.md                # Feature documentation
├── prompt_constructors/    # Prompt construction utilities
├── imghdr.py               # Python 3.13 compatibility shim for removed imghdr module
└── run.py                  # Root configuration and entry point
```

## Dependencies

For a complete list of dependencies, see the pyproject.toml file.

## Usage

Here's a basic example of how to use the LLMDataProcessor:

```python
import pandas as pd
from llm_processor import ParallelLLMDataFrameProcessor, ChainStep

# Create a sample dataframe
df = pd.DataFrame({
    "prompt": ["Explain quantum computing", "Describe machine learning"]
})

# Initialize the processor
processor = ParallelLLMDataFrameProcessor()

# Define a chain of steps
chain = [
    ChainStep(
        pt="prompt",  # Source column with prompts
        model="claude-3-5-sonnet-latest",  # Using latest Claude model
        temperature=0.7,
        max_tokens=150,
        col="response"  # Target column for results
    )
]

# Execute the chain
result_df = processor.execute_chain(df, chain)

# Display results
print(result_df)
```

For more advanced usage and examples, check the `dfp_llm_proc_examples/` directory.

## Usage for Prompt Generation

You can run the main script with different input sources:

### Using Default Google Sheet

```bash
python demo/main.py
```

### Using a Specific Google Sheet

```bash
python demo/main.py --file-path "https://docs.google.com/spreadsheets/d/YOUR_SHEET_ID/edit"
```

### Using a Local Excel File

```bash
python demo/main.py --file-path "path/to/your/local/file.xlsx"
```

### Using a Local CSV File

```bash
python demo/main.py --file-path "path/to/your/local/file.csv"
```

### Specifying a Sheet Name

```bash
python demo/main.py --sheet-name "YourSheetName"
```

Note: Sheet name only applies to Excel files and Google Sheets, not CSV files.

### Regenerating All Values

```bash
python demo/main.py --regenerate-all
```

This will regenerate all values in the output columns, ignoring any existing content.

### Debug Mode

```bash
python demo/main.py --debug
```

This enables verbose logging for debugging purposes.

## Input File Format

Whether you're using a Google Sheet, local Excel file, or CSV file, the format needs to follow these guidelines:

NOTE - THIS SHOULD NOW BE REDUNDANT AND CONFIG COLS SHOULD BE SELECTED BY NAME, NOT ROW NUMBER.

1. **Row 1**: Column labels indicating input/output types
   - Input columns must contain "inputs" in their labels
   - Output columns must contain "py-llm" in their labels
   - Python function columns must contain "py-func" in their labels
2. **Row 4**:
   - For "py-llm" columns: Prompt templates with variables in the format `{{variable_name}}`
   - For "py-func" columns: Python function definitions to be executed via LLM
3. **Row 5**: Model names for each output column (e.g., "claude-3-5-sonnet-latest", "gpt-4o")
4. **Row 6**: Temperature values for each output column (float values)
5. **Row 7**: Maximum output tokens for each output column (integer values)
6. **Rows 20+**: Actual data rows with:
   - Input values in the corresponding input columns
   - `<generate>`, `//`, `<g>`, or `generate` tags in output columns where you want generated content

The system will:

1. Process columns in left-to-right order to maintain dependencies
2. Detect rows with generation tags
3. Process Python functions first, then LLM prompt templates
4. Make previous column outputs available as inputs to subsequent columns
5. Update both the local file and (for Google Sheets) the remote sheet with generated content

## Python Function Execution (py-func)

The system supports executing Python functions via LLM. To use this feature:

1. Create a column with "py-func" in the header label
2. Define a Python function in row 4 of that column
3. The function can accept parameters that match column names in your data
4. Rows with generation tags will execute the function with data from that row

Example Python function:

```python
def transform_text(input_text, capitalize=False):
    """Transform the input text based on parameters."""
    result = input_text.strip()
    if capitalize:
        result = result.upper()
    return f"Processed: {result}"
```

## Rich Formatted Output

The latest version uses Rich and Loguru for beautiful, informative console output:

- Color-coded log levels
- Progress panels for processing status
- Tables for summarizing results
- Clear error messages with context
- Processing statistics and timing information

## File Format Support

The tool automatically detects the file format based on extension:

- `.xlsx`, `.xls`: Excel format
- `.csv`: CSV format
- For files without a recognized extension, the system will attempt to read them as Excel first, then CSV

When saving output, the system preserves the original file format.

## Column Name Matching

This project includes robust column name matching capabilities to handle typos and variations in column names when processing Python functions (py-func) and LLM templates (py-llm).

### Features

- **Levenshtein Distance Matching**: Uses the Levenshtein distance algorithm to find the closest matching column name when a user makes a typo.
- **Case-Insensitive Matching**: Handles different case variations (e.g., "FirstName" will match "first_name").
- **Format Conversion**: Handles conversion between camelCase and snake_case in column names.
- **Acronym Detection**: Detects column name acronyms (e.g., "cn" will match "company_name").
- **Substring Matching**: Improves matching when one string is contained within another.

### How It Works

When a user specifies a column name in a Python function or template that doesn't exactly match an available column, the system:

1. Calculates similarity scores using Levenshtein distance
2. Applies additional heuristics for case-insensitivity, camelCase/snake_case conversion, etc.
3. Selects the best match above a configurable threshold (default: 0.55)
4. Provides clear logs and visual feedback about the matches made

### Example

```python
# User function with typos
def generate_greeting(first_neme, companyname):
    return f"Hello from {companyname}, {first_neme}!"

# Available columns: ["first_name", "company_name"]
# The system will correctly match:
# - "first_neme" → "first_name"
# - "companyname" → "company_name"
```

### Configuration

The matching threshold can be adjusted in the `find_best_column_match` function in `demo/utils/column_matcher.py`. The default threshold is 0.55, which provides a good balance between flexibility and accuracy.

## Parallel Processing

The system processes data column by column (rather than row by row) to maintain dependencies between columns. This approach allows for:

1. Processing all rows for a single column in parallel
2. Ensuring that dependencies between columns are maintained
3. Making efficient use of LLM rate limits

This design optimizes for throughput and ensures consistent data across related columns.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
