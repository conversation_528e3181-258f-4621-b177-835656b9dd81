# Contains the LLM class with constants for various models

"""
Contains the LLM class with constants for various language models.

This module defines a class `LLM` that holds constant values representing
different language models from various providers such as OpenAI, Claude,
Fireworks, Cohere, and others. These constants can be used throughout the
project to specify which model to use for different tasks.

Classes:
    LLM: A class containing constants for various LLM models.
"""


class LLM:
    """
    Class containing constants for various LLM models.

    This class provides a centralized location for storing model identifiers,
    making it easier to manage and update supported models across the project.
    """


    # Claude Models
    claude_3_5_sonnet = "claude-3-5-sonnet-20240620"
    claude_3_haiku = "claude-3-haiku-20240307"
    claude_3_opus = "claude-3-opus-20240229"
    claude_3_sonnet = "claude-3-sonnet-20240229"

    # OpenAI Models
    gpt_3_5_turbo_instruct = "gpt-3.5-turbo-instruct"
    gpt_3_5_turbo = "openai/gpt-3.5-turbo"
    gpt_4o = "gpt-4o"
    gpt_4o_mini = "gpt-4o-mini"
    gpt_4_turbo = "gpt-4-turbo"
    gpt_4_vision_preview = "gpt-4-vision-preview"
    gpt_4 = "gpt-4"

    # Fine-Tuned GPT-4 Models
    # ft_gpt_4_0613 = "ft:gpt-4-0613"  # not found or no access
    # ft_gpt_4o_2024_05_13 = "ft:gpt-4o-2024-05-13"  # not found or no access

    # Google Gemini Models
    gemini_pro_completion = "gemini/gemini-pro"
    gemini_1_5_pro_latest = "gemini/gemini-1.5-pro-latest"
    gemini_1_5_flash = "gemini/gemini-1.5-flash"
    # gemini_pro_vision = "gemini-pro-vision"  # requires google-cloud-aiplatform; outdated

    # Perplexity Models
    pplx_llama_3_sonar_small_32k_online = (
        "perplexity/llama-3-sonar-small-32k-online"
    )
    pplx_llama_3_sonar_small_32k_chat = (
        "perplexity/llama-3-sonar-small-32k-chat"
    )
    pplx_llama_3_sonar_large_32k_online = (
        "perplexity/llama-3-sonar-large-32k-online"
    )
    pplx_llama_3_sonar_large_32k_chat = (
        "perplexity/llama-3-sonar-large-32k-chat"
    )
    pplx_llama_3_8b_instruct = "perplexity/llama-3-8b-instruct"
    pplx_llama_3_70b_instruct = "perplexity/llama-3-70b-instruct"
    pplx_mixtral_8x7b_instruct = "perplexity/mixtral-8x7b-instruct"

    # pplx_7b_chat = "perplexity/pplx-7b-chat"  # possibly outdated
    # pplx_70b_chat = "perplexity/pplx-70b-chat"  # possibly outdated
    # pplx_7b_online = "perplexity/pplx-7b-online"  # possibly outdated
    # pplx_70b_online = "perplexity/pplx-70b-online"  # possibly outdated
    # pplx_7b_chat_alpha = "perplexity/pplx-7b-chat-alpha"  # possibly outdated
    # pplx_70b_chat_alpha = "perplexity/pplx-70b-chat-alpha"  # possibly outdated
    # openhermes_2_mistral_7b = "perplexity/openhermes-2-mistral-7b"  # possibly outdated
    # openhermes_2_5_mistral_7b = "perplexity/openhermes-2.5-mistral-7b"  # possibly outdated
    # codellama_34b_instruct = "perplexity/codellama-34b-instruct"  # possibly outdated
    # llama_2_13b_chat = "perplexity/llama-2-13b-chat"  # possibly outdated
    # llama_2_70b_chat = "perplexity/llama-2-70b-chat"  # possibly outdated

    # Mistral Models
    mistral_7b_instruct_perplexity = "perplexity/mistral-7b-instruct"
    mistral_small = "mistral/mistral-small-latest"
    mistral_medium = "mistral/mistral-medium-latest"
    mistral_large = "mistral/mistral-large-latest"
    mistral_7b = "mistral/open-mistral-7b"
    mixtral_8x7b = "mistral/open-mixtral-8x7b"
    mixtral_8x22b = "mistral/open-mixtral-8x22b"
    codestral = "mistral/codestral-latest"

    # Fireworks Models (Newest)
    fire_llama_v31_405b_instruct = "fireworks_ai/llama-v3p1-405b-instruct"
    fire_llama_v31_70b_instruct = "fireworks_ai/llama-v3p1-70b-instruct"
    fire_llama_v31_8b_instruct = "fireworks_ai/llama-v3p1-8b-instruct"

    # Fireworks Models
    firellava_13b = "fireworks_ai/firellava-13b"
    firefunction_v1 = "fireworks_ai/firefunction-v1"
    mixtral_8x7b_instruct_fireworks = "fireworks_ai/mixtral-8x7b-instruct"
    llama_v3_70b_instruct_fireworks = "fireworks_ai/llama-v3-70b-instruct"
    llama_v3_70b_instruct_hf_fireworks = (
        "fireworks_ai/llama-v3-70b-instruct-hf"
    )
    llama_v3_8b_instruct_fireworks = "fireworks_ai/llama-v3-8b-instruct"
    llama_v3_8b_instruct_hf_fireworks = "fireworks_ai/llama-v3-8b-instruct-hf"
    llama_v3_1_70b_instruct_fireworks = (
        "accounts/fireworks/models/llama-v3p1-70b-instruct"
    )
    mixtral_8x7b_instruct_hf_fireworks = (
        "fireworks_ai/mixtral-8x7b-instruct-hf"
    )
    mythomax_l2_13b_fireworks = "fireworks_ai/mythomax-l2-13b"
    phi_3_vision_128k_instruct_fireworks = (
        "fireworks_ai/phi-3-vision-128k-instruct"
    )
    qwen2_72b_instruct_fireworks = "fireworks_ai/qwen2-72b-instruct"

    # Fireworks Models (not available with the current subscription)
    # bleat_adapter_fireworks = "fireworks_ai/bleat-adapter"
    # dbrx_instruct_fireworks = "fireworks_ai/dbrx-instruct"
    # gemma_7b_it_fireworks = "fireworks_ai/gemma-7b-it"
    # hermes_2_pro_mistral_7b_fireworks = "fireworks_ai/hermes-2-pro-mistral-7b"
    # japanese_stablelm_instruct_beta_70b_fireworks = (
    #     "fireworks_ai/japanese-stablelm-instruct-beta-70b"
    # )
    # japanese_stablelm_instruct_gamma_7b_fireworks = (
    #     "fireworks_ai/japanese-stablelm-instruct-gamma-7b"
    # )
    # llama_v2_13b_fireworks = "fireworks_ai/llama-v2-13b"
    # llama_v2_13b_chat_fireworks = "fireworks_ai/llama-v2-13b-chat"
    # llama_v2_34b_code_instruct_fireworks = "fireworks_ai/llama-v2-34b-code-instruct"
    # llama_v2_70b_chat_fireworks = "fireworks_ai/llama-v2-70b-chat"
    # llama_v2_7b_fireworks = "fireworks_ai/llama-v2-7b"
    # llama_v2_7b_chat_fireworks = "fireworks_ai/llama-v2-7b-chat"
    # llama_v3_8b_hf_fireworks = "fireworks_ai/llama-v3-8b-hf"
    # llava_yi_34b_fireworks = "fireworks_ai/llava-yi-34b"
    # mistral_7b_fireworks = "fireworks_ai/mistral-7b"
    # mistral_7b_instruct_4k_fireworks = "fireworks_ai/mistral-7b-instruct-4k"
    # mistral_7b_instruct_v0_2_fireworks = "fireworks_ai/mistral-7b-instruct-v0p2"
    # mistral_7b_instruct_v3_fireworks = "fireworks_ai/mistral-7b-instruct-v3"
    # mixtral_8x22b_fireworks = "fireworks_ai/mixtral-8x22b-instruct-hf"
    # mixtral_8x22b_hf_fireworks = "fireworks_ai/mixtral-8x22b-hf"
    # mixtral_8x22b_instruct_hf_fireworks = "fireworks_ai/mixtral-8x22b-instruct-hf"
    # mixtral_8x7b_fireworks = "fireworks_ai/mixtral-8x7b"
    # nous_hermes_2_mixtral_8x7b_dpo_fp8_fireworks = "fireworks_ai/nous-hermes-2-mixtral-8x7b-dpo-fp8"
    # phi_3_mini_128k_instruct_fireworks = "fireworks_ai/phi-3-mini-128k-instruct"
    # qwen1p5_72b_chat_fireworks = "fireworks_ai/qwen1p5-72b-chat"
    # stablelm_2_zephyr_2b_fireworks = "fireworks_ai/stablelm-2-zephyr-2b"
    # stablelm_zephyr_3b_fireworks = "fireworks_ai/stablelm-zephyr-3b"

    # Code Llama Python Models on Fireworks (not available with the current subscription)
    # code_llama_70b_python_fireworks = "fireworks_ai/code-llama-70b-python"
    # code_llama_34b_python_fireworks = "fireworks_ai/code-llama-34b-python"
    # code_llama_13b_python_fireworks = "fireworks_ai/code-llama-13b-python"

    # Cohere
    cohere_command = "command"
    cohere_command_light = "command-light"
    cohere_command_r = "command-r"
    cohere_command_r_plus = "command-r-plus"

    # AI21
    # j2_light = "j2-light"  # TODO: test
    # j2_mid = "j2-mid"
    # j2_ultra = "j2-ultra"

    # TODO: Add AI21, Cohere, and VLM models when implemented
