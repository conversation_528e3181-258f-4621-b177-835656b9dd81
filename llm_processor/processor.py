"""Contains the ParallelLLMDataFrameProcessor class for processing dataframes with LLM chains."""

# Standard library imports
import asyncio
import string
import time
import traceback
from concurrent.futures import ThreadPoolExecutor
from typing import Dict, List, Optional, Union
from uuid import uuid4
import re

# Third-party imports
import litellm
import pandas as pd
import tenacity
from asyncio_throttle import Throttler
from loguru import logger
from llm_processor.chain_step import ChainStep
from main_config import CFG  # Import CFG for access to MAX_TOKENS_PARAM_MAP


async def _call_litellm_acompletion(
    model: str,
    messages: List[Dict],
    temperature: float,
    max_tokens: int,
    reasoning_effort: Optional[str] = None,
    **kwargs,  # To accept other potential litellm args
) -> Dict:
    """
    Helper to call litellm.acompletion with correct parameter names for the model.

    Args:
        model: The model name to use
        messages: The messages to send to the model
        temperature: The temperature setting
        max_tokens: The maximum number of tokens for the response
        reasoning_effort: The reasoning effort setting (only used for models that support it)
        **kwargs: Additional arguments to pass to litellm.acompletion

    Returns:
        The response from the model
    """
    # Get the correct parameter name for max tokens based on the model
    max_tokens_param_name = CFG.MAX_TOKENS_PARAM_MAP.get(model, CFG.MAX_TOKENS_PARAM_MAP["default"])

    # Create parameters dictionary with the model and messages
    params = {
        "model": model,
        "messages": messages,
        **kwargs,
    }

    # Add max_tokens parameter with the correct name
    params[max_tokens_param_name] = max_tokens

    # Add temperature parameter if the model supports it
    temperature_param_name = CFG.TEMPERATURE_PARAM_MAP.get(model)
    if temperature_param_name:
        params[temperature_param_name] = temperature

    # Add reasoning_effort parameter if the model supports it
    reasoning_effort_param_name = CFG.REASONING_EFFORT_PARAM_MAP.get(model)
    if reasoning_effort_param_name and reasoning_effort:
        params[reasoning_effort_param_name] = reasoning_effort

    # Make the API call with the appropriate parameters
    return await litellm.acompletion(**params)


class ParallelLLMDataFrameProcessor:
    """Process dataframe rows in parallel with multiple LLM chains."""

    def __init__(
        self,
        def_model: str = "gpt-4o",
        def_temperature: float = 0.0,
        def_max_tokens: int = 8192,
        def_async_rate_limit: int = 10,
        def_thread_rate_limit: int = 10,
    ):
        """
        Initialize the ParallelLLMDataFrameProcessor.
        Dataframe columns are used to fill in prompt keywords in the prompt templates.
        Dataframe rows are processed in parallel.

        Args:
            def_model (str): Default model name.
            def_temperature (float): Default model temperature.
            def_max_tokens (int): Default model max_tokens.
            def_async_rate_limit (int): Default async rate limit (litellm models).
            def_thread_rate_limit (int): Default thread rate limit (X.ai Grok models).
        """
        self.def_model = def_model
        self.def_temperature = def_temperature
        self.def_max_tokens = def_max_tokens
        self.def_async_rate_limit = def_async_rate_limit
        self.def_thread_rate_limit = def_thread_rate_limit
        self.throttler = Throttler(rate_limit=def_async_rate_limit, period=1.0, retry_interval=0.1)
        self.thread_pool = ThreadPoolExecutor(max_workers=def_thread_rate_limit)
        logger.debug(
            f"Initialized with def_model={def_model}, def_temperature={def_temperature}, def_max_tokens={def_max_tokens}, rate_limit={def_async_rate_limit}"
        )

    @staticmethod
    def extract_fstring_keywords(fstring: str) -> List[str]:
        """
        Extract keywords from an f-string.
        Args: fstring (str): The f-string to extract keywords from.

        Returns: List[str]: A list of extracted keywords.
        """
        formatter = string.Formatter()
        keywords = [field_name for _, field_name, _, _ in formatter.parse(fstring) if field_name]
        return keywords

    @staticmethod
    async def _fanout_list(text: str) -> List[str]:
        """
        Convert a comma-separated string into a list. Assumes LLM output is a list.
        TODO: unclear if it handles commas within the list items.
        Args: text (str): The comma-separated string.

        Returns: List[str]: The list of items.
        """
        text = text.strip()
        if not (("[" in text) and ("]" in text)):
            raise ValueError(f"Response '{text[:10]}...{text[-10:]}' is not a list")
        text = text.split("[")[1].split("]")[0].strip()
        text = text.replace("\n\n", "\n").replace("\n", ",").replace(";", ",")
        text = text.split(",")
        chunks = [x.strip("'\" ") for x in text if x]
        return chunks

    @staticmethod
    def _remove_thinking_tags(text: str) -> str:
        """
        Removes <think>...</think> tags and their content from the LLM response.

        Args:
            text (str): The original LLM response text

        Returns:
            str: The cleaned text with thinking tags removed
        """
        # Remove <think>...</think> tags and their content
        cleaned_text = re.sub(r"<think>.*?</think>", "", text, flags=re.DOTALL)
        # Remove any extra newlines that might be left
        cleaned_text = re.sub(r"\n{3,}", "\n\n", cleaned_text)
        return cleaned_text.strip()

    async def _async_get_response(
        self,
        message: str,
        temperature: float,
        max_tokens: int,
        model: str,
        returns_list: bool = False,
        verbose: bool = False,
        reasoning_effort: Optional[str] = None,
    ) -> Union[str, List[str]]:
        """
        Get a response from the LLM model asynchronously.

        Args:
            message (str): The prompt message.
            temperature (float): The temperature for the LLM model.
            max_tokens (int): The maximum number of tokens for the LLM response.
            model (str): The name of the LLM model.
            returns_list (bool): Whether the response should be a list.
            verbose (bool): Whether to log verbose output.
            reasoning_effort (Optional[str]): The reasoning effort setting for models that support it.

        Returns:
            Union[str, List[str]]: The LLM response.
        """
        async with self.throttler:
            start = time.time()
            try:
                messages = [
                    {
                        "role": "system",
                        "content": "You are a helpful assistant.",
                    },
                    {"role": "user", "content": message},
                ]

                # Use the helper function to call litellm.acompletion with the correct parameters
                resp = await _call_litellm_acompletion(
                    model=model,
                    messages=messages,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    reasoning_effort=reasoning_effort,
                )

                text = resp["choices"][0]["message"]["content"]
                # Remove thinking tags from the response
                text = self._remove_thinking_tags(text)
            except Exception as e:
                error_type = type(e).__name__
                error_message = str(e)
                logger.error(f"Failed API call model={model}: {error_type} - {error_message}")
                if verbose:
                    logger.error(f"Full traceback: {traceback.format_exc()}")
                raise
            else:
                if returns_list:
                    text_list = await self._fanout_list(text)
                    logger.info(
                        f"Prompt: ['{text_list[0]}',...,'{text_list[-1]}'] len={len(text_list)} ✅ in {time.time() - start:.0f}s using {model}"
                    )
                    return text_list
                else:
                    logger.info(f"Prompt: '{text[:10]}...{text[-10:]}' ✅ in {time.time() - start:.0f}s using {model}")
                    return text

    async def _process_chain_step(
        self,
        c_row: pd.Series,
        step: ChainStep,
        mapping: dict,
        max_attempts: int,
    ) -> None:
        """
        Process a single chain step for a given row.

        Args:
            c_row (pd.Series): The current row of the DataFrame.
            step (ChainStep): The chain step to process.
            mapping (dict): The mapping of prompt keywords to column names.
            max_attempts (int): The maximum number of retry attempts for failed API calls.
        """
        prompt = c_row.get(step.pt)
        if c_row.get(step.col) and not step.overwrite:
            logger.debug(f"{step.col} already exists (skip)")
            return
        for k, v in mapping.items():
            prompt = prompt.replace(f"{{{k}}}", str(c_row.get(v, "N/A")))

        temperature = c_row.get("__temperature") or step.temperature or self.def_temperature
        max_tokens = c_row.get("__max_tokens") or step.max_tokens or self.def_max_tokens
        model = c_row.get("__model") or step.model or self.def_model
        reasoning_effort = c_row.get("__reasoning_effort") or step.reasoning_effort or CFG.DEFAULT_REASONING_EFFORT

        try:
            if max_attempts > 1:
                foo = tenacity.retry(
                    stop=tenacity.stop_after_attempt(max_attempts),
                    wait=tenacity.wait_exponential(exp_base=2, multiplier=2),
                    after=tenacity.after_log(logger=logger, log_level=1),
                )(self._async_get_response)
                response = await foo(
                    message=prompt,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    model=model,
                    returns_list=step.fanout,
                    reasoning_effort=reasoning_effort,
                )
            else:
                response = await self._async_get_response(
                    message=prompt,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    model=model,
                    returns_list=step.fanout,
                    reasoning_effort=reasoning_effort,
                )
                # logger.info(f"Response: {response}")
        except Exception as e:
            error_msg = str(e).replace("\n", " ")
            logger.error(f"Error in _process_chain_step: {error_msg}")
            c_row[step.col] = f"[N/A] [ERROR: {error_msg}]"
        else:
            c_row[step.col] = response

    async def _process_chain(
        self,
        input_row: pd.Series,
        chain: List[ChainStep],
        max_attempts: int,
    ) -> List[pd.Series]:
        """
        Process a chain of prompt steps for a given row.

        Args:
            input_row (pd.Series): The input row of the DataFrame.
            chain (List[ChainStep]): The list of chain steps to process.
            max_attempts (int): The maximum number of retry attempts for failed API calls.
            generate_variable_values (bool): Whether to generate variable values.

        Returns:
            List[pd.Series]: The list of processed rows.
        """
        assert max_attempts >= 1
        c_rows = [input_row.copy()]

        for step in chain:
            if step.mapping is None:
                keywords = self.extract_fstring_keywords(step.pt)
                mapping = {k: k for k in keywords}
            else:
                mapping = step.mapping

            tasks = [self._process_chain_step(c_row, step, mapping, max_attempts) for c_row in c_rows]
            await asyncio.gather(*tasks)

            if step.fanout:
                df = pd.DataFrame(c_rows)
                df = df.explode(column=step.col)
                c_rows = [r for _, r in df.iterrows()]
        return c_rows

    def _process_df_with_llm_parallel(
        self,
        df: pd.DataFrame,
        chain: List[ChainStep],
        n_rows: Optional[int],
        max_attempts: int,
    ) -> pd.DataFrame:
        """
        Process a DataFrame with LLM chains in parallel.

        Args:
            df (pd.DataFrame): The input DataFrame.
            chain (List[ChainStep]): The list of chain steps to process.
            n_rows (Optional[int]): The number of rows to process (None = all rows).
            max_attempts (int): The maximum number of retry attempts for failed API calls.
            generate_variable_values (bool): Whether to generate variable values.
        Returns:
            pd.DataFrame: The processed DataFrame.
        """
        assert max_attempts >= 0
        df_to_process = df.head(n_rows).copy() if n_rows is not None else df.copy()

        async def process_all_rows():
            tasks = [self._process_chain(row, chain, max_attempts) for _, row in df_to_process.iterrows()]
            return await asyncio.gather(*tasks)

        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            # No event loop in current thread, create a new one
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        processed_rows = loop.run_until_complete(process_all_rows())
        unnested_rows = [x for y in processed_rows for x in y]
        result_df = pd.DataFrame(unnested_rows, index=range(len(unnested_rows)))

        # Ensure all columns from the original DataFrame are present
        for col in df.columns:
            if col not in result_df.columns:
                result_df[col] = df[col]
        return result_df

    def execute_chain(
        self,
        df: pd.DataFrame,
        chain: List[ChainStep],
        max_attempts: int = 1,
        n_rows: Optional[int] = None,
        async_rate_limit: Optional[int] = None,
        thread_rate_limit: Optional[int] = None,
    ) -> pd.DataFrame:
        """
        Execute a chain of prompts from a dataframe in parallel.

        Model parameter precedence:
        1. Dataframe: columns  __model, __temperature, __max_tokens
        2. Chain step parameters: model, temperature, max_tokens
        3. Default dataframe processor parameters: def_model, def_temperature, def_max_tokens

        Args:
            df (pd.DataFrame): Prompt templates to process.
            chain (list[ChainStep]): A list of ChainStep objects.
            n_rows (Optional[int], optional): Number of head rows to process. Defaults to None.
            max_attempts (int, optional): Number of max attempts per API request. Defaults to 1.
            async_rate_limit (Optional[int], optional): Apply new rate limit for async API calls (litellm models).
            thread_rate_limit (Optional[int], optional): Apply new rate limit for thread API calls (Grok models).
        Returns:
            pd.DataFrame: The processed DataFrame.
        """
        self._update_rate_limits(async_rate_limit, thread_rate_limit)
        result_df = self._process_df_with_llm_parallel(
            df,
            chain,
            n_rows,
            max_attempts=max_attempts,
        )
        self._reset_rate_limits()
        return result_df

    def _update_rate_limits(self, async_rate_limit: Optional[int], thread_rate_limit: Optional[int]):
        if async_rate_limit is not None:
            self.throttler = Throttler(rate_limit=async_rate_limit, period=1.0, retry_interval=0.1)
        if thread_rate_limit is not None:
            self.thread_pool = ThreadPoolExecutor(max_workers=thread_rate_limit)

    def _reset_rate_limits(self):
        self.throttler = Throttler(
            rate_limit=self.def_async_rate_limit,
            period=1.0,
            retry_interval=0.1,
        )
        self.thread_pool = ThreadPoolExecutor(max_workers=self.def_thread_rate_limit)

    def rerun_chain(
        self,
        df: pd.DataFrame,
        chain: List[ChainStep],
        n_rows: Optional[int] = None,
        max_attempts: int = 1,
        async_rate_limit: Optional[int] = None,
        thread_rate_limit: Optional[int] = None,
    ) -> pd.DataFrame:
        """
        Rerun a chain of prompts from a dataframe in parallel.

        TODO: Explore merging this functionality with execute_chain method to reduce code duplication
        and simplify the API. Consider adding a 'rerun' bool flag parameter to execute_chain instead.

        Args:
            df (pd.DataFrame): Prompt templates to process.
            chain (list[ChainStep]): A list of ChainStep objects.
            n_rows (Optional[int], optional): Number of head rows to process. Defaults to None.
            max_attempts (int, optional): Number of max attempts per API request. Defaults to 1.
            async_rate_limit (Optional[int], optional): Apply new rate limit for async API calls (litellm models).
            thread_rate_limit (Optional[int], optional): Apply new rate limit for thread API calls (Grok models).

        Returns:
            pd.DataFrame: The processed DataFrame.
        """
        df = df.copy()
        df["id"] = [uuid4() for _ in range(len(df))]
        response_columns: List[str] = [step.col for step in chain]
        df[response_columns] = df[response_columns].map(lambda x: "" if x.startswith("[N/A]") else x)
        result_df = self.execute_chain(
            df,
            chain,
            n_rows,
            max_attempts=max_attempts,
            async_rate_limit=async_rate_limit,
            thread_rate_limit=thread_rate_limit,
        )
        return result_df
