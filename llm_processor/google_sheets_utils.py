import os
import pandas as pd
from loguru import logger

# from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from google.oauth2 import service_account
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
import json
from google.auth.exceptions import MalformedError
from colorama import Fore, Style, init
from google.auth.transport.requests import Request

# Initialize colorama
init(autoreset=True)

# Set up credentials
SCOPES = [
    "https://www.googleapis.com/auth/spreadsheets",
    "https://www.googleapis.com/auth/drive",
]
SERVICE_ACCOUNT_FILE = os.path.join(
    os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
    ".env.google_creds.json",
)

# TODO[86b35zcgw]: Fix up this google auth flow - need to understand how this script will be used


def get_service_account_creds():
    try:
        if not os.path.exists(SERVICE_ACCOUNT_FILE):
            raise FileNotFoundError(f"Service account file not found: {SERVICE_ACCOUNT_FILE}")

        with open(SERVICE_ACCOUNT_FILE, "r") as f:
            service_account_info = json.load(f)

        required_fields = ["token_uri", "client_email", "private_key"]
        missing_fields = [field for field in required_fields if field not in service_account_info]

        if missing_fields:
            raise MalformedError(f"Service account info is missing required fields: {', '.join(missing_fields)}")

        creds = service_account.Credentials.from_service_account_file(SERVICE_ACCOUNT_FILE, scopes=SCOPES)

        return creds

    except FileNotFoundError as e:
        logger.error(
            "Please ensure the service account key file exists at the specified path.",
            e,
        )
        raise e
    except json.JSONDecodeError as e:
        logger.error("Error: The service account file is not a valid JSON file.", e)
        exit(1)
    except MalformedError as e:
        logger.error(
            "Please generate a new service account key from the Google Cloud Console.",
            e,
        )
        exit(1)


def get_flow_creds():
    creds = None
    # The file token.json stores the user's access and refresh tokens, and is
    # created automatically when the authorization flow completes for the first
    # time.
    if os.path.exists("token.json"):
        creds = Credentials.from_authorized_user_file("token.json", SCOPES)
    # If there are no (valid) credentials available, let the user log in.
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
        else:
            flow = InstalledAppFlow.from_client_secrets_file(".env.google_creds.json", SCOPES)
        creds = flow.run_local_server(port=0)
        # Save the credentials for the next run
        with open("token.json", "w") as token:
            token.write(creds.to_json())

    return creds


try:
    creds = get_service_account_creds()
except FileNotFoundError as e:
    logger.error("Service account key file not found. Using flow credentials.", e)

    try:
        creds = get_flow_creds()
    except Exception as e:
        logger.error("Error getting flow credentials.", e)
        exit(1)


# Build the Google Sheets API service
service = build("sheets", "v4", credentials=creds)
drive_service = build("drive", "v3", credentials=creds)


def create_google_sheet(title):
    """Create a new Google Sheet and return its ID."""
    sheet_metadata = service.spreadsheets().create(body={"properties": {"title": title}}).execute()
    logger.info(f"{Fore.GREEN}Sheet created with ID: {sheet_metadata['spreadsheetId']}{Style.RESET_ALL}")
    return sheet_metadata["spreadsheetId"]


def add_new_sheet(sheet_id, new_sheet_name):
    """Add a new blank sheet to an existing Google Sheet."""
    try:
        # First check if sheet exists
        sheet_metadata = service.spreadsheets().get(spreadsheetId=sheet_id).execute()
        existing_sheets = [sheet["properties"]["title"] for sheet in sheet_metadata.get("sheets", [])]

        if new_sheet_name in existing_sheets:
            # Delete existing sheet
            existing_sheet_id = None
            for sheet in sheet_metadata["sheets"]:
                if sheet["properties"]["title"] == new_sheet_name:
                    existing_sheet_id = sheet["properties"]["sheetId"]
                    break

            if existing_sheet_id is not None:
                delete_request = {"requests": [{"deleteSheet": {"sheetId": existing_sheet_id}}]}
                service.spreadsheets().batchUpdate(spreadsheetId=sheet_id, body=delete_request).execute()
                logger.info(f"{Fore.YELLOW}Deleted existing sheet '{new_sheet_name}'{Style.RESET_ALL}")

        # Create new sheet
        requests = [{"addSheet": {"properties": {"title": new_sheet_name}}}]
        body = {"requests": requests}
        response = service.spreadsheets().batchUpdate(spreadsheetId=sheet_id, body=body).execute()
        logger.info(f"{Fore.GREEN}New sheet '{new_sheet_name}' created successfully.{Style.RESET_ALL}")

    except Exception as e:
        logger.error(f"{Fore.RED}Error managing sheet: {str(e)}{Style.RESET_ALL}")
        raise


def get_sheet_id(spreadsheet_id: str, sheet_name: str) -> int:
    """Get the sheet ID for a specific sheet name within a spreadsheet."""
    try:
        sheet_metadata = service.spreadsheets().get(spreadsheetId=spreadsheet_id).execute()
        sheets = sheet_metadata.get("sheets", "")
        for sheet in sheets:
            if sheet["properties"]["title"] == sheet_name:
                return sheet["properties"]["sheetId"]
        raise ValueError(f"Sheet '{sheet_name}' not found")
    except Exception as e:
        logger.error(f"{Fore.RED}Error getting sheet ID: {str(e)}{Style.RESET_ALL}")
        raise


def share_sheet(sheet_id, email):
    """Share the Google Sheet with a specific email address."""
    try:
        permission = (
            drive_service.permissions()
            .create(
                fileId=sheet_id,
                body={"type": "user", "role": "writer", "emailAddress": email},
                fields="id",
                sendNotificationEmail=False,
            )
            .execute()
        )
        logger.info(f"{Fore.GREEN}Sheet shared successfully with {email}. Permission ID: {permission.get('id')}{Style.RESET_ALL}")
    except Exception as e:
        logger.error(f"{Fore.RED}Error sharing sheet: {str(e)}{Style.RESET_ALL}")


def update_to_sheet(df, output_cols, sheet_id, sheet_name):
    """
    Update specific output columns in the Google Sheet based on col_ref.
    col_ref=1 corresponds to row 21 in the sheet (20-row offset).

    Args:
        df (pandas.DataFrame): DataFrame containing the output data to update
        output_cols (list): List of column names to update in the sheet
        sheet_id (str): ID of the Google Sheet
        sheet_name (str): Name of the sheet to update
    """
    try:
        # Get sheet information to check if it exists
        sheet = service.spreadsheets()
        sheet_metadata = sheet.get(spreadsheetId=sheet_id).execute()

        # Get the current values from the sheet to preserve existing data
        dynamic_range = get_dynamic_range(sheet_id, sheet_name)
        result = sheet.values().get(spreadsheetId=sheet_id, range=dynamic_range).execute()
        current_values = result.get("values", [])

        if not current_values:
            logger.error(f"{Fore.RED}No data found in sheet '{sheet_name}'. Nothing to update.{Style.RESET_ALL}")
            return

        # Get headers from the first row
        headers = current_values[0]
        logger.debug(f"Sheet headers: {headers}")

        # Map column names to indices
        column_indices = {}
        for col in output_cols:
            if col in headers:
                column_indices[col] = headers.index(col)
                logger.debug(f"Found column '{col}' at index {column_indices[col]}")
            else:
                logger.warning(f"{Fore.YELLOW}Column '{col}' not found in sheet headers. Skipping.{Style.RESET_ALL}")

        # Make sure col_ref is in the DataFrame
        if "col_ref" not in df.columns:
            logger.error(f"{Fore.RED}col_ref column not found in DataFrame. Cannot determine which rows to update.{Style.RESET_ALL}")
            return

        # Log summary of what we're processing
        logger.info(f"{Fore.CYAN}Processing {len(df)} rows for update with columns: {list(column_indices.keys())}{Style.RESET_ALL}")

        # Process updates based on col_ref
        update_rows = []
        for idx, row in df.iterrows():
            # Get col_ref value to determine which row to update
            col_ref_value = row.get("col_ref")

            # Skip if col_ref is missing or invalid
            if col_ref_value is None or pd.isna(col_ref_value):
                logger.warning(f"{Fore.YELLOW}Skipping row {idx}: Missing or invalid col_ref.{Style.RESET_ALL}")
                continue

            try:
                # col_ref should be a number representing the row to update
                if isinstance(col_ref_value, pd.Series):
                    # If it's a Series (which it shouldn't be), take the first value
                    row_ref = int(col_ref_value.iloc[0])
                else:
                    row_ref = int(col_ref_value)

                # Apply 20-row offset (col_ref=1 corresponds to row 21)
                sheet_row = row_ref + 20

                # Log what row we're updating
                logger.info(f"{Fore.CYAN}Checking row {sheet_row} in sheet (col_ref={row_ref}){Style.RESET_ALL}")

                # Count how many cells we'll update for this row
                cells_to_update = 0

                # Prepare row data for each output column
                for col in output_cols:
                    if col in column_indices and col in df.columns:
                        col_idx = column_indices[col]

                        # Get column letter (A, B, C, etc.) using the new function
                        col_letter = index_to_column_letter(col_idx)

                        # Get cell value safely
                        cell_value = row.get(col)

                        # Skip empty values to avoid overwriting existing content with blanks
                        if cell_value is None or pd.isna(cell_value):
                            logger.debug(f"Skipping None/NA value for {col_letter}{sheet_row}")
                            continue

                        # Convert to string and check if empty
                        if isinstance(cell_value, str) and cell_value.strip() == "":
                            logger.debug(f"Skipping empty string value for {col_letter}{sheet_row}")
                            continue

                        # Handle different value types
                        if isinstance(cell_value, pd.Series):
                            value = str(cell_value.iloc[0]) if not cell_value.empty else ""
                        else:
                            value = str(cell_value)

                        # Check value again after conversion
                        if not value or value.strip() == "" or value.strip() == "<generate>":
                            logger.debug(f"Skipping empty or <generate> value for {col_letter}{sheet_row}")
                            continue

                        # Do a final check before adding to updates
                        if pd.notna(value) and str(value).strip() != "" and str(value).strip() != "<generate>":
                            # Log what we're updating
                            logger.debug(f"Will update {col_letter}{sheet_row} to: '{value}'")
                            cells_to_update += 1

                            # Add to update batch - target specific cell using col_ref + offset as row number
                            update_rows.append(
                                {
                                    "range": f"{sheet_name}!{col_letter}{sheet_row}",
                                    "values": [[value]],
                                }
                            )
                        else:
                            logger.debug(f"Final check failed for {col_letter}{sheet_row}: '{value}'")

                if cells_to_update == 0:
                    logger.debug(f"No cells to update for row {sheet_row} (col_ref={row_ref})")
                else:
                    logger.debug(f"Will update {cells_to_update} cells for row {sheet_row} (col_ref={row_ref})")

            except (ValueError, TypeError) as e:
                logger.warning(f"{Fore.YELLOW}Skipping row {idx}: Invalid col_ref format: {col_ref_value}. Error: {str(e)}{Style.RESET_ALL}")
                continue

        # Execute batch update if we have rows to update
        if update_rows:
            logger.info(f"{Fore.CYAN}Total updates: {len(update_rows)} cells across {len(df)} rows{Style.RESET_ALL}")

            # Batch updates in groups of 100 to avoid API limits
            BATCH_SIZE = 100
            total_updated = 0

            for i in range(0, len(update_rows), BATCH_SIZE):
                batch = update_rows[i : i + BATCH_SIZE]
                body = {"valueInputOption": "RAW", "data": batch}

                batch_num = i // BATCH_SIZE + 1
                total_batches = (len(update_rows) - 1) // BATCH_SIZE + 1
                logger.info(
                    f"{Fore.GREEN}Sending batch of {len(batch)} cell updates to Google Sheets (batch {batch_num}/{total_batches}){Style.RESET_ALL}"
                )
                # Execute the batch update (response not needed)
                sheet.values().batchUpdate(spreadsheetId=sheet_id, body=body).execute()
                total_updated += len(batch)

                # Small delay to avoid rate limiting if needed
                # import time
                # time.sleep(0.5)

            logger.info(f"{Fore.GREEN}Successfully updated {total_updated} cells in sheet '{sheet_name}'.{Style.RESET_ALL}")
        else:
            logger.warning(f"{Fore.YELLOW}No valid updates to make in sheet '{sheet_name}'.{Style.RESET_ALL}")

    except Exception as e:
        logger.error(f"{Fore.RED}Error updating sheet: {str(e)}{Style.RESET_ALL}")
        logger.error(f"{Fore.RED}Error details: {type(e).__name__}{Style.RESET_ALL}")
        import traceback

        logger.error(f"{Fore.RED}Traceback: {traceback.format_exc()}{Style.RESET_ALL}")
        raise


def verify_sharing(sheet_id, email):
    """Verify if the sheet is shared with the specified email."""
    try:
        permissions = drive_service.permissions().list(fileId=sheet_id).execute()
        for permission in permissions.get("permissions", []):
            if permission.get("emailAddress", "").lower() == email.lower():
                logger.info(f"{Fore.GREEN}Sharing verified: Sheet is shared with {email}{Style.RESET_ALL}")
                return True

        # If not found, try sharing again
        logger.info(f"{Fore.YELLOW}Sharing not found, attempting to share again...{Style.RESET_ALL}")
        share_sheet(sheet_id, email)
        return True

    except Exception as e:
        logger.error(f"{Fore.RED}Error verifying sharing: {str(e)}{Style.RESET_ALL}")
        return False


def get_sheet_id_from_url(url):
    """Extract the sheet ID from a Google Sheets URL."""
    try:
        return url.split("/spreadsheets/d/")[1].split("/")[0]
    except IndexError:
        logger.error(f"{Fore.RED}Error: Invalid Google Sheets URL{Style.RESET_ALL}")
        return None


def get_sheet_ranges(sheet_id):
    """Get the available ranges in a Google Sheet."""
    sheet = service.spreadsheets()
    sheet_metadata = sheet.get(spreadsheetId=sheet_id).execute()
    return [sheet_info.get("properties", {}).get("title") for sheet_info in sheet_metadata.get("sheets", [])]


def index_to_column_letter(idx):
    """
    Convert a 0-based column index to Excel-style column letter (A, B, C, ..., Z, AA, AB, ..., ZZ, AAA, etc.)

    Args:
        idx: 0-based column index

    Returns:
        Excel-style column letter
    """
    result = ""
    while True:
        idx, remainder = divmod(idx, 26)
        result = chr(65 + remainder) + result
        if idx == 0:
            break
        idx -= 1  # Adjust for the fact that Excel uses 1-based indexing for the conversion
    return result


def get_dynamic_range(sheet_id, sheet_name):
    """
    Determine the dynamic range for a sheet based on its dimensions.

    Args:
        sheet_id: ID of the Google Sheet
        sheet_name: Name of the sheet

    Returns:
        A range string in the format 'SheetName!A1:XX' where XX is determined by the sheet's dimensions
    """
    try:
        sheet = service.spreadsheets()
        sheet_metadata = sheet.get(spreadsheetId=sheet_id).execute()

        # Find the specified sheet
        sheet_properties = None
        for s in sheet_metadata.get("sheets", []):
            if s.get("properties", {}).get("title") == sheet_name:
                sheet_properties = s.get("properties", {})
                break

        if not sheet_properties:
            logger.warning(f"{Fore.YELLOW}Sheet '{sheet_name}' not found. Using default range A1:ZZ.{Style.RESET_ALL}")
            return f"{sheet_name}!A1:ZZ"

        # Get the column count from the sheet properties
        column_count = sheet_properties.get("gridProperties", {}).get("columnCount", 26)

        # Ensure we have at least 26 columns (A-Z)
        column_count = max(column_count, 26)

        # Convert the column count to a column letter (0-based index)
        max_column_letter = index_to_column_letter(column_count - 1)

        # Return the dynamic range
        dynamic_range = f"{sheet_name}!A1:{max_column_letter}"
        logger.info(f"{Fore.CYAN}Dynamic range determined: {dynamic_range} (based on {column_count} columns){Style.RESET_ALL}")

        return dynamic_range

    except Exception as e:
        logger.warning(f"{Fore.YELLOW}Error determining dynamic range: {str(e)}. Using default range A1:ZZ.{Style.RESET_ALL}")
        return f"{sheet_name}!A1:ZZ"


def get_data_from_sheet(sheet_id, range_name="Sheet1"):
    """
    Pull data from an existing Google Sheet and return it as a pandas DataFrame.
    Reads data starting from row 5 as header.
    """
    try:
        sheet = service.spreadsheets()
        # Use dynamic range determination
        range = get_dynamic_range(sheet_id, range_name)
        result = sheet.values().get(spreadsheetId=sheet_id, range=range).execute()

        values = result.get("values", [])

        if not values:
            logger.warning(f"{Fore.YELLOW}No data found in the specified range.{Style.RESET_ALL}")
            return pd.DataFrame()

        headers = values[0]

        # Convert to DataFrame, using data after header row
        if len(values) > 1:
            # Ensure all rows have same number of columns as header
            data = []
            for row in values[1:]:
                # Pad row with empty strings if needed
                padded_row = row + [""] * (len(headers) - len(row))
                data.append(padded_row[: len(headers)])  # Truncate if too long
            df = pd.DataFrame(data, columns=headers)
        else:
            df = pd.DataFrame(columns=headers)

        logger.info(f"{Fore.GREEN}Successfully retrieved data from Google Sheet.{Style.RESET_ALL}")
        logger.info(f"{Fore.CYAN}Found {len(df)} rows of data.{Style.RESET_ALL}")

        return df

    except Exception:
        logger.error(f"{Fore.RED}Error retrieving data from Google Sheet{Style.RESET_ALL}")
        return pd.DataFrame()
