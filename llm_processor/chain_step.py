# Contains the ChainStep class

"""
Contains the ChainStep class for defining steps in an LLM processing chain.

This module defines a NamedTuple class `ChainStep` that represents a single step
in a chain of LLM processing operations. It encapsulates various parameters
needed for executing an LLM query, such as the prompt template, model settings,
and output specifications.

Classes:
    ChainStep: A NamedTuple class representing a step in an LLM processing chain.
"""

from typing import NamedTuple, Optional, Dict, List


class ChainStep(NamedTuple):
    """
    Represents a single step in an LLM processing chain.

    This class defines the structure and parameters for each step in the chain,
    including the prompt template, model settings, and output specifications.

    Attributes:
        pt (str): The prompt template for this step.
        mapping (Optional[Dict[str, str]]): A mapping of prompt keywords to column names.
        temperature (Optional[float]): The temperature setting for the LLM model.
        max_tokens (Optional[int]): The maximum number of tokens for the LLM response.
        model (Optional[str]): The name of the LLM model to use.
        col (str): The name of the column to store the response.
        fanout (bool): Whether to fan out the response as a list.
        overwrite (bool): Whether to overwrite existing data in the response column.
        validators (List[LLMValidator]): A list of LLM validators to apply to the response.
        reasoning_effort (Optional[str]): The reasoning effort setting for models that support it.
                                         Options: "low", "medium", "high". Controls what percentage
                                         of tokens are reserved for thinking vs. completion.
    """

    pt: str  # prompt template
    mapping: Optional[Dict[str, str]] = None  # prompt keyword mapping
    temperature: Optional[float] = None  # model temperature
    max_tokens: Optional[int] = None  # model max_tokens
    model: Optional[str] = None  # model name
    col: str = "response"  # response column name
    fanout: bool = False  # fanout response column as a list TODO: ensure all cols's new rows are dropped down. TODO: how to un-fan?
    unfan_col: Optional[str] = None  # if fanout, specify the column to un-fan #TODO - THIS IS NOT FUNCTIONAL, SEE NOTE BELOW
    overwrite: bool = False  # overwrite existing response column
    validators: List = None  # LLM validators to apply to the response
    reasoning_effort: Optional[str] = None  # reasoning effort for models that support it (e.g., "low", "medium", "high")


# TODO: ENSEMBLE FLAG PARAMETER
"""
@dataclass
class EnsembleConfig:
    models: List[str] = field(default_factory=lambda: [LLM.gpt_4o_mini, LLM.claude_3_5_sonnet, LLM.gpt_3_5_turbo])
    temperatures: List[float] = field(default_factory=lambda: [0.2, 0.5, 0.8])
    scoring_model: str = LLM.gpt_4o_mini
    scoring_temperature: float = 0.1
    scoring_max_tokens: int = 10

We'd want a config class so we can use ensembling naively. Maybe a few core types set up.
Our chaining is essentially making a list of initialised ChainSteps, which we then execute in order.
Which col the ensembles cols were nested under would have to be specified in the col name. Maybe better way like a df that just describes the relationships betweeen cols.
Ideally, we would then have dependencies detected and parellisation optimized. Could potentially then even automate the ordering... just a set of steps with dependencies and it figures the rest out.
"""

# -------------------------------------------------------------------------------------------------

# TODO: UNFANNING
"""
The `unfan_col` parameter in the `ChainStep` class is designed to support a "unfanning" operation after a fanout has occurred. Here's how it would work:

1. When a step has `fanout=True`, it creates multiple rows from a single input row, each with a different value in the specified column.

2. The `unfan_col` parameter specifies which column should be used to collapse these multiple rows back into a single row.

3. This unfanning operation would typically be performed after the fanout step, in a subsequent step of the chain.

Here's a conceptual example of how this might work:

```python
chain = [
    ChainStep(pt="Generate 3 ideas for {topic}", fanout=True, col="ideas"),
    ChainStep(pt="Elaborate on idea: {ideas}", col="elaborations"),
    ChainStep(pt="Summarize elaborations: {elaborations}", unfan_col="topic", col="summary") # <----------------------- What if there are multiple PT params? will it group each into each?
]
```

In this chain:

1. The first step generates multiple ideas, creating multiple rows.
2. The second step elaborates on each idea.
3. The third step summarizes the elaborations and "unfans" the results back to a single row per original topic.

To implement this functionality, you would need to modify the `_process_chain` method in the `ParallelLLMDataFrameProcessor` class. Here's a sketch of how you might do this:

```python
async def _process_chain(self, input_row: pd.Series, chain: List[ChainStep], max_attempts: int) -> List[pd.Series]:
    c_rows = [input_row.copy()]

    for step in chain:
        # ... existing code for processing the step ...

        if step.fanout:
            df = pd.DataFrame(c_rows)
            df = df.explode(column=step.col)
            c_rows = [r for _, r in df.iterrows()]
        elif step.unfan_col:
            df = pd.DataFrame(c_rows)
            grouped = df.groupby(step.unfan_col).agg({
                col: lambda x: list(x) if col != step.unfan_col else 'first' for col in df.columns
            })
            c_rows = [row for _, row in grouped.iterrows()]

    return c_rows
```

This implementation would:

1. Perform the fanout operation as before when `step.fanout` is True.
2. When `step.unfan_col` is specified, it groups the rows by that column and aggregates the other columns into lists.

Note that this is a basic implementation and might need refinement depending on your specific use case. For example, you might want to customize how different columns are aggregated during the unfanning process.

To fully implement this feature, you would also need to:

1. Update the `ChainStep` class to include the `unfan_col` parameter.
2. Modify the `execute_chain` method to handle the potentially changed structure of the result DataFrame.
3. Consider how to handle cases where fanout and unfan operations are not perfectly balanced in a chain.

Remember that this kind of operation can be complex and may have implications for how data is structured and processed throughout your chain. It's important to carefully consider the data flow and potential edge cases when implementing such a feature.
"""


# -------------------------------------------------------------------------------------------------


# TODO: DEPENDENCY DETECTION GRAPH & AUTOMATED OPTIMISATION
"""
We can do this by:
1. Parsing the prompt template to extract the variables (currenly done by the processor)
2. Building a directed graph of the dependencies between the variables.
3. Using topological sorting to determine the order of the steps.
4. Optimizing the parallel execution of the steps.

This approach offers several advantages:
1. Maximum Parallelism: Tasks start as soon as their dependencies are met, not waiting for unrelated tasks.
2. Efficient Resource Use: The execution pool is always working on all available tasks.
3. Flexibility: It easily handles complex dependency graphs, not just level-based execution.
Scalability: Works well for both small and large numbers of tasks.
To integrate this with your existing ParallelLLMDataFrameProcessor:
Modify ChainStep to include a dependencies attribute.
In execute_chain, use this DependencyExecutor instead of sequential execution.
Adjust the run_step method to use your existing execution logic for individual steps.
This approach ensures that each step waits only for its specific dependencies, maximizing parallelism and efficiency in your chain execution.

o1-preview did a great job of upgrading the sonnet 1st pass.

# -------------------------------------------------------------------------------------------------

The code you've provided attempts to execute tasks with dependencies using `asyncio`, but it has several issues that may prevent it from working effectively at scale. Here's why:

1. **Unmanaged Tasks**: You're creating tasks with `asyncio.create_task` but not keeping references to them or properly awaiting their completion. This can lead to tasks running in the background without proper coordination, potentially causing resource exhaustion or unexpected behavior.

2. **Inefficient Waiting Mechanism**: Using `await asyncio.sleep(0.1)` to prevent busy-waiting is inefficient and can lead to unnecessary delays, especially at scale. It's better to use `asyncio` primitives designed for synchronization, such as `asyncio.Event`, `asyncio.Queue`, or `asyncio.gather`.

3. **No Concurrency Control**: There's no mechanism to limit the number of concurrent tasks. If many tasks become ready simultaneously, the code could start a large number of tasks at once, overwhelming system resources.

4. **Race Conditions**: Modifying shared data structures like `self.in_progress` and `self.completed` without proper synchronization can lead to race conditions in an asynchronous environment.

5. **Inefficient Dependency Checking**: Continuously checking for completed dependencies can be inefficient at scale. A more efficient approach would be to trigger dependent tasks as soon as their dependencies are completed.

### Why Asyncio Sometimes "Trips Over Itself"

Asynchronous programming with `asyncio` can be tricky because it requires careful management of tasks and understanding of how the event loop works. Common pitfalls include:

- **Not Awaiting Tasks**: If you don't `await` a coroutine or task, it won't run as expected.
- **Improper Synchronization**: Failing to use synchronization primitives can lead to race conditions or deadlocks.
- **Overloading the Event Loop**: Starting too many tasks at once can overwhelm the event loop, causing performance degradation.

### Improved Code Example

Here's a revised version of your code that addresses these issues by using `asyncio.TaskGroup` (available in Python 3.11 and above) for better task management and `asyncio.Semaphore` to control concurrency:

```python
import asyncio
from collections import deque

class DependencyExecutor:
    def __init__(self, chain_steps, max_concurrency=10):
        self.steps = {step.col: step for step in chain_steps}
        self.dependencies = {step.col: set(step.dependencies) for step in chain_steps}
        self.dependents = self._build_dependents()
        self.ready_queue = deque()
        self.in_progress = set()
        self.completed = set()
        self.max_concurrency = max_concurrency
        self.semaphore = asyncio.Semaphore(max_concurrency)

    def _build_dependents(self):
        dependents = {step: set() for step in self.steps}
        for step, deps in self.dependencies.items():
            for dep in deps:
                dependents[dep].add(step)
        return dependents

    def initialize_queue(self):
        for step, deps in self.dependencies.items():
            if not deps:
                self.ready_queue.append(step)

    async def execute(self):
        self.initialize_queue()
        async with asyncio.TaskGroup() as task_group:
            while self.ready_queue or self.in_progress:
                # Start ready tasks up to the concurrency limit
                while self.ready_queue and len(self.in_progress) < self.max_concurrency:
                    step = self.ready_queue.popleft()
                    self.in_progress.add(step)
                    task_group.create_task(self.run_step(step))
                # Wait for any task to complete
                await asyncio.sleep(0)  # Yield control to allow tasks to progress

    async def run_step(self, step):
        async with self.semaphore:
            await self.steps[step].execute()
            self.in_progress.remove(step)
            self.completed.add(step)
            # Trigger dependents
            for dependent in self.dependents[step]:
                if dependent not in self.completed and self.dependencies[dependent].issubset(self.completed):
                    self.ready_queue.append(dependent)

async def main(chain_steps):
    executor = DependencyExecutor(chain_steps)
    await executor.execute()

# Usage
chain_steps = [...]  # Your ChainStep objects
asyncio.run(main(chain_steps))
```

**Key Improvements:**

- **Task Management**: Using `asyncio.TaskGroup` ensures that all tasks are properly awaited and managed.
- **Concurrency Control**: An `asyncio.Semaphore` limits the number of concurrent tasks to prevent resource exhaustion.
- **Efficient Waiting**: Replacing `await asyncio.sleep(0.1)` with `await asyncio.sleep(0)` yields control to the event loop more efficiently.
- **Proper Synchronization**: By managing tasks within a `TaskGroup` and using a semaphore, we reduce the risk of race conditions.

### Conclusion

The original code may not work effectively at scale due to improper task management, lack of concurrency control, and inefficient synchronization. By restructuring the code to use `asyncio`'s built-in primitives for task management and synchronization, you can create a more robust and scalable solution.

**Recommendations:**

- **Always Keep References to Tasks**: This allows you to manage and await them properly.
- **Use Synchronization Primitives**: Utilize `asyncio.Event`, `asyncio.Lock`, or `asyncio.Semaphore` to manage shared resources and concurrency.
- **Limit Concurrency**: Prevent overwhelming the event loop by controlling the number of concurrent tasks.
- **Avoid Sleeping in Event Loop**: Instead of using `asyncio.sleep` for synchronization, rely on `asyncio` mechanisms designed for that purpose.

By following these best practices, you can make asynchronous code more reliable and scalable.

# -------------------------------------------------------------------------------------------------

The next bit ignores the step of extracting dependencies from PTs and assumes you've done that. I think.

# Assuming you have a list of ChainStep objects with 'col' and 'dependencies' attributes.

```python
chain_steps = [
    # ChainStep instances with their respective 'col' identifiers and 'dependencies' lists.
    # For example:
    # ChainStep(col='A', dependencies=[]),
    # ChainStep(col='B', dependencies=['A']),
    # ChainStep(col='C', dependencies=['A']),
    # ChainStep(col='D', dependencies=['B', 'C']),
    # ChainStep(col='E', dependencies=['D']),
]
asyncio.run(main(chain_steps))
```

```python
import asyncio
from collections import deque

class DependencyExecutor:
    '''
    Executes tasks with dependencies using a dependency graph for maximum optimization.

    Steps:
    1. Build the dependency graph by mapping tasks to their dependencies and dependents.
       1.1. Map each task to its dependencies.
       1.2. Build an inverse mapping from tasks to their dependents.
    2. Initialize the ready queue with tasks that have no dependencies.
    3. Start executing tasks while respecting the concurrency limit.
       3.1. Start ready tasks up to the concurrency limit.
       3.2. Yield control to allow tasks to progress.
    4. Upon task completion, update the completed tasks and trigger dependent tasks.
       4.1. Mark the task as completed and remove it from in-progress.
       4.2. For each dependent, check if it can now be scheduled.
    5. Continue until all tasks are completed.

    Usage:
    - Create an instance of DependencyExecutor with your tasks.
    - Call the execute() method to start execution.
    '''

    def __init__(self, chain_steps, max_concurrency=10):
        # Step 1: Build the dependency graph by mapping tasks to their dependencies and dependents.

        # Step 1.1: Map each task to its dependencies.
        self.steps = {step.col: step for step in chain_steps}  # Map task identifiers to ChainStep objects.
        self.dependencies = {
            step.col: set(step.dependencies) for step in chain_steps
        }  # Map each task to the set of tasks it depends on.

        # Step 1.2: Build an inverse mapping from tasks to their dependents.
        self.dependents = self._build_dependents()  # Map each task to the set of tasks that depend on it.

        # Initialize task status tracking structures.
        self.ready_queue = deque()  # Queue of tasks that are ready to execute (dependencies satisfied).
        self.in_progress = set()  # Set of tasks that are currently being executed.
        self.completed = set()  # Set of tasks that have been completed.

        # Concurrency control.
        self.max_concurrency = max_concurrency  # Maximum number of tasks to run concurrently.
        self.semaphore = asyncio.Semaphore(max_concurrency)  # Semaphore to limit the number of concurrent tasks.

    def _build_dependents(self):
        # Step 1.2: Build an inverse mapping from tasks to their dependents.

        # Initialize the dependents mapping with empty sets for each task.
        dependents = {step: set() for step in self.steps}

        # For each task, get its dependencies.
        for step, deps in self.dependencies.items():
            # For each dependency 'dep' of the task 'step':
            for dep in deps:
                # Add 'step' to the set of dependents of 'dep'.
                # This builds the mapping of which tasks depend on 'dep'.
                dependents[dep].add(step)
        return dependents

    def initialize_queue(self):
        # Step 2: Initialize the ready queue with tasks that have no dependencies.

        # Iterate over all tasks and their dependencies.
        for step, deps in self.dependencies.items():
            if not deps:
                # If the task has no dependencies, it is ready to execute.
                self.ready_queue.append(step)

    async def execute(self):
        # Step 3: Start executing tasks while respecting the concurrency limit.

        self.initialize_queue()  # Initialize the ready queue with tasks that have no dependencies.

        # Use an asyncio TaskGroup to manage asynchronous task execution.
        async with asyncio.TaskGroup() as task_group:
            # Continue executing as long as there are tasks ready to execute or tasks in progress.
            while self.ready_queue or self.in_progress:
                # Step 3.1: Start ready tasks up to the concurrency limit.

                # While we have tasks ready to execute and haven't reached the concurrency limit:
                while self.ready_queue and len(self.in_progress) < self.max_concurrency:
                    step = self.ready_queue.popleft()  # Get the next task to execute.
                    self.in_progress.add(step)  # Mark the task as in progress.

                    # Schedule the task for execution.
                    # The task will execute asynchronously and upon completion will update the dependency graph.
                    task_group.create_task(self.run_step(step))

                # Step 3.2: Yield control to allow tasks to progress.

                # Since we might not have any tasks ready to start, or we might have reached the concurrency limit,
                # we yield control to the event loop to allow the currently running tasks to proceed.
                await asyncio.sleep(0)  # This effectively yields control without adding unnecessary delay.

    async def run_step(self, step):
        # Step 4: Upon task completion, update the completed tasks and trigger dependent tasks.

        async with self.semaphore:
            # Execute the task associated with 'step'.
            # Here, we await the asynchronous execution of the task.
            await self.steps[step].execute()

            # Step 4.1: Mark the task as completed and remove it from in-progress.
            self.in_progress.remove(step)  # Task is no longer in progress.
            self.completed.add(step)  # Task is now completed.

            # Step 4.2: For each dependent, check if it can now be scheduled.

            # Retrieve the set of tasks that depend on this completed task.
            for dependent in self.dependents.get(step, []):
                # If the dependent task is not already completed or in progress:
                if dependent not in self.completed and dependent not in self.in_progress:
                    # Check if all dependencies of the dependent task are now completed.
                    if self.dependencies[dependent].issubset(self.completed):
                        # All dependencies are satisfied; the dependent task can be scheduled.
                        self.ready_queue.append(dependent)  # Add the dependent task to the ready queue.

async def main(chain_steps):
    executor = DependencyExecutor(chain_steps)
    await executor.execute()

# Usage
chain_steps = [...]  # Your ChainStep objects
asyncio.run(main(chain_steps))
```

**Explanation of the Code with Dependency Graph Walkthrough:**

- **Step 1: Building the Dependency Graph**

  - **1.1 Mapping Tasks to Dependencies:**
    - We create a dictionary `self.steps` that maps each task identifier (`step.col`) to its corresponding `ChainStep` object.
    - We create another dictionary `self.dependencies` that maps each task to the set of tasks it depends on. This represents the edges in the dependency graph pointing from dependencies to the task.

  - **1.2 Building the Inverse Mapping to Dependents:**
    - We build `self.dependents`, an inverse mapping that tells us, for each task, which tasks depend on it.
    - For each task, we iterate over its dependencies and add the task to the dependents list of each dependency.
    - This mapping allows us to efficiently find which tasks might become ready to execute when a task completes.

- **Step 2: Initializing the Ready Queue**

  - We iterate over all tasks and check if they have any dependencies.
  - Tasks with no dependencies are added to the `self.ready_queue` because they can be executed immediately.
  - This queue represents tasks that are ready to run without waiting for other tasks to complete.

- **Step 3: Executing Tasks with Concurrency Control**

  - **3.1 Starting Ready Tasks:**
    - While there are tasks in the `ready_queue` and we haven't reached the `max_concurrency` limit, we start tasks.
    - We dequeue a task from the `ready_queue`, mark it as `in_progress`, and schedule it for execution using `task_group.create_task(self.run_step(step))`.
    - This step ensures that we are utilizing available concurrency to execute independent tasks in parallel.

  - **3.2 Yielding Control:**
    - After scheduling tasks, we use `await asyncio.sleep(0)` to yield control to the event loop.
    - This allows the event loop to run scheduled tasks and handle any awaiting tasks.
    - It ensures that our scheduler doesn't block the event loop and that tasks can progress concurrently.

- **Step 4: Handling Task Completion**

  - **4.1 Updating Task Status:**
    - When a task completes its execution in `run_step`, we remove it from `in_progress` and add it to `completed`.
    - This updates our tracking structures to reflect that the task has finished.

  - **4.2 Triggering Dependent Tasks:**
    - We then look up all tasks that depend on the just-completed task using `self.dependents.get(step, [])`.
    - For each dependent, we check if all its dependencies are now satisfied by verifying if `self.dependencies[dependent].issubset(self.completed)`.
    - If all dependencies are satisfied, we add the dependent task to the `ready_queue` to be scheduled for execution.
    - This mechanism ensures that tasks are executed as soon as they are ready, maximizing concurrency and efficiency.

- **Step 5: Continuation Until Completion**

  - The execution loop in `execute` continues until there are no tasks left in `ready_queue` or `in_progress`.
  - This means all tasks have been scheduled and completed, respecting their dependencies.

**Key Functionalities Explained:**

- **Dependency Tracking:**
  - By maintaining `self.dependencies` and `self.dependents`, we efficiently track the relationships between tasks.
  - This allows us to determine when a task is ready to execute based on the completion of its dependencies.

- **Concurrency Control:**
  - The `asyncio.Semaphore` ensures that we don't exceed the `max_concurrency` limit, preventing resource exhaustion.
  - The semaphore is acquired when a task starts and released upon its completion.

- **Task Scheduling:**
  - The use of `asyncio.TaskGroup` allows us to manage multiple asynchronous tasks concurrently.
  - Tasks are scheduled as soon as their dependencies are met, maximizing the utilization of resources.

- **Efficient Event Loop Usage:**
  - The use of `await asyncio.sleep(0)` ensures that the event loop remains responsive and that we yield control appropriately.
  - This prevents blocking other tasks and allows for efficient asynchronous execution.

**Usage Example:**

```python
# Assuming you have a list of ChainStep objects with 'col' and 'dependencies' attributes.
chain_steps = [
    # ChainStep instances with their respective 'col' identifiers and 'dependencies' lists.
    # For example:
    # ChainStep(col='A', dependencies=[]),
    # ChainStep(col='B', dependencies=['A']),
    # ChainStep(col='C', dependencies=['A']),
    # ChainStep(col='D', dependencies=['B', 'C']),
    # ChainStep(col='E', dependencies=['D']),
]

asyncio.run(main(chain_steps))
```

This code sets up and runs the `DependencyExecutor`, executing tasks while respecting their dependencies and optimizing for maximum concurrency where possible.

**Note:** Replace the `chain_steps` list with your actual `ChainStep` objects that have the necessary `execute` method and attributes.

---

Feel free to let me know if you have any questions or need further clarification on any part of the code!


"""
