"""
Configuration settings for the LLM processor.

This module contains the (very) default values and configurations used throughout
the LLM processor package. These should be overriden during instantiation.
"""

from .models import LLM

# Default model to use if not specified
DEFAULT_MODEL = LLM.gpt_4o_mini

# Default temperature for LLM queries
DEFAULT_TEMPERATURE = 0.0

# Default maximum number of tokens for LLM responses
DEFAULT_MAX_TOKENS = 1024

# Default rate limit for async operations (requests per second)
DEFAULT_ASYNC_RATE_LIMIT = 10

# Default rate limit for threaded operations (requests per second)
DEFAULT_THREAD_RATE_LIMIT = 5


class Config:
    # ... existing code ...
    INPUT_DIR = "_input"  # Change to "__input"
    OUTPUT_DIR = "_output"  # Change to "__output"
    # ... existing code ...
