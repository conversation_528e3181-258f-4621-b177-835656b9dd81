# run.py
# Import the main function from demo.main at runtime to avoid circular imports
import argparse
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from typing import List

from main_config import CFG
from demo.main import main

console = Console()

# New import within try/except to handle dependency gracefully
try:
    import questionary  # type: ignore
except ModuleNotFoundError:  # pragma: no cover – will be handled by runtime install
    questionary = None  # type: ignore

# Google Sheets helper imports (lazy-loaded in helper functions to avoid circular deps)


# -----------------------------------------------------------------------------
# Helper functions
# -----------------------------------------------------------------------------


def _get_available_sheets() -> List[str]:
    """Return a list of sheet names for the configured source.

    * If PROC_GSHEET is True – fetch via Google Sheets API.
    * Else – inspect the local Excel file.
    """
    if CFG.PROC_GSHEET:
        # Lazy import to avoid unnecessary dependency chain when not needed
        from llm_processor.google_sheets_utils import get_sheet_id_from_url, get_sheet_ranges  # noqa: WPS433

        sheet_id = get_sheet_id_from_url(CFG.GSHEET_URL)
        if not sheet_id:
            console.print("[red]Unable to parse sheet id from URL – falling back to default sheet name.[/red]")
            return [CFG.SHEET_NAME]
        return get_sheet_ranges(sheet_id)

    # Excel fallback
    try:
        import pandas as pd  # noqa: WPS433 – optional heavy import

        excel_file = pd.ExcelFile(CFG.EXCEL_PATH)
        return excel_file.sheet_names
    except Exception as exc:  # pragma: no cover – runtime diagnostics
        console.print(f"[red]Failed to list sheets from Excel: {exc}[/red]")
        return [CFG.SHEET_NAME]


def _interactive_select_sheet() -> None:
    """Prompt the user to choose a sheet if `questionary` is available."""
    if questionary is None:
        console.print("[yellow]questionary not installed – using default sheet name.[/yellow]")
        return

    sheets = _get_available_sheets()
    if not sheets:
        console.print("[red]No sheets found – using default sheet name.[/red]")
        return

    selected = (
        questionary.select(
            "Select sheet to process:",
            choices=sheets,
            default=CFG.SHEET_NAME,
        ).unsafe_ask()  # noqa: WPS437 – need raw ask to support arrow keys
    )

    if selected:
        CFG.SHEET_NAME = str(selected)


def display_sheet_name_prominently():
    """prevents sheet-name config stupidity, visually"""
    sheet_name_text = Text("Sheet: " + CFG.SHEET_NAME, style="bold blue")
    panel = Panel(sheet_name_text, title="Processing Sheet", border_style="blue")
    console.print("\n")
    console.print(panel)
    console.print("\n")


def parse_cli_args() -> bool:
    """Parse CLI args and override config.

    Returns True if the sheet name was provided via CLI, else False.
    """
    parser = argparse.ArgumentParser(
        description="Process a specific sheet from the configured source",
        epilog="Example: uv run run.py --sheetname 'My Sheet Name'",
    )
    parser.add_argument(
        "--sheetname",
        "-s",
        type=str,
        help="Override the sheet name from config",
    )
    parser.add_argument(
        "--debug",
        "-d",
        action="store_true",
        help="Enable debug mode",
    )
    args = parser.parse_args()

    sheet_set = False
    if args.sheetname:
        CFG.SHEET_NAME = args.sheetname
        sheet_set = True
    if args.debug:
        CFG.DEBUG = True

    return sheet_set


def run() -> None:
    """Main entry point."""
    sheet_set_from_cli = parse_cli_args()

    if not sheet_set_from_cli:
        _interactive_select_sheet()

    display_sheet_name_prominently()
    main(CFG)


if __name__ == "__main__":
    # NOTE: Reminder to avoid circular imports.
    # All config in CFG and is used unless CLI args are provided.
    # NOTE: there is also Rishab's old CLI, the above agparse added by AF just to know exactly what is going on.
    run()
