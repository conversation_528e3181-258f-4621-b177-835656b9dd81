# Core CLI cheatsheet / Index for interacting with this repo in development.
# ↳ It's an upgrade to makefiles.
#
# USAGE: 'just <command>'
# E.g.,
#     just run                       # Run with interactive sheet selection (no quotes needed!)
#     just run a2                    # Run with specific sheet (-s)
#     just run a2 true               # Run with sheet + debug (-s -d)
#     just run "" true               # Run with debug only (-d)
#     just test                      # Run all tests
#     just test-verbose              # Run all tests with verbose output
#     just lint                      # Run ruff linter
#     just clean                     # Clean up cache and temporary files
#
# ADDING: If you are making a feature and want to quickly declare how someone can set it up / run it - add here.
# ↳ demark comment with your name until PR pushed e.g, # AF - feaure description.

# Set shell to zsh as requested in user rules
set shell := ["zsh", "-cu"]

# Default recipe - show available commands
default:
    @just --list

# Run script with optional sheet and debug parameters
run sheet="" debug="false":
    @if [ "{{sheet}}" != "" ] && [ "{{debug}}" = "true" ]; then \
        uv run run.py -s "{{sheet}}" -d; \
    elif [ "{{sheet}}" != "" ]; then \
        uv run run.py -s "{{sheet}}"; \
    elif [ "{{debug}}" = "true" ]; then \
        uv run run.py -d; \
    else \
        uv run run.py; \
    fi

# Setup dependencies
setup:
    uv sync --dev

# Sync dependencies
sync:
    uv sync --dev

# Run tests
test:
    @echo "Running all tests..."
    @source .venv/bin/activate && python demo/tests/run_tests.py

# Run tests with verbose output
test-verbose:
    @echo "Running all tests in verbose mode..."
    @source .venv/bin/activate && PYTHONVERBOSE=2 python demo/tests/run_tests.py

# Run linter
lint:
    @echo "Running linter..."
    uv run ruff check . --fix

# Clean up cache and temporary files
clean:
    @echo "Cleaning up cache and temporary files..."
    rm -rf __pycache__
    rm -rf .pytest_cache
    rm -rf demo/__pycache__
    rm -rf demo/tests/__pycache__
    find . -name '*.pyc' -delete
    find . -name '*.pyo' -delete
    find . -name '*.pyd' -delete
    find . -name '.DS_Store' -delete
    find . -name '__pycache__' -delete
    find . -name '.temp_*' -delete

# Run coverage tests
coverage:
    @echo "Running tests with coverage..."
    @source .venv/bin/activate && python scripts/run_coverage.py

# Install Git pre-commit hooks
setup-hooks:
    @echo "Setting up pre-commit hooks..."
    @source .venv/bin/activate && python scripts/setup_hooks.py 