"""
Main entry point for running the LLMProcessor with various configurations.
"""

import sys
import os
import time
import pandas as pd
from dotenv import load_dotenv
from loguru import logger
from colorama import Fore, Style, init
import multiprocessing as mp
import re
from pathlib import Path
import argparse

from loguru import logger as log
from rich.console import Console
from rich.traceback import install
from rich.table import Table
from rich.panel import Panel

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from llm_processor.processor import ParallelLLMDataFrameProcessor
from llm_processor.chain_step import ChainStep
from llm_processor.google_sheets_utils import (
    get_data_from_sheet,
    get_sheet_id_from_url,
    update_to_sheet,
)

# Initialize colorama
init(autoreset=True)
load_dotenv()


class CONFIG:
    LOG_LEVEL = "DEBUG"


# Configure Loguru
log.remove()  # Remove default handler
log.add(
    sys.stderr,
    format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>",
    level=CONFIG.LOG_LEVEL,
)


# Enable rich tracebacks with locals
install(show_locals=True)
console = Console()


max_workers = mp.cpu_count() - 1
DATA_DIR = Path("data")  # Directory to store local data files


def is_google_sheets_url(path):
    """
    Check if the provided path is a Google Sheets URL.

    Args:
        path: String path to check

    Returns:
        Boolean indicating if the path is a Google Sheets URL
    """
    return (
        isinstance(path, str)
        and path.startswith("https://")
        and "docs.google.com/spreadsheets" in path
    )


def fill_template(row, template, input_cols):
    """
    Fill template with values from a DataFrame row.

    Args:
        row: DataFrame row with input values
        template: String template with {variable} placeholders
        input_cols: List of column names to use for variable replacement

    Returns:
        Filled template with all variables replaced

    Raises:
        ValueError: If a required variable is not found in the input
    """
    # Extract all expected variables from the template
    expected_vars = set(re.findall(r"\{\{([^}]+)\}\}", template))
    missing_vars = []

    # Track which variables were actually replaced vs which are missing
    replaced_vars = []

    for col in input_cols:
        if col in row:
            # Skip if the value is NaN or empty
            if pd.isna(row[col]) or str(row[col]).strip() == "":
                missing_vars.append(col)
                continue

            if "{{" + col + "}}" in template:
                replaced_vars.append(col)
            template = template.replace("{{" + col + "}}", str(row[col]))
        elif "{{" + col + "}}" in template:
            # The column is referenced in template but doesn't exist in row
            missing_vars.append(col)
            template = template.replace("{{" + col + "}}", "")

    # Find any remaining template variables and note them as missing
    remaining_vars = re.findall(r"\{\{([^}]+)\}\}", template)
    for var in remaining_vars:
        if var not in missing_vars:
            missing_vars.append(var)
        template = template.replace("{{" + var + "}}", "")

    # Return template and metadata about what was replaced
    return {
        "filled_template": template,
        "expected_vars": expected_vars,
        "missing_vars": missing_vars,
        "replaced_vars": replaced_vars,
    }


def save_df_to_file(df, file_path):
    """
    Save DataFrame to file based on file extension.

    Args:
        df: DataFrame to save
        file_path: Path to save the file
    """
    try:
        # Convert to Path object if it's a string
        file_path = (
            Path(file_path) if isinstance(file_path, str) else file_path
        )

        # Create absolute path
        abs_path = file_path.absolute()

        # Log complete file path for debugging
        logger.info(
            f"{Fore.CYAN}Saving DataFrame to absolute path: {abs_path}{Style.RESET_ALL}"
        )

        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(abs_path), exist_ok=True)

        # Detect file type from extension and save appropriately
        file_path_str = str(abs_path).lower()
        if file_path_str.endswith(".csv"):
            # Save as CSV with UTF-8 encoding
            df.to_csv(abs_path, index=False, encoding="utf-8")
            logger.info(
                f"{Fore.GREEN}DataFrame saved to CSV: {abs_path}{Style.RESET_ALL}"
            )
        elif file_path_str.endswith((".xlsx", ".xls")):
            # Save as Excel
            df.to_excel(abs_path, index=False)
            logger.info(
                f"{Fore.GREEN}DataFrame saved to Excel: {abs_path}{Style.RESET_ALL}"
            )
        else:
            # Default to Excel if no extension or unrecognized
            new_path = str(abs_path) + ".xlsx"
            df.to_excel(new_path, index=False)
            logger.info(
                f"{Fore.GREEN}DataFrame saved to Excel (default): {new_path}{Style.RESET_ALL}"
            )

        # Verify file was created
        if os.path.exists(abs_path) or os.path.exists(
            new_path if "new_path" in locals() else abs_path
        ):
            logger.info(
                f"{Fore.GREEN}File save verified - file exists on disk{Style.RESET_ALL}"
            )
        else:
            logger.warning(
                f"{Fore.YELLOW}File may not have been saved correctly - cannot find file on disk{Style.RESET_ALL}"
            )

    except Exception as e:
        logger.error(
            f"{Fore.RED}Error saving DataFrame to {file_path}: {str(e)}{Style.RESET_ALL}"
        )
        import traceback

        logger.error(
            f"{Fore.RED}Traceback: {traceback.format_exc()}{Style.RESET_ALL}"
        )


def load_df_from_file(file_path):
    """
    Load DataFrame from file based on file extension.

    Args:
        file_path: Path to the file

    Returns:
        DataFrame loaded from the file or None if file doesn't exist or can't be read
    """
    try:
        # Convert to Path object if it's a string
        file_path = (
            Path(file_path) if isinstance(file_path, str) else file_path
        )

        # Create absolute path
        abs_path = file_path.absolute()

        # Log complete file path for debugging
        logger.info(
            f"{Fore.CYAN}Loading DataFrame from absolute path: {abs_path}{Style.RESET_ALL}"
        )

        if os.path.exists(abs_path):
            file_path_str = str(abs_path).lower()

            if file_path_str.endswith(".csv"):
                # Try to detect encoding and delimiter for CSV
                try:
                    df = pd.read_csv(abs_path)
                except UnicodeDecodeError:
                    # Try different encodings if default fails
                    df = pd.read_csv(abs_path, encoding="latin1")
                logger.info(
                    f"{Fore.GREEN}DataFrame loaded from CSV: {abs_path}{Style.RESET_ALL}"
                )
                return df

            elif file_path_str.endswith((".xlsx", ".xls")):
                df = pd.read_excel(abs_path)
                logger.info(
                    f"{Fore.GREEN}DataFrame loaded from Excel: {abs_path}{Style.RESET_ALL}"
                )
                return df

            else:
                logger.warning(
                    f"{Fore.YELLOW}Unrecognized file extension for {abs_path}. Trying Excel format.{Style.RESET_ALL}"
                )
                try:
                    df = pd.read_excel(abs_path)
                    logger.info(
                        f"{Fore.GREEN}Successfully loaded file as Excel: {abs_path}{Style.RESET_ALL}"
                    )
                    return df
                except:
                    try:
                        df = pd.read_csv(abs_path)
                        logger.info(
                            f"{Fore.GREEN}Successfully loaded file as CSV: {abs_path}{Style.RESET_ALL}"
                        )
                        return df
                    except:
                        logger.error(
                            f"{Fore.RED}Could not determine file format for: {abs_path}{Style.RESET_ALL}"
                        )
                        return None
        else:
            logger.info(
                f"{Fore.YELLOW}File {abs_path} not found.{Style.RESET_ALL}"
            )
            return None
    except Exception as e:
        logger.error(
            f"{Fore.RED}Error loading DataFrame from {file_path}: {str(e)}{Style.RESET_ALL}"
        )
        import traceback

        logger.error(
            f"{Fore.RED}Traceback: {traceback.format_exc()}{Style.RESET_ALL}"
        )
        return None


def parse_arguments():
    """
    Parse command line arguments.

    Returns:
        Parsed arguments object
    """
    parser = argparse.ArgumentParser(
        description="Process data with LLM using Google Sheets or local files."
    )
    parser.add_argument(
        "--file-path",
        type=str,
        required=True,
        help="Path to input file. Can be a Google Sheets URL or a local Excel/CSV file path (required)",
    )
    parser.add_argument(
        "--sheet-name",
        type=str,
        default="Sheet1",
        help="Sheet name to process (default: Sheet1)",
    )
    parser.add_argument(
        "--regenerate-all",
        action="store_true",
        help="Regenerate all values regardless of <generate> tags",
    )

    return parser.parse_args()


def main():
    """Main execution function with enhanced quality checks."""
    # Parse command line arguments
    args = parse_arguments()
    file_path = args.file_path
    sheet_name = args.sheet_name
    regenerate_all = args.regenerate_all

    if regenerate_all:
        log.info(
            "Regenerate all flag is set. Will regenerate all values regardless of generation tags."
        )

    log.info(
        "Supported generation tags: '<generate>', '//', '<g>', 'generate' (exact matches only)"
    )

    # Create data directory if it doesn't exist
    os.makedirs(DATA_DIR, exist_ok=True)

    # Determine if the file path is a Google Sheets URL or a local file
    is_gsheet = is_google_sheets_url(file_path)

    if is_gsheet:
        # Process Google Sheets URL
        log.info(f"Processing Google Sheets URL: {file_path}")
        try:
            sheet_id = get_sheet_id_from_url(file_path)
            if not sheet_id:
                log.error("Could not extract sheet ID from URL")
                console.print(
                    Panel(
                        "[bold red]Error:[/] Invalid Google Sheets URL\n"
                        "Please provide a valid Google Sheets URL in the format:\n"
                        "https://docs.google.com/spreadsheets/d/{SHEET_ID}/edit",
                        title="URL Error",
                        border_style="red",
                    )
                )
                sys.exit(1)
        except Exception as e:
            log.error(f"Error parsing Google Sheets URL: {str(e)}")
            console.print(
                Panel(
                    f"[bold red]Error:[/] Failed to parse Google Sheets URL\n{str(e)}\n"
                    "Please provide a valid Google Sheets URL",
                    title="URL Error",
                    border_style="red",
                )
            )
            sys.exit(1)

        log.info(f"Sheet ID: {sheet_id}")

        # Define local file path for reference only - we will NEVER use cached data
        local_file_path = DATA_DIR / f"{sheet_id}_{sheet_name}.xlsx"
        log.info(
            f"Local reference path (for output only): {local_file_path.absolute()}"
        )

        # ALWAYS fetch fresh data from Google Sheets - NEVER use cached data
        log.info(
            "Fetching fresh data from Google Sheets - never using cached data"
        )
        try:
            # First try to get a small range to verify access and sheet existence
            try:
                test_range = f"{sheet_name}!A1:Z1"
                log.debug(f"Testing sheet access with range: {test_range}")
                test_data = get_data_from_sheet(
                    sheet_id, range_name=test_range
                )

                if test_data is None:
                    log.error(
                        "Could not access sheet. Please check sheet permissions and name."
                    )
                    console.print(
                        Panel(
                            f"[bold red]Error:[/] Could not access sheet.\n"
                            f"Please verify:\n"
                            f"1. The sheet exists and is accessible\n"
                            f"2. The sheet name '{sheet_name}' is correct\n"
                            f"3. You have proper permissions to access the sheet\n"
                            f"4. The Google Sheets API is properly configured",
                            title="Sheet Access Error",
                            border_style="red",
                        )
                    )
                    sys.exit(1)

                # Verify we got some data back
                if len(test_data) == 0:
                    log.warning(
                        "Test range returned empty data. Sheet might be empty or range might be incorrect."
                    )
                    console.print(
                        Panel(
                            "[bold yellow]Warning:[/] Test range returned empty data\n"
                            "This might indicate:\n"
                            "1. The sheet is empty\n"
                            "2. The range is incorrect\n"
                            "3. The sheet name is wrong\n"
                            "Continuing with full data fetch...",
                            title="Empty Test Data",
                            border_style="yellow",
                        )
                    )
            except Exception as e:
                log.error(f"Error accessing sheet: {str(e)}")
                console.print(
                    Panel(
                        f"[bold red]Error:[/] Failed to access sheet\n{str(e)}\n"
                        f"Please verify:\n"
                        f"1. Sheet permissions and name\n"
                        f"2. Google Sheets API configuration\n"
                        f"3. Network connectivity",
                        title="Sheet Access Error",
                        border_style="red",
                    )
                )
                sys.exit(1)

            # Now fetch the actual data with retry logic
            max_retries = 3
            retry_delay = 2  # seconds
            for attempt in range(max_retries):
                try:
                    log.info(
                        f"Fetching full data from sheet: {sheet_name} (attempt {attempt + 1}/{max_retries})"
                    )
                    df = get_data_from_sheet(sheet_id, range_name=sheet_name)

                    # Validate the data
                    if df is None:
                        raise ValueError("get_data_from_sheet returned None")

                    if len(df) == 0:
                        raise ValueError("Retrieved empty DataFrame")

                    # Check for required columns
                    required_columns = ["col_ref"]
                    missing_columns = [
                        col
                        for col in required_columns
                        if col not in df.columns
                    ]
                    if missing_columns:
                        raise ValueError(
                            f"Missing required columns: {missing_columns}"
                        )

                    # Success - break out of retry loop
                    break

                except Exception as e:
                    if attempt == max_retries - 1:  # Last attempt
                        log.error(
                            f"Error retrieving data from Google Sheet: {str(e)}"
                        )
                        import traceback

                        log.error(f"Traceback: {traceback.format_exc()}")
                        console.print(
                            Panel(
                                f"[bold red]Error:[/] Failed to retrieve data from Google Sheet\n{str(e)}\n"
                                f"After {max_retries} attempts, please check:\n"
                                f"1. Google Sheets API configuration\n"
                                f"2. Sheet permissions\n"
                                f"3. Sheet name and data range\n"
                                f"4. Network connectivity",
                                title="Data Retrieval Error",
                                border_style="red",
                            )
                        )
                        sys.exit(1)
                    else:
                        log.warning(
                            f"Attempt {attempt + 1} failed: {str(e)}. Retrying in {retry_delay} seconds..."
                        )
                        time.sleep(retry_delay)

            # Log successful data retrieval
            log.info(
                f"Successfully retrieved {len(df)} rows from Google Sheet"
            )
            log.debug(f"DataFrame columns: {df.columns.tolist()}")
            log.debug(f"First few rows:\n{df.head()}")

        except Exception as e:
            log.error(
                f"Unexpected error during Google Sheets data retrieval: {str(e)}"
            )
            import traceback

            log.error(f"Traceback: {traceback.format_exc()}")
            console.print(
                Panel(
                    f"[bold red]Error:[/] Unexpected error during data retrieval\n{str(e)}\n"
                    f"Please check the logs for more details.",
                    title="Unexpected Error",
                    border_style="red",
                )
            )
            sys.exit(1)

        # Save fetched data to local file for reference only
        try:
            save_df_to_file(df, local_file_path)
            log.info(
                "Saved fresh Google Sheets data to local file for reference"
            )
        except Exception as e:
            log.warning(f"Failed to save local reference file: {str(e)}")
            # Don't exit here as the main data is already loaded
    else:
        # Process local file - always use the exact file path provided
        log.info(f"Processing local file: {file_path}")
        local_file_path = Path(file_path)

        # Verify file exists before proceeding
        if not local_file_path.exists():
            log.error(f"Local file not found: {local_file_path.absolute()}")
            console.print(
                Panel(
                    f"[bold red]Error:[/] Local file not found: {local_file_path.absolute()}\nPlease provide a valid file path.",
                    title="File Error",
                    border_style="red",
                )
            )
            sys.exit(1)

        # Always load data directly from the specified file path, never from a stored replica
        log.info(
            f"Loading data directly from specified file path: {local_file_path.absolute()}"
        )
        try:
            # Read the file based on extension
            file_extension = local_file_path.suffix.lower()
            if file_extension == ".csv":
                df = pd.read_csv(local_file_path)
            elif file_extension in [".xlsx", ".xls"]:
                df = pd.read_excel(local_file_path, sheet_name=sheet_name)
            else:
                log.error(f"Unsupported file extension: {file_extension}")
                console.print(
                    Panel(
                        f"[bold red]Error:[/] Unsupported file extension: {file_extension}\nSupported formats: .csv, .xlsx, .xls",
                        title="File Error",
                        border_style="red",
                    )
                )
                sys.exit(1)

            if len(df) == 0:
                log.error(
                    f"No data found in file: {local_file_path.absolute()}"
                )
                console.print(
                    Panel(
                        f"[bold red]Error:[/] No data found in file: {local_file_path.absolute()}",
                        title="Data Error",
                        border_style="red",
                    )
                )
                sys.exit(1)

        except Exception as e:
            log.error(
                f"Failed to load data from file {local_file_path.absolute()}: {str(e)}"
            )
            console.print(
                Panel(
                    f"[bold red]Error:[/] Failed to load data from file\n{str(e)}",
                    title="File Error",
                    border_style="red",
                )
            )
            sys.exit(1)

    log.debug(f"DataFrame loaded with shape {df.shape}")

    # Validate sheet structure and required columns
    required_columns = ["col_ref"]
    missing_columns = [
        col for col in required_columns if col not in df.columns
    ]
    if missing_columns:
        log.error(f"Required columns missing from sheet: {missing_columns}")
        console.print(
            Panel(
                f"[bold red]Error:[/] Required columns missing from sheet: {', '.join(missing_columns)}",
                title="Sheet Structure Error",
                border_style="red",
            )
        )
        sys.exit(1)

    # Print the first few rows to check structure
    log.debug(f"First few rows of df:\n{df.head()}")

    # Check if the first row contains column type identifiers
    try:
        df_col_ref = df.iloc[[0]]
        # find the column reference for the prompt input
        prompt_input_cols = [
            key
            for key, value in df_col_ref.to_dict("list").items()
            if "inputs" in value
        ]
        # find the column reference for the prompt output
        prompt_output_cols = [
            key
            for key, value in df_col_ref.to_dict("list").items()
            if "py-llm" in value
        ]

        # Validate that we found input and output columns
        if not prompt_input_cols:
            log.error(
                "No input columns found. Column headers in row 1 should contain 'inputs'"
            )
            console.print(
                Panel(
                    "[bold red]Error:[/] No input columns found in sheet.\nColumn headers in row 1 should contain 'inputs'",
                    title="Configuration Error",
                    border_style="red",
                )
            )
            sys.exit(1)

        if not prompt_output_cols:
            log.error(
                "No output columns found. Column headers in row 1 should contain 'py-llm'"
            )
            console.print(
                Panel(
                    "[bold red]Error:[/] No output columns found in sheet.\nColumn headers in row 1 should contain 'py-llm'",
                    title="Configuration Error",
                    border_style="red",
                )
            )
            sys.exit(1)
    except Exception as e:
        log.error(f"Failed to parse column configuration: {str(e)}")
        console.print(
            Panel(
                f"[bold red]Error:[/] Failed to parse column configuration.\nMake sure sheet follows the required format with proper column headers.\n{str(e)}",
                title="Configuration Error",
                border_style="red",
            )
        )
        sys.exit(1)

    # Create a rich table for column summary
    table = Table(title="Column Configuration Summary")
    table.add_column("Type", style="cyan")
    table.add_column("Count", style="magenta")
    table.add_column("Columns", style="green", no_wrap=False)

    table.add_row(
        "Input", str(len(prompt_input_cols)), ", ".join(prompt_input_cols)
    )
    table.add_row(
        "Output", str(len(prompt_output_cols)), ", ".join(prompt_output_cols)
    )

    # Display the table
    console.print(table)

    # Use proper log levels
    log.debug(f"Input columns: {prompt_input_cols}")
    log.debug(f"Output columns: {prompt_output_cols}")

    # Validate required configuration rows
    required_config_rows = {
        3: "prompt_template",
        4: "model",
        5: "temperature",
        6: "max_tokens",
    }

    for row_idx, config_name in required_config_rows.items():
        if row_idx >= len(df):
            log.error(
                f"Configuration row {row_idx+1} ({config_name}) is missing"
            )
            console.print(
                Panel(
                    f"[bold red]Error:[/] Configuration row {row_idx+1} ({config_name}) is missing",
                    title="Configuration Error",
                    border_style="red",
                )
            )
            sys.exit(1)

    # Extract configuration values with strict validation - no defaults or cached values allowed
    try:
        prompt_templates = list(
            df.iloc[[3]][prompt_output_cols].to_dict("records")[0].values()
        )

        # Validate prompt templates - fail immediately if any are missing or empty
        if not all(prompt_templates):
            empty_templates = [
                i
                for i, t in enumerate(prompt_templates)
                if not t or pd.isna(t) or str(t).strip() == ""
            ]
            log.error(
                f"Empty prompt templates found for columns: {[prompt_output_cols[i] for i in empty_templates]}"
            )
            console.print(
                Panel(
                    f"[bold red]Error:[/] Empty prompt templates found for columns: {[prompt_output_cols[i] for i in empty_templates]}\nConfiguration must be complete with no missing values.",
                    title="Configuration Error",
                    border_style="red",
                )
            )
            sys.exit(1)  # Exit immediately if any prompt templates are missing

        models = list(
            df.iloc[[4]][prompt_output_cols].to_dict("records")[0].values()
        )

        # Validate models - fail immediately if any are missing or empty
        if not all(models):
            empty_models = [
                i
                for i, m in enumerate(models)
                if not m or pd.isna(m) or str(m).strip() == ""
            ]
            log.error(
                f"Missing model configuration for columns: {[prompt_output_cols[i] for i in empty_models]}"
            )
            console.print(
                Panel(
                    f"[bold red]Error:[/] Missing model configuration for columns: {[prompt_output_cols[i] for i in empty_models]}\nConfiguration must be complete with no missing values.",
                    title="Configuration Error",
                    border_style="red",
                )
            )
            sys.exit(1)

        # Parse temperatures with strict validation - no defaults
        try:
            temperatures = []
            temp_values = (
                df.iloc[[5]][prompt_output_cols].to_dict("records")[0].values()
            )
            for i, temp in enumerate(temp_values):
                try:
                    if pd.isna(temp) or str(temp).strip() == "":
                        log.error(
                            f"Missing temperature value for {prompt_output_cols[i]}"
                        )
                        console.print(
                            Panel(
                                f"[bold red]Error:[/] Missing temperature for {prompt_output_cols[i]}\nConfiguration must be complete with no missing values.",
                                title="Configuration Error",
                                border_style="red",
                            )
                        )
                        sys.exit(1)

                    temp_value = float(temp)
                    temperatures.append(temp_value)
                except (ValueError, TypeError):
                    log.error(
                        f"Invalid temperature value for {prompt_output_cols[i]}: {temp}, must be a number"
                    )
                    console.print(
                        Panel(
                            f"[bold red]Error:[/] Invalid temperature for {prompt_output_cols[i]}: '{temp}'\nMust be a valid number with no missing values.",
                            title="Configuration Error",
                            border_style="red",
                        )
                    )
                    sys.exit(1)
        except Exception as e:
            log.error(f"Failed to parse temperature values: {str(e)}")
            console.print(
                Panel(
                    f"[bold red]Error:[/] Failed to parse temperature values: {str(e)}\nConfiguration must be complete with no missing values.",
                    title="Configuration Error",
                    border_style="red",
                )
            )
            sys.exit(1)

        # Parse max tokens with strict validation - no defaults
        try:
            max_output_tokens = []
            token_values = (
                df.iloc[[6]][prompt_output_cols].to_dict("records")[0].values()
            )
            for i, tokens in enumerate(token_values):
                try:
                    if pd.isna(tokens) or str(tokens).strip() == "":
                        log.error(
                            f"Missing max_tokens value for {prompt_output_cols[i]}"
                        )
                        console.print(
                            Panel(
                                f"[bold red]Error:[/] Missing max_tokens for {prompt_output_cols[i]}\nConfiguration must be complete with no missing values.",
                                title="Configuration Error",
                                border_style="red",
                            )
                        )
                        sys.exit(1)

                    token_value = int(tokens)
                    max_output_tokens.append(token_value)
                except (ValueError, TypeError):
                    log.error(
                        f"Invalid max_tokens value for {prompt_output_cols[i]}: {tokens}, must be an integer"
                    )
                    console.print(
                        Panel(
                            f"[bold red]Error:[/] Invalid max_tokens for {prompt_output_cols[i]}: '{tokens}'\nMust be a valid integer with no missing values.",
                            title="Configuration Error",
                            border_style="red",
                        )
                    )
                    sys.exit(1)
        except Exception as e:
            log.error(f"Failed to parse max_tokens values: {str(e)}")
            console.print(
                Panel(
                    f"[bold red]Error:[/] Failed to parse max_tokens values: {str(e)}\nConfiguration must be complete with no missing values.",
                    title="Configuration Error",
                    border_style="red",
                )
            )
            sys.exit(1)

        flags = list(
            df.iloc[[7]][prompt_output_cols].to_dict("records")[0].values()
        )
    except Exception as e:
        log.error(f"Failed to extract configuration values: {str(e)}")
        console.print(
            Panel(
                f"[bold red]Error:[/] Failed to extract configuration values: {str(e)}\nCheck that sheet has the required configuration rows and no values are missing.",
                title="Configuration Error",
                border_style="red",
            )
        )
        sys.exit(1)

    # Create a rich table for column configuration
    config_table = Table(title="LLM Configuration")
    config_table.add_column("Output Column", style="cyan")
    config_table.add_column("Model", style="green")
    config_table.add_column("Temperature", style="yellow")
    config_table.add_column("Max Tokens", style="magenta")

    for col, model, temp, tokens in zip(
        prompt_output_cols, models, temperatures, max_output_tokens
    ):
        config_table.add_row(col, model, str(temp), str(tokens))

    # Display the configuration table
    console.print(config_table)

    # Validate data rows exist
    if len(df) <= 19:
        log.error(
            "No data rows found. Sheet should have data starting from row 20"
        )
        console.print(
            Panel(
                "[bold red]Error:[/] No data rows found. Sheet should have data starting from row 20",
                title="Data Error",
                border_style="red",
            )
        )
        sys.exit(1)

    try:
        prompt_inputs = (
            df[prompt_input_cols]
            .iloc[19:]
            .replace("", pd.NA)
            .dropna(how="all", ignore_index=True)
        )

        if len(prompt_inputs) == 0:
            log.error("No valid data rows found after row 20")
            console.print(
                Panel(
                    "[bold red]Error:[/] No valid data rows found after row 20.\nAll rows are empty or contain no data in input columns.",
                    title="Data Error",
                    border_style="red",
                )
            )
            sys.exit(1)

        prompt_inputs["col_ref"] = (
            df["col_ref"]
            .iloc[19:]
            .reset_index(drop=True)
            .iloc[: len(prompt_inputs)]
        )
    except Exception as e:
        log.error(f"Failed to extract data rows: {str(e)}")
        console.print(
            Panel(
                f"[bold red]Error:[/] Failed to extract data rows: {str(e)}",
                title="Data Error",
                border_style="red",
            )
        )
        sys.exit(1)

    # Check that col_ref was properly extracted
    if prompt_inputs["col_ref"].isna().any():
        log.warning("Some rows are missing col_ref values")
        console.print(
            Panel(
                "[bold yellow]Warning:[/] Some rows are missing col_ref values.\nThis may cause issues with updating the sheet.",
                title="Data Warning",
                border_style="yellow",
            )
        )

    log.debug(f"First few col_ref values: {prompt_inputs['col_ref'].head()}")

    # Create a direct mapping to store LLM results for Google Sheets update
    direct_update_map = {}  # {(row_idx, col_name): (col_ref, generated_value)}

    # Summary table for generation tags
    tags_table = Table(title="Generation Tags Summary")
    tags_table.add_column("Output Column", style="cyan")
    tags_table.add_column("Total Cells", style="blue")
    tags_table.add_column("To Generate", style="green")
    tags_table.add_column("Tag Details", style="yellow")

    # Get current values for prompt_output_cols to identify rows with generation tags
    # Also prepopulate output columns with existing values for chain dependency
    for output_col in prompt_output_cols:
        col_data = (
            df[output_col]
            .iloc[19:]
            .reset_index(drop=True)
            .iloc[: len(prompt_inputs)]
        )
        prompt_inputs[f"original_{output_col}"] = col_data

        # Define set of valid generation tags (exact match required)
        generate_tags = ["<generate>", "//", "<g>", "generate"]

        # Check for presence of generation tags using various methods
        # 1. Exact match for each tag
        exact_match_counts = {
            tag: (col_data == tag).sum() for tag in generate_tags
        }
        # 2. String comparison with stripped values for each tag
        stripped_match_counts = {
            tag: col_data.astype(str).apply(lambda x: x.strip() == tag).sum()
            for tag in generate_tags
        }
        # 3. Case-insensitive match for each tag (except for "//" which is case-sensitive)
        case_insensitive_counts = {
            tag: col_data.astype(str)
            .apply(lambda x: x.strip().lower() == tag.lower())
            .sum()
            for tag in generate_tags
            if tag != "//"  # Skip case insensitive check for "//"
        }
        # Add "//" back with the same count as stripped match
        if "//" in stripped_match_counts:
            case_insensitive_counts["//"] = stripped_match_counts["//"]

        # Get total matches across all tags
        total_matches = sum(
            [
                max(
                    exact_match_counts.get(tag, 0),
                    stripped_match_counts.get(tag, 0),
                    case_insensitive_counts.get(tag, 0),
                )
                for tag in generate_tags
            ]
        )

        # Create tag details string
        tag_details = []
        for tag in generate_tags:
            tag_count = max(
                exact_match_counts.get(tag, 0),
                stripped_match_counts.get(tag, 0),
                case_insensitive_counts.get(tag, 0),
            )
            if tag_count > 0:
                tag_details.append(f"{tag}: {tag_count}")

        # Add to the summary table
        tags_table.add_row(
            output_col,
            str(len(col_data)),
            str(total_matches if not regenerate_all else len(col_data)),
            ", ".join(tag_details)
            if not regenerate_all
            else "ALL (regenerate-all flag)",
        )

        # Use proper log levels
        log.debug(
            f"Found {total_matches} rows with generation tags for column {output_col}"
        )
        for tag in generate_tags:
            if (
                max(
                    exact_match_counts.get(tag, 0),
                    stripped_match_counts.get(tag, 0),
                    case_insensitive_counts.get(tag, 0),
                )
                > 0
            ):
                log.debug(
                    f"  - Tag '{tag}': exact={exact_match_counts.get(tag, 0)}, "
                    f"stripped={stripped_match_counts.get(tag, 0)}, "
                    f"case-insensitive={case_insensitive_counts.get(tag, 0)}"
                )

        # Display sample of raw data for debugging
        sample_rows_idx = []

        # Collect rows that match any of the generation tags
        for tag in generate_tags:
            if exact_match_counts.get(tag, 0) > 0:
                sample_rows_idx.extend(
                    col_data[col_data == tag].head().index.tolist()
                )
            if stripped_match_counts.get(tag, 0) > exact_match_counts.get(
                tag, 0
            ):
                strip_matches = col_data.astype(str).apply(
                    lambda x: x.strip() == tag
                )
                sample_rows_idx.extend(
                    col_data[strip_matches].head().index.tolist()
                )
            if case_insensitive_counts.get(tag, 0) > stripped_match_counts.get(
                tag, 0
            ):
                case_matches = col_data.astype(str).apply(
                    lambda x: x.strip().lower() == tag.lower()
                )
                sample_rows_idx.extend(
                    col_data[case_matches].head().index.tolist()
                )

        sample_rows_idx = list(set(sample_rows_idx))  # Remove duplicates

        if sample_rows_idx:
            log.debug(
                f"Sample rows with generation tags for {output_col}: {sample_rows_idx}"
            )
            for idx in sample_rows_idx[:3]:  # Show first 3 samples
                raw_value = col_data.iloc[idx]
                log.debug(
                    f"Raw value at index {idx}: '{raw_value}', type: {type(raw_value)}, "
                    f"len: {len(str(raw_value))}, repr: {repr(raw_value)}"
                )

        # Initialize output column with existing values (excluding tags)
        # Function to check if a value is a generation tag
        def is_generation_tag(x):
            if pd.isna(x):
                return True
            x_str = str(x).strip()
            return (x_str in generate_tags) or (x_str.lower() == "generate")

        # Apply the check to filter out generation tags
        if regenerate_all:
            # If regenerate_all is True, clear all values to force regeneration
            prompt_inputs[output_col] = ""
            log.info(
                f"Clearing all values for column {output_col} due to --regenerate-all flag"
            )
        else:
            # Otherwise, only clear values with generation tags
            prompt_inputs[output_col] = col_data.apply(
                lambda x: "" if is_generation_tag(x) else x
            )

    # Display the tags summary table
    console.print(tags_table)

    # Dictionary to track which rows need updates for which columns
    rows_to_update = {col: [] for col in prompt_output_cols}

    # Process columns in sequence to handle dependencies between columns
    for idx, (
        prompt_template,
        model,
        temperature,
        max_tokens,
        flags,
        output_col,
    ) in enumerate(
        zip(
            prompt_templates,
            models,
            temperatures,
            max_output_tokens,
            flags,
            prompt_output_cols,
        )
    ):
        # Create filled_template column for this prompt template
        template_col = f"filled_template_{output_col}"

        # Apply the fill_template function to create a new column with filled templates
        # Include all previously processed output columns as potential input variables
        current_input_cols = prompt_input_cols.copy()

        # Add any output columns processed before this one as potential input variables
        for prev_idx in range(idx):
            prev_col = prompt_output_cols[prev_idx]
            if (
                prev_col not in current_input_cols
                and prev_col in prompt_inputs.columns
            ):
                current_input_cols.append(prev_col)

        # Validate the template before filling
        # Extract all expected variables from the template
        expected_vars = set(re.findall(r"\{\{([^}]+)\}\}", prompt_template))
        unknown_vars = [
            var for var in expected_vars if var not in current_input_cols
        ]

        if unknown_vars:
            log.error(
                f"Template for {output_col} references variables not found in input columns: {unknown_vars}"
            )
            # Create a table showing the missing variables to make it more obvious
            missing_vars_table = Table(
                title=f"⚠️ Missing Variables for {output_col}",
                caption="These variables in the prompt template don't match any input column names",
            )
            missing_vars_table.add_column("Missing Variable", style="yellow")
            missing_vars_table.add_column("Available Columns", style="green")

            available_cols_str = ", ".join(current_input_cols)
            for var in unknown_vars:
                # Suggest possible matches based on similarity
                possible_matches = [
                    col
                    for col in current_input_cols
                    if var.lower() in col.lower() or col.lower() in var.lower()
                ]
                suggestion = (
                    f"Possible match: {', '.join(possible_matches)}"
                    if possible_matches
                    else available_cols_str
                )
                missing_vars_table.add_row(var, suggestion)

            console.print(missing_vars_table)
            console.print(
                Panel(
                    f"[bold red]Error:[/] Template variables not found in input columns for {output_col}\nFix the template variables or add the missing columns.",
                    title="Template Error",
                    border_style="red",
                )
            )
            sys.exit(
                1
            )  # Exit with error if template references unknown variables

        # Check for column dependencies
        dependency_vars = [
            var for var in expected_vars if var in prompt_output_cols
        ]
        if dependency_vars:
            # Check if any dependent columns haven't been processed yet
            future_dependencies = [
                var
                for var in dependency_vars
                if var in prompt_output_cols[idx:]
            ]
            if future_dependencies:
                log.error(
                    f"Template for {output_col} depends on columns that haven't been processed yet: {future_dependencies}"
                )
                console.print(
                    Panel(
                        f"[bold red]Error:[/] Column dependency issue for {output_col}\n"
                        f"Template depends on columns that haven't been processed yet: {future_dependencies}\n"
                        f"Reorder your columns in the spreadsheet to fix dependency order.",
                        title="Dependency Error",
                        border_style="red",
                    )
                )
                sys.exit(1)

            # Check if any dependent columns (already processed) have empty values
            for dep_col in dependency_vars:
                if (
                    dep_col not in future_dependencies
                    and dep_col in prompt_inputs.columns
                ):
                    empty_deps = prompt_inputs[
                        prompt_inputs[dep_col].isna()
                        | (prompt_inputs[dep_col] == "")
                    ]
                    if not empty_deps.empty:
                        log.error(
                            f"Template for {output_col} depends on column {dep_col} which has {len(empty_deps)} empty values"
                        )
                        console.print(
                            Panel(
                                f"[bold red]Error:[/] Column {output_col} depends on {dep_col} which has {len(empty_deps)} empty values\n"
                                f"All dependent column values must be filled before processing.",
                                title="Dependency Error",
                                border_style="red",
                            )
                        )
                        sys.exit(1)

        # Apply the fill_template function to create a new column with filled templates
        template_results = prompt_inputs.apply(
            lambda row: fill_template(
                row, prompt_template, current_input_cols
            ),
            axis=1,
        )

        # Extract the filled template and missing variable information
        prompt_inputs[template_col] = template_results.apply(
            lambda x: x["filled_template"]
        )

        # Count rows with missing variables
        rows_with_missing_vars = 0
        missing_vars_by_row = {}

        for idx, result in enumerate(template_results):
            if result["missing_vars"]:
                rows_with_missing_vars += 1
                missing_vars_by_row[idx] = result["missing_vars"]

        if rows_with_missing_vars > 0:
            # Log error about missing variables (strict mode)
            log.error(
                f"{rows_with_missing_vars} rows have missing variables for {output_col} template"
            )

            # Show sample of rows with missing variables
            sample_size = min(3, len(missing_vars_by_row))
            sample_rows = list(missing_vars_by_row.items())[:sample_size]

            missing_vars_sample_table = Table(
                title=f"Sample Rows with Missing Variables for {output_col}"
            )
            missing_vars_sample_table.add_column("Row Index", style="cyan")
            missing_vars_sample_table.add_column(
                "Missing Variables", style="yellow"
            )
            missing_vars_sample_table.add_column(
                "Row Data Sample", style="green"
            )

            for row_idx, missing_vars in sample_rows:
                # Get sample data from the row to help debugging
                row_data = prompt_inputs.iloc[row_idx]
                row_sample = {
                    var: row_data.get(var, "N/A") for var in missing_vars
                }
                row_sample_str = ", ".join(
                    [f"{k}: {v}" for k, v in row_sample.items()]
                )

                missing_vars_sample_table.add_row(
                    str(row_idx), ", ".join(missing_vars), row_sample_str
                )

            console.print(missing_vars_sample_table)
            console.print(
                Panel(
                    f"[bold red]Error:[/] {rows_with_missing_vars} rows have missing variables for template {output_col}\n"
                    f"All variable values must be provided. Fix the data or update the template.",
                    title="Missing Variable Values",
                    border_style="red",
                )
            )
            sys.exit(1)  # Exit with error if any rows have missing variables

        log.debug(f"Created template column: {template_col}")

        # Find rows that have generation tags for this output column or process all rows if regenerate_all is True
        if regenerate_all:
            # If regenerate_all is True, create a mask for all rows
            generate_mask = pd.Series(
                [True] * len(prompt_inputs), index=prompt_inputs.index
            )
            log.info(
                f"Regenerate all flag is set: processing all {len(generate_mask)} rows for {output_col}"
            )
        else:
            # Otherwise, only process rows with generation tags
            generate_mask = prompt_inputs[f"original_{output_col}"].apply(
                is_generation_tag
            )
            log.debug(
                f"Generate mask for {output_col} has {generate_mask.sum()} matches"
            )

        # No need for additional checks since we're using the comprehensive approach above
        if not generate_mask.any():
            log.warning(f"No rows to process for {output_col}. Skipping.")
            continue

        generate_indices = generate_mask[generate_mask].index.tolist()
        rows_to_update[output_col] = generate_indices

        log.debug(f"Generate indices for {output_col}: {generate_indices}")

        # Filter to only process rows with generation tags
        chain_input = prompt_inputs.loc[generate_indices].copy()

        if len(chain_input) == 0:
            log.warning(
                f"No rows to process for {output_col} after filtering."
            )
            continue

        log.info(
            f"Processing {len(chain_input)} rows for {output_col} with generation tags."
        )

        # Debug some of the inputs
        if not chain_input.empty:
            log.debug(
                f"First row template for {output_col}: {chain_input[template_col].iloc[0]}"
            )

        # Process data
        llm_proc = ParallelLLMDataFrameProcessor(
            def_model=model,
            def_temperature=temperature,
            def_max_tokens=max_tokens,
            def_async_rate_limit=10,
            def_thread_rate_limit=5,
        )

        # Set the template column as the source for the prompt
        chain = [
            ChainStep(
                pt=template_col,  # Use the template column as source
                model=model,
                temperature=temperature,
                max_tokens=max_tokens,
                col=output_col,
            ),
        ]

        # Create processing status panel
        processing_panel = Panel(
            f"[bold cyan]Processing column:[/] [bold green]{output_col}[/]\n"
            f"[bold cyan]Model:[/] [green]{model}[/]\n"
            f"[bold cyan]Temperature:[/] [yellow]{temperature}[/]\n"
            f"[bold cyan]Max tokens:[/] [magenta]{max_tokens}[/]\n"
            f"[bold cyan]Rows to process:[/] [blue]{len(chain_input)}[/]",
            title="LLM Processing Started",
            border_style="yellow",
        )
        console.print(processing_panel)

        # Track failed rows and errors
        failed_indices = []
        error_messages = {}

        # Try to execute chain with error handling
        try:
            start_time = time.time()
            result_df = llm_proc.execute_chain(
                chain_input,
                chain,
                max_attempts=3,
            )
            processing_time = time.time() - start_time

            # Check for missing rows in the result
            if len(result_df) < len(chain_input):
                missing_indices = set(chain_input.index) - set(result_df.index)
                if missing_indices:
                    failed_indices.extend(missing_indices)
                    for idx in missing_indices:
                        error_messages[idx] = (
                            "Row failed to process - not found in results"
                        )
                    log.warning(
                        f"{len(missing_indices)} rows failed to process for {output_col}"
                    )

        except Exception as e:
            # Handle complete chain failure
            log.error(f"Chain execution failed for {output_col}: {str(e)}")
            console.print(
                Panel(
                    f"[bold red]Error:[/] Chain execution failed for {output_col}\n{str(e)}",
                    title="LLM Processing Error",
                    border_style="red",
                )
            )

            # Mark all rows as failed
            failed_indices = list(chain_input.index)
            for idx in failed_indices:
                error_messages[idx] = f"Chain execution failed: {str(e)}"

            # Create empty result dataframe to avoid breaking the rest of the code
            result_df = pd.DataFrame(columns=chain_input.columns)
            processing_time = time.time() - start_time

        # Create results summary table
        results_table = Table(title=f"Processing Results: {output_col}")
        results_table.add_column("Metric", style="cyan")
        results_table.add_column("Value", style="green")

        results_table.add_row("Processed rows", str(len(result_df)))
        results_table.add_row("Failed rows", str(len(failed_indices)))
        results_table.add_row(
            "Processing time", f"{processing_time:.2f} seconds"
        )

        # Only calculate averages if there were successful rows
        if len(result_df) > 0:
            results_table.add_row(
                "Avg time per row",
                f"{processing_time/max(1, len(result_df)):.2f} seconds",
            )

        # Calculate success rate
        success_rate = len(result_df) / max(1, len(chain_input)) * 100
        rate_color = (
            "green"
            if success_rate > 90
            else "yellow"
            if success_rate > 70
            else "red"
        )
        results_table.add_row(
            "Success rate",
            f"[{rate_color}]{len(result_df)}/{len(chain_input)} ({success_rate:.1f}%)[/]",
        )

        console.print(results_table)

        # Show error details if any failures occurred
        if failed_indices:
            error_table = Table(title=f"Processing Errors: {output_col}")
            error_table.add_column("Row Index", style="cyan")
            error_table.add_column("Error", style="red")

            # Show up to 5 sample errors
            sample_errors = list(error_messages.items())[:5]
            for idx, error in sample_errors:
                try:
                    row_info = (
                        f"{idx} (col_ref: {chain_input.loc[idx, 'col_ref']})"
                    )
                except:
                    row_info = str(idx)
                error_table.add_row(row_info, error)

            if len(error_messages) > 5:
                error_table.add_row(
                    "...",
                    f"[yellow]{len(error_messages) - 5} more errors omitted[/]",
                )

            console.print(error_table)

            log.warning(
                f"Failed to process {len(failed_indices)} rows for {output_col}"
            )

        if len(result_df) > 0:
            log.success(
                f"Processing complete for {output_col}: {len(result_df)} rows in {processing_time:.2f} seconds"
            )
        else:
            log.error(f"Processing failed for all rows in {output_col}")

            # If all rows failed and this is the first column, we should stop processing
            if idx == 0 and len(failed_indices) == len(chain_input):
                log.error(
                    "First column completely failed - chain dependency means all columns will fail"
                )
                console.print(
                    Panel(
                        f"[bold red]Critical Error:[/] First column '{output_col}' completely failed to process.\n"
                        f"Since other columns may depend on this one, processing will stop to prevent cascading failures.",
                        title="Processing Halted",
                        border_style="red",
                    )
                )
                break

        # Debug the results
        log.debug(f"Result DataFrame has {len(result_df)} rows")
        if not result_df.empty:
            log.debug(f"First result: {result_df[output_col].iloc[0]}")

        # Results counter for summary
        successful_updates = 0

        # DIRECTLY STORE results in our update map
        for idx_in_result in range(len(result_df)):
            try:
                result_row_idx = result_df.index[idx_in_result]
                result_value = result_df.iloc[idx_in_result][output_col]

                # Get the col_ref value for this row
                col_ref_value = None

                # Try multiple ways to get the col_ref value
                try:
                    # Method 1: Try direct .loc access
                    col_ref_value = chain_input.loc[result_row_idx, "col_ref"]
                except Exception as e:
                    log.warning(f"Error accessing col_ref via .loc: {str(e)}")
                    try:
                        # Method 2: Try .iloc if we have numeric indices
                        if isinstance(result_row_idx, int):
                            col_ref_value = chain_input.iloc[result_row_idx][
                                "col_ref"
                            ]
                    except Exception as e:
                        log.warning(
                            f"Error accessing col_ref via .iloc: {str(e)}"
                        )
                        try:
                            # Method 3: Try direct dictionary-style access
                            col_ref_value = chain_input.at[
                                result_row_idx, "col_ref"
                            ]
                        except Exception as e:
                            log.warning(
                                f"Error accessing col_ref via .at: {str(e)}"
                            )

                # If we still don't have a col_ref, look it up in the original data
                if col_ref_value is None:
                    try:
                        # Find the same index in the original prompt_inputs
                        if result_row_idx in prompt_inputs.index:
                            col_ref_value = prompt_inputs.loc[
                                result_row_idx, "col_ref"
                            ]
                            log.debug(
                                f"Found col_ref {col_ref_value} from prompt_inputs"
                            )
                    except Exception as e:
                        log.warning(
                            f"Error getting col_ref from prompt_inputs: {str(e)}"
                        )

                # As a last resort, if we still don't have a col_ref, use the row index + 1
                # This is risky but better than losing the result
                if col_ref_value is None:
                    col_ref_value = (
                        int(result_row_idx) + 1
                    )  # Add 1 since col_ref is 1-based
                    log.warning(
                        f"Using fallback col_ref value: {col_ref_value} (row index + 1)"
                    )

                # Only store non-empty values
                if pd.notna(result_value) and str(result_value).strip() != "":
                    log.debug(
                        f"Direct storage: row {result_row_idx}, col_ref {col_ref_value}, col {output_col}: '{result_value}'"
                    )

                    # Store in direct map for Google Sheets update
                    # Use a string representation of row_idx to avoid any issues with indexes
                    map_key = (str(result_row_idx), output_col)
                    direct_update_map[map_key] = (
                        col_ref_value,
                        str(result_value),
                    )
                    successful_updates += 1

                    # Also try to store in DataFrames for template use
                    try:
                        prompt_inputs.at[result_row_idx, output_col] = str(
                            result_value
                        )
                    except:
                        pass
                else:
                    log.warning(
                        f"Empty result for row {result_row_idx}, not storing"
                    )
            except Exception as e:
                log.error(
                    f"Error storing result at index {idx_in_result}: {str(e)}"
                )
                import traceback

                log.error(f"Traceback: {traceback.format_exc()}")

        # Show a summary of the updates
        log.success(
            f"Successfully stored {successful_updates} results for column {output_col}"
        )

        if output_col not in prompt_input_cols:
            prompt_input_cols.append(output_col)

    # After all processing, prepare the update dataframe directly from our map
    if direct_update_map:
        log.success(
            f"Found {len(direct_update_map)} total values to update in the sheet"
        )

        # Create a clean DataFrame for updates
        update_rows = []
        for (row_idx, col_name), (col_ref, value) in direct_update_map.items():
            log.debug(
                f"Adding to update: row {row_idx}, col_ref {col_ref}, col {col_name}, value: '{value}'"
            )
            update_rows.append({"col_ref": col_ref, col_name: value})

        # Create DataFrame and make sure every row has entries for all columns
        update_df = pd.DataFrame(update_rows)

        # Ensure all prompt_output_cols exist in the DataFrame
        for col in prompt_output_cols:
            if col not in update_df.columns:
                update_df[col] = ""

        log.debug(f"Update DataFrame created with {len(update_df)} rows")

        # Group by col_ref to consolidate updates for the same row
        if len(update_df) > 1:
            update_df = update_df.groupby("col_ref").first().reset_index()
            log.debug(
                f"After grouping by col_ref, update DataFrame has {len(update_df)} rows"
            )

        # Create a final summary table for all updates
        update_summary_table = Table(title="Final Update Summary")
        update_summary_table.add_column("Column", style="cyan")
        update_summary_table.add_column("Updated Cells", style="green")

        # Count updates per column
        col_update_counts = {}
        for _, row in update_df.iterrows():
            for col in prompt_output_cols:
                if (
                    col in row
                    and pd.notna(row[col])
                    and str(row[col]).strip() != ""
                ):
                    col_update_counts[col] = col_update_counts.get(col, 0) + 1

        # Add rows to the summary table
        for col in prompt_output_cols:
            update_count = col_update_counts.get(col, 0)
            update_summary_table.add_row(col, str(update_count))

        # Add total row
        update_summary_table.add_row(
            "TOTAL", str(sum(col_update_counts.values())), style="bold"
        )

        # Print the summary table
        console.print(
            Panel(
                update_summary_table,
                title="Processing Completed",
                border_style="green",
            )
        )

        # Print what we're updating
        for idx, row in update_df.iterrows():
            cols_with_values = [
                col
                for col in prompt_output_cols
                if col in row
                and pd.notna(row[col])
                and str(row[col]).strip() != ""
            ]
            if cols_with_values:
                log.debug(
                    f"Row with col_ref={row['col_ref']}: Updating columns {cols_with_values}"
                )
                for col in cols_with_values:
                    log.debug(f"  Value for {col}: '{row[col]}'")

        # Update the local file by applying changes to the original DataFrame
        try:
            log.info(f"Updating local file: {local_file_path}")

            # Log key information for debugging
            log.debug(
                f"Update data shape: {update_df.shape}, columns: {update_df.columns.tolist()}"
            )
            log.debug(
                f"Original DataFrame shape: {df.shape}, columns: {df.columns.tolist()}"
            )

            # Check if the file appears to be in the standard format or a custom format
            is_standard_format = True
            for row_idx in range(min(20, len(df))):
                if (
                    row_idx < 4
                    and isinstance(df.iloc[row_idx, 0], str)
                    and "leave free" in str(df.iloc[row_idx, 0])
                ):
                    is_standard_format = True
                    break

            log.debug(
                f"Detected format: {'standard' if is_standard_format else 'custom'}"
            )

            # Locate <generate> tags in the original file for validation
            for output_col in prompt_output_cols:
                if output_col in df.columns:
                    generate_rows = []
                    for i in range(len(df)):
                        cell_value = df.iloc[i].get(output_col, "")
                        if (
                            isinstance(cell_value, str)
                            and cell_value.strip() == "<generate>"
                        ):
                            generate_rows.append(i)

                    if generate_rows:
                        log.debug(
                            f"Found <generate> tags for {output_col} in original file at rows: {generate_rows}"
                        )

            # Apply updates to the original DataFrame
            updated_cells = 0
            for idx, row in update_df.iterrows():
                col_ref_value = row["col_ref"]
                try:
                    # Find the corresponding row index in the original DataFrame
                    # The mapping is different in local files vs Google Sheets
                    # In local CSV files, we need to find the actual row with <generate>

                    # First, try the standard formula (col_ref is 1-indexed and has 20-row offset)
                    df_row_idx = (
                        int(col_ref_value) + 19
                    )  # Convert to 0-indexed and add offset
                    log.debug(
                        f"Initial row index calculation: col_ref={col_ref_value}, row_idx={df_row_idx}"
                    )

                    # Check if this row actually exists in the file
                    if df_row_idx >= len(df):
                        log.warning(
                            f"Row index {df_row_idx} is out of bounds for DataFrame with {len(df)} rows"
                        )
                        continue

                    # Scan the file to find where <generate> tags actually are for this col_ref
                    matches = []
                    for i in range(
                        max(0, df_row_idx - 30), min(len(df), df_row_idx + 30)
                    ):
                        # Only check rows that have the matching col_ref
                        if (
                            i < len(df)
                            and "col_ref" in df.columns
                            and df.iloc[i]["col_ref"] == col_ref_value
                        ):
                            for output_col in prompt_output_cols:
                                if (
                                    output_col in df.columns
                                    and output_col in row
                                ):
                                    cell = df.iloc[i][output_col]
                                    if (
                                        isinstance(cell, str)
                                        and "<generate>" in cell
                                    ):
                                        matches.append((i, output_col))

                    if matches:
                        log.debug(f"Found matching <generate> rows: {matches}")
                        # Use the first matching row
                        df_row_idx = matches[0][0]
                        log.debug(f"Using found row index: {df_row_idx}")

                    # Apply each column update
                    for col in prompt_output_cols:
                        if (
                            col in row
                            and pd.notna(row[col])
                            and str(row[col]).strip() != ""
                        ):
                            if col in df.columns:
                                # Update the row at the calculated index
                                df.at[df_row_idx, col] = row[col]
                                updated_cells += 1
                                log.debug(
                                    f"Updated local DataFrame row {df_row_idx}, col {col} with value: '{row[col]}'"
                                )
                            else:
                                log.warning(
                                    f"Column {col} not found in original DataFrame"
                                )
                except Exception as e:
                    log.error(
                        f"Error updating row with col_ref {col_ref_value}: {str(e)}"
                    )
                    import traceback

                    log.error(f"Traceback: {traceback.format_exc()}")

            # Save the updated DataFrame back to the original file path
            log.info(f"Saving changes back to: {local_file_path}")
            save_df_to_file(df, local_file_path)

            # Double check file exists after save
            if os.path.exists(local_file_path):
                file_size = os.path.getsize(local_file_path)
                log.success(
                    f"Local file updated successfully with {updated_cells} cells. File size: {file_size} bytes"
                )
            else:
                log.warning(
                    f"Cannot verify updated file at {local_file_path} - file not found"
                )

        except Exception as e:
            log.error(f"Error updating local file: {str(e)}")
            import traceback

            log.error(f"Traceback: {traceback.format_exc()}")

        # Directly update Google Sheets if the input was a Google Sheets URL
        if is_gsheet:
            try:
                log.info("Updating remote Google Sheet")
                sheet_id = get_sheet_id_from_url(file_path)
                update_to_sheet(
                    update_df, prompt_output_cols, sheet_id, sheet_name
                )
                log.success(
                    f"Remote Google Sheet updated successfully with {sum(col_update_counts.values())} cells"
                )
            except Exception as e:
                log.error(f"Error updating Google Sheet: {str(e)}")
                import traceback

                log.error(f"Traceback: {traceback.format_exc()}")
        else:
            log.info("Skipping Google Sheet update as input was a local file")

    else:
        log.warning("No valid LLM results found to update.")

    # Final status message with comprehensive processing summary
    console.print("\n[bold blue]===== PROCESSING SUMMARY =====[/]")

    # Create a comprehensive summary table
    summary_table = Table(title="Processing Results Summary")
    summary_table.add_column("Column", style="cyan")
    summary_table.add_column("Processed", style="blue")
    summary_table.add_column("Success", style="green")
    summary_table.add_column("Failed", style="red")
    summary_table.add_column("Success Rate", style="yellow")
    summary_table.add_column("Updated", style="magenta")

    # Track overall statistics
    total_processed = 0
    total_successful = 0
    total_failed = 0
    total_updated = 0

    # Collect statistics for each column
    for col in prompt_output_cols:
        # Get the number of rows processed for this column
        processed = len(rows_to_update.get(col, []))
        total_processed += processed

        # For success count, use the number of cells that were updated in this column
        updated = (
            col_update_counts.get(col, 0)
            if "col_update_counts" in locals()
            else 0
        )
        total_updated += updated

        # Calculate failures (those that were attempted but not updated)
        failed = processed - updated
        if failed < 0:
            failed = 0  # In case of unexpected behavior
        total_failed += failed

        # Calculate successful rows (those that were processed without errors)
        successful = processed - failed
        total_successful += successful

        # Calculate success rate
        success_rate = (successful / max(1, processed)) * 100

        # Add row with color-coded success rate
        rate_color = (
            "green"
            if success_rate > 90
            else "yellow"
            if success_rate > 70
            else "red"
        )
        summary_table.add_row(
            col,
            str(processed),
            str(successful),
            str(failed),
            f"[{rate_color}]{success_rate:.1f}%[/]",
            str(updated),
        )

    # Add totals row
    total_success_rate = (total_successful / max(1, total_processed)) * 100
    rate_color = (
        "green"
        if total_success_rate > 90
        else "yellow"
        if total_success_rate > 70
        else "red"
    )
    summary_table.add_row(
        "[bold]TOTAL[/]",
        f"[bold]{total_processed}[/]",
        f"[bold]{total_successful}[/]",
        f"[bold]{total_failed}[/]",
        f"[bold][{rate_color}]{total_success_rate:.1f}%[/][/]",
        f"[bold]{total_updated}[/]",
        style="bold",
    )

    # Print the summary table
    console.print(summary_table)

    # Display execution time
    total_execution_time = time.time() - time.process_time()
    console.print(
        f"[bold blue]Total execution time:[/] [green]{total_execution_time:.2f} seconds[/]"
    )

    # Final status message panel
    if direct_update_map:
        console.print(
            Panel(
                f"[bold green]Process completed successfully![/]\n"
                f"[cyan]Total cells updated:[/] [yellow]{sum(col_update_counts.values()) if 'col_update_counts' in locals() else 0}[/]\n"
                f"[cyan]File:[/] [blue]{local_file_path}[/]",
                title="Summary",
                border_style="green",
            )
        )
    else:
        console.print(
            Panel(
                f"[bold yellow]Process completed with no updates[/]\n"
                f"[cyan]File:[/] [blue]{local_file_path}[/]",
                title="Summary",
                border_style="yellow",
            )
        )


if __name__ == "__main__":
    main()
