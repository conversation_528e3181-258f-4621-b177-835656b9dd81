Requirements:
"* <PERSON><PERSON><PERSON> takes sheet name as input, doesn't create new sheet unless sheet name doesn't exist, logs if doing so
- new sheet won't have this format.
- <feat> default to having a base sheet -> duplicate it and remove the data"
* LiteLLM doing processing
* Running python script checks that all required input cols are present, else error
* script checks for existence of 
✅ -> config pulled as ref to it's name in col A,not row number. We need to agree defaults, which should be GLOB_VARS in guard main.
"- add config: structured schema
- accept JSON_MODE etc"
model name validation in-sheet [AF todo]

Handling the Config Rows
Split handling of config and main df
"DF TERMINOLOGY: Apply = take a function and apply it to every row in a DF
Take worksheet.get(""A1:MaxCol"")"
header row = header row
[r2-20] -> df_config
df_1 = remove_r2-20(df_1)

Limitation:
- no way of including non-LLM logic
- missing cells -> NaN (eg 30 rows for one, 100 for another)

In Python -> Run Main()
* How to know which LLM_func to run for which col?
Option1: Define ChainSteps -> allows arb pythong
Option2: singl col name each time
"✨Option3: List of col names

cols_to_run = [
   # name,
   # profession,
   # age,
   new_career,
   issues,
]
>> This new_career then issues.

This way we can just comment / uncomment.
e.g.,:

cols_to_run = [
   #name,
   #profession,
   #age,
   #new_career,
   issues,
]
>> // this will only run issues col"
<generate> <<only generate for cells where I've added this flag? See col I20-I34

parsing -> what to parse out, and where to put it? Could put in following cols, make light gray, and group. 
Parsing for templates e.g., {{name.output}} should return the section within <output> (.*)</output> within 'name' col for the given row.
Object coersion (like but not eval()) -> Structured outputs are automatically coerced into the object (dict, list_dict, list, whatever it is)
StrictTypes -> how to handle pydantic definitions.
Sheets code -> how to not overwrite it? Only fill empty rows? Could mark rows requesting updates with <target> or UPDATE
header row dup?
prompt shorthand? Add BLUF etc
How can we handle recursion / feedback loops?
How can we better async parellise by 'dependency graph' ? -> potentially construct a pipeline / chain of function calls but also could just have conditional flags for each task (how to make that non-blocking? WHY: if-rerunning a large 1000 row pipeline end-end, waiting for the slowest return from each col to complete the gather before continuing is painful.
--> how to handle retries, timeouts, failed runs, passing back errors into sheet.
semaphore, rate limiting and fallback models. Annotate fallbacks with <fb> full output </fb> //fb = model_name
ensemble-vertical and ensemble-horizontal
Iteration mode -> duplicates sheet and updates rather than updating same sheet. Requires duplicating the formatting, groups, etc. Probalby just full duplicate the sheet first (may require waiting to complete...)

Prompt Goal Statement

"✨Option3: List of col names
HOW TO ACHIVE PARELLISATION WITHIN FLOW
nested lists?
cols_to_run = [
   # name,
   # profession,
   # age,
   [new_career, new_career2, new_career3],
   issues,
]

Or for recursion?
We could parse a mermaid chart :P"
MYPY:   HOW TO ADJUST PROMPT TEMPLATE SETUP FOR 
CATCHING COMPILING ERRORS -> e.g, prompt variables not going in, or accidental miss-construction. NEED to have unit-tests for this.
--> and need to have compiled prompt pushed somewhere in sheets, even if just a hidden sheet that can be used as a lookup.

Agents