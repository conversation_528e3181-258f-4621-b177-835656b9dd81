import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))


def main():
    print(f"Python version: {sys.version}")

    # Test importing modules from the project
    try:
        from llm_processor.processor import ParallelLLMDataFrameProcessor
        print("\nSuccessfully imported ParallelLLMDataFrameProcessor")
    except ImportError as e:
        print(f"\nError importing ParallelLLMDataFrameProcessor: {e}")

    try:
        from llm_processor.chain_step import ChainStep
        print("Successfully imported ChainStep")
    except ImportError as e:
        print(f"Error importing ChainStep: {e}")

    try:
        from main_config import CFG
        print("Successfully imported CFG")
    except ImportError as e:
        print(f"Error importing CFG: {e}")

    print("\nTest completed successfully!")


if __name__ == "__main__":
    main()
