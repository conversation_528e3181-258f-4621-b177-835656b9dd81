# Python 3.12 Compatibility Tests

These test scripts verify that the project works correctly with Python 3.12.

## Test Files

1. `test_python312_simple.py` - Basic test that verifies Python and pandas versions
2. `test_python312.py` - Tests importing the `ParallelLLMDataFrameProcessor` class
3. `test_python312_import.py` - Tests importing various project modules

## Running the Tests

To run these tests, make sure you have a Python 3.12 virtual environment activated:

```bash
# Create a Python 3.12 virtual environment
rm -rf .venv && uv venv -p python3.12

# Install dependencies
uv sync

# Activate the virtual environment
source .venv/bin/activate

# Run the tests
uv run scripts/python312_tests/test_python312_simple.py
uv run scripts/python312_tests/test_python312.py
uv run scripts/python312_tests/test_python312_import.py
```

## Expected Output

All tests should run without errors and show that the project's modules can be imported successfully.
