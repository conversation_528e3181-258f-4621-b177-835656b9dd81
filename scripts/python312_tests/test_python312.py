import sys
import os
import pandas as pd

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))


def main():
    print(f"Python version: {sys.version}")
    print(f"Pandas version: {pd.__version__}")

    # Create a simple DataFrame
    df = pd.DataFrame({
        'A': [1, 2, 3],
        'B': ['a', 'b', 'c']
    })
    print("\nSample DataFrame:")
    print(df)

    # Test importing a module from the project
    try:
        from llm_processor.processor import ParallelLLMDataFrameProcessor
        print("\nSuccessfully imported ParallelLLMDataFrameProcessor")
    except ImportError as e:
        print(f"\nError importing ParallelLLMDataFrameProcessor: {e}")

    print("\nTest completed successfully!")


if __name__ == "__main__":
    main()
