#!/usr/bin/env python3
"""
Script to set up pre-commit hooks for the project.
"""

import os
import sys
import subprocess


def main():
    """Install pre-commit hooks and add coverage to git hooks."""
    print("\n🔧 Setting up pre-commit hooks...\n")

    # Get project root directory
    project_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

    # Make sure pre-commit is installed
    try:
        subprocess.run(["pre-commit", "--version"], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        print("✅ pre-commit is installed")
    except (subprocess.SubprocessError, FileNotFoundError):
        print("⚠️ pre-commit not found. Installing...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pre-commit"], check=True)

    # Install the pre-commit hooks
    print("\n📋 Installing pre-commit hooks...")
    subprocess.run(["pre-commit", "install"], cwd=project_dir, check=True)

    # Install coverage hook
    print("\n📋 Installing coverage hook for manual use...")
    subprocess.run(["pre-commit", "install-hooks"], cwd=project_dir, check=True)

    print("\n✨ Hooks setup complete!")
    print("\nAvailable hooks:")
    print("  • pre-commit run --all-files            # Run all hooks")
    print("  • pre-commit run coverage-check         # Run coverage check")
    print("\nHooks will run automatically on commit, or manually with the commands above.")

    return 0


if __name__ == "__main__":
    sys.exit(main())
