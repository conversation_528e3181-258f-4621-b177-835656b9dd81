#!/usr/bin/env bash
set -euo pipefail

# When running in Codex or any locked-down environment, we cannot reach
# files.pythonhosted.org. Vendor wheels to the wheelhouse/ directory
# and install them offline.

wheelhouse="$(dirname "$0")/../wheelhouse"

if [ -d "$wheelhouse" ] && [ "$(ls -A "$wheelhouse" 2>/dev/null)" ]; then
  uv sync --offline --find-links "$wheelhouse"
else
  echo "[codex_setup] wheelhouse/ is missing or empty."
  echo "Run 'uv pip download -r pyproject.toml --dest wheelhouse' with network access before invoking this script." >&2
  exit 1
fi
