# Feature: Error <PERSON>ling and Retries (Part 2)

Original requirement:
> --> how to handle retries, timeouts, failed runs, passing back errors into sheet.

## Implementation Status: Partially Implemented

In Part 1, we covered the enhanced error detection and retry mechanisms. In Part 2, we'll focus on passing errors back to the sheet, configuring error handling, and implementing error recovery strategies.

## Implementation Recommendation: Error Reporting and Sheet Integration

### 1. Error Formatting for Sheet Display

```python
def format_error_for_sheet(error: ProcessingError) -> Dict[str, str]:
    """
    Format an error for display in the sheet.
    
    Args:
        error: Processing error to format
        
    Returns:
        Dictionary with formatted error values for different columns
    """
    # Base error message for the main column
    main_message = f"[ERROR] [{error.error_type.value}] {error.message}"
    
    # Add retry information if applicable
    if error.retry_count > 0:
        main_message += f" (Retries: {error.retry_count})"
    
    # Create a dictionary with values for different columns
    result = {
        "main": main_message,
        "status": "error",
        "type": error.error_type.value,
        "message": error.message,
        "retry_count": str(error.retry_count),
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(error.timestamp)),
        "details": json.dumps(error.details) if error.details else "",
    }
    
    return result
```

### 2. Enhanced Sheet Update Function for Error Reporting

```python
def update_sheet_with_errors(
    df: pd.DataFrame,
    output_cols: List[str],
    sheet_id: str,
    sheet_name: str,
    include_error_details: bool = True,
) -> None:
    """
    Update the sheet with results and error information.
    
    Args:
        df: DataFrame with results and error information
        output_cols: List of output column names
        sheet_id: ID of the Google Sheet
        sheet_name: Name of the sheet
        include_error_details: Whether to include detailed error information
    """
    try:
        # Get sheet information
        sheet = service.spreadsheets()
        sheet_metadata = sheet.get(spreadsheetId=sheet_id).execute()
        
        # Get headers
        result = sheet.values().get(spreadsheetId=sheet_id, range=f"{sheet_name}!A1:Z1").execute()
        headers = result.get("values", [[]])[0]
        
        # Prepare update data
        update_data = []
        
        # For each row in the DataFrame
        for idx, row in df.iterrows():
            # Get col_ref value
            col_ref_value = row.get("col_ref")
            
            if col_ref_value is None or pd.isna(col_ref_value):
                continue
            
            try:
                # Convert to integer
                row_ref = int(col_ref_value)
                
                # Apply 20-row offset (col_ref=1 corresponds to row 21)
                sheet_row = row_ref + 20
                
                # For each output column
                for col in output_cols:
                    # Skip if column doesn't exist in DataFrame
                    if col not in df.columns:
                        continue
                    
                    # Get column index in sheet
                    if col in headers:
                        col_idx = headers.index(col)
                        col_letter = chr(65 + col_idx) if col_idx < 26 else chr(64 + col_idx // 26) + chr(65 + col_idx % 26)
                    else:
                        logger.warning(f"{Fore.YELLOW}Column {col} not found in sheet headers{Style.RESET_ALL}")
                        continue
                    
                    # Get cell value
                    cell_value = row.get(col)
                    
                    # Skip empty values
                    if pd.isna(cell_value) or (isinstance(cell_value, str) and cell_value.strip() == ""):
                        continue
                    
                    # Check if it's an error
                    is_error = False
                    if isinstance(cell_value, str) and cell_value.startswith("[ERROR]"):
                        is_error = True
                    
                    # Add main value to update data
                    update_data.append({
                        "range": f"{sheet_name}!{col_letter}{sheet_row}",
                        "values": [[str(cell_value)]],
                    })
                    
                    # If it's an error and we should include error details
                    if is_error and include_error_details:
                        # Add error status column if it exists
                        status_col = f"{col}_status"
                        if status_col in row and status_col in headers:
                            status_idx = headers.index(status_col)
                            status_letter = chr(65 + status_idx) if status_idx < 26 else chr(64 + status_idx // 26) + chr(65 + status_idx % 26)
                            update_data.append({
                                "range": f"{sheet_name}!{status_letter}{sheet_row}",
                                "values": [[str(row.get(status_col, "error"))]],
                            })
                        
                        # Add error type column if it exists
                        type_col = f"{col}_error_type"
                        if type_col in row and type_col in headers:
                            type_idx = headers.index(type_col)
                            type_letter = chr(65 + type_idx) if type_idx < 26 else chr(64 + type_idx // 26) + chr(65 + type_idx % 26)
                            update_data.append({
                                "range": f"{sheet_name}!{type_letter}{sheet_row}",
                                "values": [[str(row.get(type_col, "unknown"))]],
                            })
                        
                        # Add error message column if it exists
                        message_col = f"{col}_error_message"
                        if message_col in row and message_col in headers:
                            message_idx = headers.index(message_col)
                            message_letter = chr(65 + message_idx) if message_idx < 26 else chr(64 + message_idx // 26) + chr(65 + message_idx % 26)
                            update_data.append({
                                "range": f"{sheet_name}!{message_letter}{sheet_row}",
                                "values": [[str(row.get(message_col, ""))]],
                            })
                        
                        # Add retry count column if it exists
                        retry_col = f"{col}_retry_count"
                        if retry_col in row and retry_col in headers:
                            retry_idx = headers.index(retry_col)
                            retry_letter = chr(65 + retry_idx) if retry_idx < 26 else chr(64 + retry_idx // 26) + chr(65 + retry_idx % 26)
                            update_data.append({
                                "range": f"{sheet_name}!{retry_letter}{sheet_row}",
                                "values": [[str(row.get(retry_col, "0"))]],
                            })
            except (ValueError, TypeError) as e:
                logger.warning(f"{Fore.YELLOW}Invalid col_ref value: {col_ref_value}. Error: {str(e)}{Style.RESET_ALL}")
        
        # Execute batch update
        if update_data:
            # Split into batches to avoid API limits
            batch_size = 100
            for i in range(0, len(update_data), batch_size):
                batch = update_data[i:i+batch_size]
                body = {"valueInputOption": "RAW", "data": batch}
                sheet.values().batchUpdate(spreadsheetId=sheet_id, body=body).execute()
            
            logger.info(f"{Fore.GREEN}Updated {len(update_data)} cells in sheet{Style.RESET_ALL}")
        else:
            logger.warning(f"{Fore.YELLOW}No cells to update{Style.RESET_ALL}")
            
    except Exception as e:
        logger.error(f"{Fore.RED}Error updating sheet: {str(e)}{Style.RESET_ALL}")
        import traceback
        logger.error(f"{Fore.RED}Traceback: {traceback.format_exc()}{Style.RESET_ALL}")
```

### 3. Add Error Status Columns to Sheet

```python
def add_error_status_columns(sheet_id: str, sheet_name: str, output_cols: List[str]) -> None:
    """
    Add error status columns to the sheet.
    
    Args:
        sheet_id: ID of the Google Sheet
        sheet_name: Name of the sheet
        output_cols: List of output column names
    """
    try:
        # Get sheet information
        sheet = service.spreadsheets()
        sheet_metadata = sheet.get(spreadsheetId=sheet_id).execute()
        
        # Get headers
        result = sheet.values().get(spreadsheetId=sheet_id, range=f"{sheet_name}!A1:Z1").execute()
        headers = result.get("values", [[]])[0]
        
        # Determine which error columns to add
        columns_to_add = []
        for col in output_cols:
            status_col = f"{col}_status"
            type_col = f"{col}_error_type"
            message_col = f"{col}_error_message"
            retry_col = f"{col}_retry_count"
            
            if status_col not in headers:
                columns_to_add.append(status_col)
            if type_col not in headers:
                columns_to_add.append(type_col)
            if message_col not in headers:
                columns_to_add.append(message_col)
            if retry_col not in headers:
                columns_to_add.append(retry_col)
        
        if not columns_to_add:
            logger.info(f"{Fore.GREEN}No error status columns to add{Style.RESET_ALL}")
            return
        
        # Get sheet ID
        sheet_id_num = None
        for s in sheet_metadata.get("sheets", []):
            if s["properties"]["title"] == sheet_name:
                sheet_id_num = s["properties"]["sheetId"]
                break
        
        if sheet_id_num is None:
            logger.error(f"{Fore.RED}Sheet {sheet_name} not found{Style.RESET_ALL}")
            return
        
        # Prepare append column request
        requests = []
        for col_name in columns_to_add:
            requests.append({
                "appendDimension": {
                    "sheetId": sheet_id_num,
                    "dimension": "COLUMNS",
                    "length": 1,
                }
            })
        
        # Execute request
        body = {"requests": requests}
        sheet.batchUpdate(spreadsheetId=sheet_id, body=body).execute()
        
        # Update headers
        new_headers = headers + columns_to_add
        sheet.values().update(
            spreadsheetId=sheet_id,
            range=f"{sheet_name}!A1:{chr(65 + len(new_headers) - 1)}1",
            valueInputOption="RAW",
            body={"values": [new_headers]},
        ).execute()
        
        logger.info(f"{Fore.GREEN}Added error status columns: {columns_to_add}{Style.RESET_ALL}")
        
    except Exception as e:
        logger.error(f"{Fore.RED}Error adding error status columns: {str(e)}{Style.RESET_ALL}")
        import traceback
        logger.error(f"{Fore.RED}Traceback: {traceback.format_exc()}{Style.RESET_ALL}")
```

### 4. Add Error Recovery Configuration

```python
class ErrorRecoveryStrategy(Enum):
    """Strategies for recovering from errors."""
    SKIP = "skip"              # Skip the error and continue
    RETRY = "retry"            # Retry the operation
    FALLBACK = "fallback"      # Use a fallback model
    PROMPT_REPAIR = "repair"   # Try to repair the prompt and retry
    MANUAL = "manual"          # Require manual intervention

class ErrorRecoveryConfig:
    """
    Configuration for error recovery.
    """
    
    def __init__(
        self,
        default_strategy: ErrorRecoveryStrategy = ErrorRecoveryStrategy.RETRY,
        max_retries: int = 3,
        fallback_model: str = "gpt-3.5-turbo",
        strategies_by_error_type: Optional[Dict[ErrorType, ErrorRecoveryStrategy]] = None,
    ):
        """
        Initialize error recovery configuration.
        
        Args:
            default_strategy: Default recovery strategy
            max_retries: Maximum number of retries
            fallback_model: Fallback model to use
            strategies_by_error_type: Dictionary mapping error types to recovery strategies
        """
        self.default_strategy = default_strategy
        self.max_retries = max_retries
        self.fallback_model = fallback_model
        self.strategies_by_error_type = strategies_by_error_type or {}
    
    def get_strategy(self, error_type: ErrorType) -> ErrorRecoveryStrategy:
        """
        Get the recovery strategy for an error type.
        
        Args:
            error_type: Type of error
            
        Returns:
            Recovery strategy to use
        """
        return self.strategies_by_error_type.get(error_type, self.default_strategy)
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'ErrorRecoveryConfig':
        """
        Create an ErrorRecoveryConfig from a dictionary.
        
        Args:
            config_dict: Dictionary with configuration values
            
        Returns:
            ErrorRecoveryConfig instance
        """
        default_strategy = ErrorRecoveryStrategy(config_dict.get("default_strategy", "retry"))
        max_retries = config_dict.get("max_retries", 3)
        fallback_model = config_dict.get("fallback_model", "gpt-3.5-turbo")
        
        strategies_by_error_type = {}
        for error_type_str, strategy_str in config_dict.get("strategies_by_error_type", {}).items():
            try:
                error_type = ErrorType(error_type_str)
                strategy = ErrorRecoveryStrategy(strategy_str)
                strategies_by_error_type[error_type] = strategy
            except (ValueError, KeyError):
                logger.warning(f"{Fore.YELLOW}Invalid error type or strategy: {error_type_str} -> {strategy_str}{Style.RESET_ALL}")
        
        return cls(
            default_strategy=default_strategy,
            max_retries=max_retries,
            fallback_model=fallback_model,
            strategies_by_error_type=strategies_by_error_type,
        )
```

### 5. Implement Error Recovery Logic

```python
async def recover_from_error(
    error: ProcessingError,
    config: ErrorRecoveryConfig,
    prompt: str,
    model: str,
    temperature: float,
    max_tokens: int,
    llm_processor: ParallelLLMDataFrameProcessor,
) -> Tuple[Optional[str], Optional[ProcessingError]]:
    """
    Attempt to recover from an error.
    
    Args:
        error: Processing error to recover from
        config: Error recovery configuration
        prompt: Original prompt
        model: Original model
        temperature: Original temperature
        max_tokens: Original max tokens
        llm_processor: LLM processor to use
        
    Returns:
        Tuple of (recovered_response, new_error)
    """
    # Get recovery strategy
    strategy = config.get_strategy(error.error_type)
    
    logger.info(f"{Fore.CYAN}Recovering from {error.error_type.value} error using {strategy.value} strategy{Style.RESET_ALL}")
    
    if strategy == ErrorRecoveryStrategy.SKIP:
        # Skip the error and return None
        return None, error
    
    elif strategy == ErrorRecoveryStrategy.RETRY:
        # Retry with the same parameters
        if error.retry_count >= config.max_retries:
            logger.warning(f"{Fore.YELLOW}Max retries ({config.max_retries}) exceeded for {error.error_type.value} error{Style.RESET_ALL}")
            return None, error
        
        try:
            # Increment retry count
            error.retry_count += 1
            
            # Add exponential backoff
            backoff_seconds = 2 ** error.retry_count
            logger.info(f"{Fore.CYAN}Retrying after {backoff_seconds} seconds (retry {error.retry_count}/{config.max_retries}){Style.RESET_ALL}")
            await asyncio.sleep(backoff_seconds)
            
            # Retry the request
            response = await llm_processor._async_get_response_with_error_handling(
                message=prompt,
                temperature=temperature,
                max_tokens=max_tokens,
                model=model,
                timeout_seconds=60.0,
            )
            
            return response, None
            
        except ProcessingError as e:
            # Update retry count
            e.retry_count = error.retry_count
            return None, e
    
    elif strategy == ErrorRecoveryStrategy.FALLBACK:
        # Try with fallback model
        try:
            logger.info(f"{Fore.CYAN}Using fallback model {config.fallback_model} instead of {model}{Style.RESET_ALL}")
            
            # Use fallback model
            response = await llm_processor._async_get_response_with_error_handling(
                message=prompt,
                temperature=temperature,
                max_tokens=max_tokens,
                model=config.fallback_model,
                timeout_seconds=60.0,
            )
            
            # Add fallback note to response
            response = f"{response}\n\n[Note: Generated using fallback model {config.fallback_model}]"
            
            return response, None
            
        except ProcessingError as e:
            return None, e
    
    elif strategy == ErrorRecoveryStrategy.PROMPT_REPAIR:
        # Try to repair the prompt and retry
        try:
            # Create a prompt to repair the original prompt
            repair_prompt = f"""
You are an AI prompt repair assistant. The following prompt caused an error when sent to an LLM:

ERROR TYPE: {error.error_type.value}
ERROR MESSAGE: {error.message}

ORIGINAL PROMPT:
{prompt}

Please create a repaired version of this prompt that is more likely to work correctly.
Only output the repaired prompt, nothing else.
"""
            
            # Use a reliable model for repair
            repair_model = "gpt-3.5-turbo"
            
            logger.info(f"{Fore.CYAN}Attempting to repair prompt using {repair_model}{Style.RESET_ALL}")
            
            # Get repaired prompt
            repaired_prompt = await llm_processor._async_get_response_with_error_handling(
                message=repair_prompt,
                temperature=0.2,  # Low temperature for more reliable output
                max_tokens=max_tokens,
                model=repair_model,
                timeout_seconds=60.0,
            )
            
            logger.info(f"{Fore.CYAN}Prompt repaired. Retrying with repaired prompt.{Style.RESET_ALL}")
            
            # Retry with repaired prompt
            response = await llm_processor._async_get_response_with_error_handling(
                message=repaired_prompt,
                temperature=temperature,
                max_tokens=max_tokens,
                model=model,
                timeout_seconds=60.0,
            )
            
            # Add repair note to response
            response = f"{response}\n\n[Note: Generated using repaired prompt]"
            
            return response, None
            
        except ProcessingError as e:
            return None, e
    
    elif strategy == ErrorRecoveryStrategy.MANUAL:
        # Require manual intervention
        logger.warning(f"{Fore.YELLOW}Manual intervention required for {error.error_type.value} error{Style.RESET_ALL}")
        return None, error
    
    else:
        # Unknown strategy
        logger.warning(f"{Fore.YELLOW}Unknown recovery strategy: {strategy.value}{Style.RESET_ALL}")
        return None, error
```

### 6. Update Process Chain Step to Use Error Recovery

```python
async def _process_chain_step_with_recovery(
    self,
    c_row: pd.Series,
    step: ChainStep,
    mapping: dict,
    max_attempts: int,
    timeout_seconds: float = 60.0,
    recovery_config: Optional[ErrorRecoveryConfig] = None,
) -> None:
    """
    Process a single chain step for a given row with error recovery.
    
    Args:
        c_row (pd.Series): The current row of the DataFrame.
        step (ChainStep): The chain step to process.
        mapping (dict): The mapping of prompt keywords to column names.
        max_attempts (int): The maximum number of retry attempts for failed API calls.
        timeout_seconds (float): Timeout in seconds for LLM requests.
        recovery_config (ErrorRecoveryConfig): Configuration for error recovery.
    """
    # Use default recovery config if none provided
    if recovery_config is None:
        recovery_config = ErrorRecoveryConfig()
    
    prompt = c_row.get(step.pt)
    if c_row.get(step.col) and not step.overwrite:
        logger.debug(f"{step.col} already exists (skip)")
        return
        
    for k, v in mapping.items():
        prompt = prompt.replace(f"{{{k}}}", str(c_row.get(v, "N/A")))

    temperature = (
        c_row.get("__temperature")
        or step.temperature
        or self.def_temperature
    )
    max_tokens = (
        c_row.get("__max_tokens") or step.max_tokens or self.def_max_tokens
    )
    model = c_row.get("__model") or step.model or self.def_model

    try:
        # First attempt
        response = await self._async_get_response_with_error_handling(
            message=prompt,
            temperature=temperature,
            max_tokens=max_tokens,
            model=model,
            timeout_seconds=timeout_seconds,
        )
        
        # Store successful response
        c_row[step.col] = response
        
        # Store success status
        c_row[f"{step.col}_status"] = "success"
        
    except ProcessingError as e:
        # Attempt recovery
        recovered_response, new_error = await recover_from_error(
            error=e,
            config=recovery_config,
            prompt=prompt,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            llm_processor=self,
        )
        
        if recovered_response is not None:
            # Recovery succeeded
            c_row[step.col] = recovered_response
            c_row[f"{step.col}_status"] = "recovered"
            c_row[f"{step.col}_error_type"] = e.error_type.value
            c_row[f"{step.col}_error_message"] = e.message
            c_row[f"{step.col}_retry_count"] = e.retry_count
        else:
            # Recovery failed
            error_msg = new_error.to_sheet_value()
            logger.error(f"Error in _process_chain_step: {error_msg}")
            
            # Store error in the output column
            c_row[step.col] = error_msg
            
            # Store error details in status columns
            c_row[f"{step.col}_status"] = "error"
            c_row[f"{step.col}_error_type"] = new_error.error_type.value
            c_row[f"{step.col}_error_message"] = new_error.message
            c_row[f"{step.col}_retry_count"] = new_error.retry_count
    
    except Exception as e:
        # Handle unexpected exceptions
        error = ProcessingError.from_exception(e)
        error_msg = error.to_sheet_value()
        logger.error(f"Unexpected error in _process_chain_step: {error_msg}")
        
        # Store error in the output column
        c_row[step.col] = error_msg
        
        # Store error details in status columns
        c_row[f"{step.col}_status"] = "error"
        c_row[f"{step.col}_error_type"] = error.error_type.value
        c_row[f"{step.col}_error_message"] = error.message
```

## Architecture Diagram

```mermaid
flowchart TD
    A[Start Processing] --> B[Prepare Prompt]
    B --> C[Execute LLM Request]
    
    C --> D{Success?}
    D -->|Yes| E[Store Response]
    D -->|No| F[Classify Error]
    
    F --> G[Determine Recovery Strategy]
    G --> H{Strategy?}
    
    H -->|Skip| I[Skip and Continue]
    H -->|Retry| J[Retry with Backoff]
    H -->|Fallback| K[Use Fallback Model]
    H -->|Repair| L[Repair Prompt]
    H -->|Manual| M[Require Manual Intervention]
    
    J --> N{Max Retries?}
    N -->|No| C
    N -->|Yes| O[Store Error]
    
    K --> P{Fallback Success?}
    P -->|Yes| Q[Store Fallback Response]
    P -->|No| O
    
    L --> R{Repair Success?}
    R -->|Yes| S[Retry with Repaired Prompt]
    R -->|No| O
    
    S --> T{Retry Success?}
    T -->|Yes| U[Store Repaired Response]
    T -->|No| O
    
    I --> V[Update Sheet]
    E --> V
    O --> V
    Q --> V
    U --> V
    M --> V
    
    subgraph Error Recovery
        G
        H
        I
        J
        K
        L
        M
        N
        P
        R
        S
        T
    end
```

## Example Error Recovery Configuration

```json
{
  "default_strategy": "retry",
  "max_retries": 3,
  "fallback_model": "gpt-3.5-turbo",
  "strategies_by_error_type": {
    "api_error": "retry",
    "timeout": "retry",
    "rate_limit": "retry",
    "validation": "repair",
    "parsing": "repair",
    "internal": "fallback",
    "unknown": "manual"
  }
}
```

## Context and Considerations

1. **Detailed Error Reporting**: The implementation provides detailed error information in the sheet, making it easier for users to understand and troubleshoot issues.

2. **Configurable Recovery Strategies**: The implementation allows for different recovery strategies based on error types, giving users flexibility in how errors are handled.

3. **Prompt Repair**: The implementation includes a novel approach to automatically repair prompts that cause errors, potentially improving success rates.

4. **Fallback Models**: The implementation supports using fallback models when primary models fail, ensuring that processing can continue even when preferred models are unavailable.

5. **Sheet Integration**: The implementation adds error status columns to the sheet and updates them with detailed error information, making it easier to track and analyze errors.

6. **User Experience**: Clear error messages and recovery status information help users understand what happened and what was done to recover.

7. **Future Enhancements**: 
   - More sophisticated prompt repair techniques
   - Learning from successful recoveries to improve future attempts
   - Integration with monitoring and alerting systems
   - Support for user-defined recovery strategies
