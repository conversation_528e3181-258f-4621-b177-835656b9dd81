# Logging and Monitoring (Part 2)

Original feature description from features_list.md:
```
--> how to handle retries, timeouts, failed runs, passing back errors into sheet.
```

## Feature Description

This feature focuses on advanced error handling and reporting mechanisms in the gsheet-model-breaker-demo tool, specifically:

1. **Retry Mechanisms**: How the system handles retries when LLM API calls fail
2. **Timeout Handling**: Managing timeouts for LLM API calls
3. **Failed Run Reporting**: Tracking and reporting failed runs
4. **Error Feedback**: Passing error information back to the Google Sheet

## Current Implementation Status

The current implementation includes basic retry and error handling mechanisms, but lacks comprehensive timeout management and direct error feedback to the Google Sheet.

### Retry Mechanism Implementation

The retry mechanism is implemented in the `ParallelLLMDataFrameProcessor` class using the `tenacity` library:

<augment_code_snippet path="experiments/src/tools/gsheet-model-breaker-demo/llm_processor/processor.py" mode="EXCERPT">
````python
try:
    if max_attempts > 1:
        foo = tenacity.retry(
            stop=tenacity.stop_after_attempt(max_attempts),
            wait=tenacity.wait_exponential(exp_base=2, multiplier=2),
            after=tenacity.after_log(logger=logger, log_level=1),
        )(self._async_get_response)
        response = await foo(
            message=prompt,
            temperature=temperature,
            max_tokens=max_tokens,
            model=model,
            returns_list=step.fanout,
        )
    else:
        response = await self._async_get_response(
            message=prompt,
            temperature=temperature,
            max_tokens=max_tokens,
            model=model,
            returns_list=step.fanout,
        )
````
</augment_code_snippet>

In main2.py, the retry mechanism is utilized when executing the chain:

<augment_code_snippet path="experiments/src/tools/gsheet-model-breaker-demo/scripts/main2.py" mode="EXCERPT">
````python
result_df = llm_proc.execute_chain(
    chain_input,
    chain,
    max_attempts=3,
)
````
</augment_code_snippet>

### Error Handling Implementation

Error handling is implemented in the `_process_chain_step` method:

<augment_code_snippet path="experiments/src/tools/gsheet-model-breaker-demo/llm_processor/processor.py" mode="EXCERPT">
````python
try:
    # API call code...
except Exception as e:
    error_msg = str(e).replace("\n", " ")
    logger.error(f"Error in _process_chain_step: {error_msg}")
    c_row[step.col] = f"[N/A] [ERROR: {error_msg}]"
else:
    c_row[step.col] = response
````
</augment_code_snippet>

## Gaps in Current Implementation

1. **Limited Timeout Management**: The current implementation doesn't have explicit timeout settings for API calls.
2. **No Direct Error Feedback to Sheet**: While errors are logged and stored in the DataFrame, there's no structured way to pass detailed error information back to the Google Sheet.
3. **No Differentiation of Error Types**: All errors are treated the same way, without distinguishing between different types (API errors, rate limits, etc.).
4. **No Aggregated Error Reporting**: There's no summary of errors across all processed rows.

## Proposed Implementation

### 1. Enhanced Timeout Management

```python
# In processor.py, modify _async_get_response
async def _async_get_response(
    self,
    message: str,
    temperature: float,
    max_tokens: int,
    model: str,
    returns_list: bool = False,
    timeout: float = 60.0,  # Add timeout parameter
    verbose: bool = False,
):
    async with self.throttler:
        start = time.time()
        try:
            messages = [
                {
                    "role": "system",
                    "content": "You are a helpful assistant.",
                },
                {"role": "user", "content": message},
            ]
            # Add timeout to the API call
            resp = await asyncio.wait_for(
                litellm.acompletion(
                    model=model,
                    messages=messages,
                    temperature=temperature,
                    max_tokens=max_tokens,
                ),
                timeout=timeout
            )
            text = resp["choices"][0]["message"]["content"]
        except asyncio.TimeoutError:
            logger.error(f"API call timed out after {timeout} seconds for model {model}")
            raise TimeoutError(f"API call timed out after {timeout} seconds")
        except Exception as e:
            # Existing error handling
```

### 2. Structured Error Feedback

```python
# Add a new class for structured error information
class LLMError(NamedTuple):
    error_type: str  # e.g., "API_ERROR", "TIMEOUT", "RATE_LIMIT"
    error_message: str
    model: str
    timestamp: float
    row_index: int
    column: str
    
# Modify _process_chain_step to use this structure
try:
    # API call code...
except Exception as e:
    error_type = "API_ERROR"
    if isinstance(e, TimeoutError):
        error_type = "TIMEOUT"
    elif "rate limit" in str(e).lower():
        error_type = "RATE_LIMIT"
        
    error = LLMError(
        error_type=error_type,
        error_message=str(e),
        model=model,
        timestamp=time.time(),
        row_index=c_row.name,
        column=step.col
    )
    
    # Store structured error in the DataFrame
    c_row[step.col] = f"[ERROR:{error_type}] {str(e)[:100]}"
    
    # Add to error tracking
    self.errors.append(error)
```

### 3. Error Aggregation and Reporting

```python
# Add to ParallelLLMDataFrameProcessor class
def __init__(self, ...):
    # Existing initialization
    self.errors = []  # Track all errors

def get_error_summary(self):
    """Return a summary of all errors encountered during processing."""
    if not self.errors:
        return "No errors encountered."
        
    error_counts = {}
    for error in self.errors:
        if error.error_type not in error_counts:
            error_counts[error.error_type] = 0
        error_counts[error.error_type] += 1
    
    summary = "Error Summary:\n"
    for error_type, count in error_counts.items():
        summary += f"- {error_type}: {count} occurrences\n"
    
    return summary
```

### 4. Google Sheet Error Feedback

```python
# In main2.py, modify the update_to_sheet function call
# Create a separate error sheet or dedicated error columns

# Add error columns to the update DataFrame
for (row_idx, col_name), (col_ref, value) in direct_update_map.items():
    if value.startswith("[ERROR:"):
        # Extract error type and message
        error_type = value.split("]")[0].replace("[ERROR:", "")
        error_message = value.split("]")[1].strip()
        
        # Add to error columns
        update_rows.append({
            "col_ref": col_ref, 
            col_name: value,
            f"{col_name}_error_type": error_type,
            f"{col_name}_error_details": error_message
        })
    else:
        update_rows.append({"col_ref": col_ref, col_name: value})
```

## Implementation Considerations

1. **Error Formatting**: Standardize error formatting in the sheet to make it easily identifiable (e.g., using color formatting based on error type).

2. **Error Aggregation**: Consider creating a separate sheet tab for error summaries, showing aggregated error statistics.

3. **Retry Configuration**: Allow configuring retry parameters (attempts, backoff) through the sheet configuration rows.

4. **Timeout Configuration**: Add timeout settings to the configuration rows in the sheet.

5. **Error Recovery**: Implement mechanisms to recover from certain types of errors automatically.

## Mermaid Diagram: Error Handling Flow

```mermaid
flowchart TD
    A[LLM API Call] --> B{Success?}
    B -->|Yes| C[Process Response]
    B -->|No| D[Classify Error]
    D --> E{Retryable?}
    E -->|Yes| F{Max Attempts<br>Reached?}
    F -->|No| G[Exponential<br>Backoff]
    G --> A
    F -->|Yes| H[Record Final Error]
    E -->|No| H
    H --> I[Format Error for Sheet]
    I --> J[Update DataFrame]
    J --> K[Write to Google Sheet]
    
    subgraph Error Classification
    D1[API Error] 
    D2[Timeout Error]
    D3[Rate Limit Error]
    D4[Content Filter Error]
    end
    
    D --> D1
    D --> D2
    D --> D3
    D --> D4
```

## Conclusion

Enhancing the error handling, retry mechanisms, and error reporting capabilities of the gsheet-model-breaker-demo tool would significantly improve its reliability and user experience. By implementing structured error feedback directly in the Google Sheet, users would have better visibility into issues and could more easily troubleshoot problems.

The proposed implementation builds on the existing retry mechanism while adding more sophisticated timeout handling, error classification, and reporting features. This would make the tool more robust when dealing with the inherent uncertainties of LLM API calls.
