# Feature: Error Handling and Retries (Part 1)

Original requirement:
> --> how to handle retries, timeouts, failed runs, passing back errors into sheet.

## Implementation Status: Partially Implemented

The current implementation in `main2.py` and `processor.py` includes basic error handling and retries for LLM API calls using the Tenacity library. However, it doesn't have comprehensive handling for all types of errors, doesn't pass detailed error information back to the sheet, and has limited timeout handling.

## Feature Description

This feature would enhance the error handling and retry mechanisms in the system to make it more robust and informative. It would provide better handling of various error types, implement configurable retry strategies, manage timeouts effectively, and pass detailed error information back to the sheet for user visibility.

In Part 1, we'll focus on the current implementation and the enhancements for error detection and retry mechanisms.

## Current Implementation

The current implementation uses Tenacity for retries in the `_process_chain_step` method in `processor.py`:

```python
# From processor.py
if max_attempts > 1:
    foo = tenacity.retry(
        stop=tenacity.stop_after_attempt(max_attempts),
        wait=tenacity.wait_exponential(exp_base=2, multiplier=2),
        after=tenacity.after_log(logger=logger, log_level=1),
    )(self._async_get_response)
    response = await foo(
        message=prompt,
        temperature=temperature,
        max_tokens=max_tokens,
        model=model,
        returns_list=step.fanout,
    )
else:
    response = await self._async_get_response(
        message=prompt,
        temperature=temperature,
        max_tokens=max_tokens,
        model=model,
        returns_list=step.fanout,
    )
```

And basic error handling:

```python
# From processor.py
try:
    # ... LLM API call ...
except Exception as e:
    error_msg = str(e).replace("\n", " ")
    logger.error(f"Error in _process_chain_step: {error_msg}")
    c_row[step.col] = f"[N/A] [ERROR: {error_msg}]"
```

## Implementation Recommendation: Error Detection and Retry Mechanisms

### 1. Enhanced Error Classification

```python
from enum import Enum
from typing import Optional, Tuple, Dict, Any

class ErrorType(Enum):
    """Types of errors that can occur during processing."""
    API_ERROR = "api_error"  # Error from the LLM API
    TIMEOUT = "timeout"      # Request timed out
    RATE_LIMIT = "rate_limit"  # Rate limit exceeded
    VALIDATION = "validation"  # Response validation failed
    PARSING = "parsing"      # Response parsing failed
    INTERNAL = "internal"    # Internal error
    UNKNOWN = "unknown"      # Unknown error

class ProcessingError:
    """
    Represents an error that occurred during processing.
    """
    
    def __init__(
        self,
        error_type: ErrorType,
        message: str,
        exception: Optional[Exception] = None,
        details: Optional[Dict[str, Any]] = None,
        retry_count: int = 0,
        is_retryable: bool = True,
    ):
        """
        Initialize a processing error.
        
        Args:
            error_type: Type of error
            message: Error message
            exception: Original exception
            details: Additional details about the error
            retry_count: Number of retries attempted
            is_retryable: Whether the error is retryable
        """
        self.error_type = error_type
        self.message = message
        self.exception = exception
        self.details = details or {}
        self.retry_count = retry_count
        self.is_retryable = is_retryable
        self.timestamp = time.time()
    
    def __str__(self) -> str:
        """String representation of the error."""
        return f"{self.error_type.value}: {self.message}"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage or display."""
        return {
            "type": self.error_type.value,
            "message": self.message,
            "details": self.details,
            "retry_count": self.retry_count,
            "is_retryable": self.is_retryable,
            "timestamp": self.timestamp,
        }
    
    def to_sheet_value(self) -> str:
        """Format the error for display in a sheet cell."""
        if self.retry_count > 0:
            return f"[ERROR] [{self.error_type.value}] {self.message} (Retries: {self.retry_count})"
        else:
            return f"[ERROR] [{self.error_type.value}] {self.message}"
    
    @classmethod
    def from_exception(cls, e: Exception, retry_count: int = 0) -> 'ProcessingError':
        """
        Create a ProcessingError from an exception.
        
        Args:
            e: Exception to convert
            retry_count: Number of retries attempted
            
        Returns:
            ProcessingError instance
        """
        error_type = ErrorType.UNKNOWN
        message = str(e)
        is_retryable = True
        details = {}
        
        # Classify based on exception type and message
        error_class = e.__class__.__name__
        
        if "timeout" in message.lower() or error_class.lower().endswith("timeout"):
            error_type = ErrorType.TIMEOUT
        elif "rate limit" in message.lower() or "too many requests" in message.lower():
            error_type = ErrorType.RATE_LIMIT
        elif "validation" in message.lower():
            error_type = ErrorType.VALIDATION
            is_retryable = False
        elif "parse" in message.lower() or "json" in message.lower():
            error_type = ErrorType.PARSING
            is_retryable = False
        elif hasattr(e, "status_code") or "status code" in message.lower():
            error_type = ErrorType.API_ERROR
            
            # Extract status code if available
            status_code = getattr(e, "status_code", None)
            if status_code:
                details["status_code"] = status_code
                
                # Determine if retryable based on status code
                if status_code >= 500:  # Server errors are retryable
                    is_retryable = True
                elif status_code == 429:  # Rate limit is retryable
                    error_type = ErrorType.RATE_LIMIT
                    is_retryable = True
                elif status_code >= 400:  # Client errors are not retryable
                    is_retryable = False
        
        return cls(
            error_type=error_type,
            message=message,
            exception=e,
            details=details,
            retry_count=retry_count,
            is_retryable=is_retryable,
        )
```

### 2. Enhanced Retry Strategy

```python
import tenacity
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type, retry_if_result
from tenacity.wait import wait_random_exponential

def is_retryable_error(exception: Exception) -> bool:
    """
    Check if an exception is retryable.
    
    Args:
        exception: Exception to check
        
    Returns:
        bool: True if the exception is retryable
    """
    error = ProcessingError.from_exception(exception)
    return error.is_retryable

def create_retry_decorator(
    max_attempts: int = 3,
    min_wait: float = 1.0,
    max_wait: float = 60.0,
    exponential_base: float = 2.0,
    jitter: bool = True,
) -> callable:
    """
    Create a retry decorator with the specified parameters.
    
    Args:
        max_attempts: Maximum number of retry attempts
        min_wait: Minimum wait time between retries (seconds)
        max_wait: Maximum wait time between retries (seconds)
        exponential_base: Base for exponential backoff
        jitter: Whether to add jitter to wait times
        
    Returns:
        Retry decorator
    """
    # Define wait strategy
    if jitter:
        wait_strategy = wait_random_exponential(
            multiplier=min_wait,
            exp_base=exponential_base,
            max=max_wait,
        )
    else:
        wait_strategy = wait_exponential(
            multiplier=min_wait,
            exp_base=exponential_base,
            max=max_wait,
        )
    
    # Create retry decorator
    return retry(
        retry=retry_if_exception_type(Exception) & tenacity.retry_if_not_result(is_retryable_error),
        stop=stop_after_attempt(max_attempts),
        wait=wait_strategy,
        before_sleep=lambda retry_state: logger.info(
            f"{Fore.YELLOW}Retry {retry_state.attempt_number}/{max_attempts} after error: {retry_state.outcome.exception()}{Style.RESET_ALL}"
        ),
        reraise=True,
    )
```

### 3. Timeout Handling

```python
import asyncio
from functools import wraps

async def with_timeout(coro, timeout_seconds: float, timeout_message: str = "Operation timed out"):
    """
    Execute a coroutine with a timeout.
    
    Args:
        coro: Coroutine to execute
        timeout_seconds: Timeout in seconds
        timeout_message: Message to use in timeout exception
        
    Returns:
        Result of the coroutine
        
    Raises:
        asyncio.TimeoutError: If the operation times out
    """
    try:
        return await asyncio.wait_for(coro, timeout=timeout_seconds)
    except asyncio.TimeoutError:
        raise asyncio.TimeoutError(timeout_message)

def timeout_decorator(timeout_seconds: float, timeout_message: str = "Operation timed out"):
    """
    Decorator to add timeout to an async function.
    
    Args:
        timeout_seconds: Timeout in seconds
        timeout_message: Message to use in timeout exception
        
    Returns:
        Decorated function
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            return await with_timeout(func(*args, **kwargs), timeout_seconds, timeout_message)
        return wrapper
    return decorator
```

### 4. Update LLM Processor to Use Enhanced Error Handling

```python
async def _async_get_response_with_error_handling(
    self,
    message: str,
    temperature: float,
    max_tokens: int,
    model: str,
    returns_list: bool = False,
    timeout_seconds: float = 60.0,
    verbose: bool = False,
) -> Union[str, List[str]]:
    """
    Get a response from the LLM model asynchronously with enhanced error handling.
    
    Args:
        message: The prompt message
        temperature: Temperature setting
        max_tokens: Maximum tokens
        model: Model name
        returns_list: Whether to return a list
        timeout_seconds: Timeout in seconds
        verbose: Verbose logging
        
    Returns:
        Response from the LLM
        
    Raises:
        ProcessingError: If an error occurs
    """
    async with self.throttler:
        start = time.time()
        try:
            messages = [
                {
                    "role": "system",
                    "content": "You are a helpful assistant.",
                },
                {"role": "user", "content": message},
            ]
            
            # Execute with timeout
            resp = await with_timeout(
                litellm.acompletion(
                    model=model,
                    messages=messages,
                    temperature=temperature,
                    max_tokens=max_tokens,
                ),
                timeout_seconds=timeout_seconds,
                timeout_message=f"LLM request to {model} timed out after {timeout_seconds} seconds",
            )
            
            text = resp["choices"][0]["message"]["content"]
            
            # Handle list parsing if requested
            if returns_list:
                try:
                    text_list = await self._fanout_list(text)
                    logger.info(
                        f"Prompt: ['{text_list[0]}',...,'{text_list[-1]}'] len={len(text_list)} ✅ in {time.time() - start:.0f}s using {model}"
                    )
                    return text_list
                except ValueError as e:
                    # Convert to ProcessingError
                    raise ProcessingError(
                        error_type=ErrorType.PARSING,
                        message=f"Failed to parse list response: {str(e)}",
                        exception=e,
                        is_retryable=False,
                    )
            else:
                logger.info(
                    f"Prompt: '{text[:10]}...{text[-10:]}' ✅ in {time.time() - start:.0f}s using {model}"
                )
                return text
                
        except asyncio.TimeoutError as e:
            # Handle timeout
            error = ProcessingError(
                error_type=ErrorType.TIMEOUT,
                message=str(e),
                exception=e,
                details={"model": model, "timeout_seconds": timeout_seconds},
                is_retryable=True,
            )
            logger.error(
                f"{Fore.RED}Timeout error: {error.message}{Style.RESET_ALL}"
            )
            raise error
            
        except Exception as e:
            # Handle other errors
            error_type = type(e).__name__
            error_message = str(e)
            
            # Convert to ProcessingError
            error = ProcessingError.from_exception(e)
            
            logger.error(
                f"{Fore.RED}Failed API call model={model}: {error_type} - {error_message}{Style.RESET_ALL}"
            )
            
            if verbose:
                logger.error(
                    f"{Fore.RED}Full traceback: {traceback.format_exc()}{Style.RESET_ALL}"
                )
                
            raise error
```

### 5. Update Process Chain Step to Use Enhanced Error Handling

```python
async def _process_chain_step(
    self,
    c_row: pd.Series,
    step: ChainStep,
    mapping: dict,
    max_attempts: int,
    timeout_seconds: float = 60.0,
) -> None:
    """
    Process a single chain step for a given row with enhanced error handling.
    
    Args:
        c_row (pd.Series): The current row of the DataFrame.
        step (ChainStep): The chain step to process.
        mapping (dict): The mapping of prompt keywords to column names.
        max_attempts (int): The maximum number of retry attempts for failed API calls.
        timeout_seconds (float): Timeout in seconds for LLM requests.
    """
    prompt = c_row.get(step.pt)
    if c_row.get(step.col) and not step.overwrite:
        logger.debug(f"{step.col} already exists (skip)")
        return
        
    for k, v in mapping.items():
        prompt = prompt.replace(f"{{{k}}}", str(c_row.get(v, "N/A")))

    temperature = (
        c_row.get("__temperature")
        or step.temperature
        or self.def_temperature
    )
    max_tokens = (
        c_row.get("__max_tokens") or step.max_tokens or self.def_max_tokens
    )
    model = c_row.get("__model") or step.model or self.def_model

    # Create retry decorator
    retry_decorator = create_retry_decorator(
        max_attempts=max_attempts,
        min_wait=1.0,
        max_wait=30.0,
        exponential_base=2.0,
        jitter=True,
    )
    
    # Apply retry decorator to the response function
    get_response_with_retry = retry_decorator(self._async_get_response_with_error_handling)
    
    try:
        # Execute with retry
        response = await get_response_with_retry(
            message=prompt,
            temperature=temperature,
            max_tokens=max_tokens,
            model=model,
            returns_list=step.fanout,
            timeout_seconds=timeout_seconds,
        )
        
        # Store successful response
        c_row[step.col] = response
        
        # Store success status
        c_row[f"{step.col}_status"] = "success"
        
    except ProcessingError as e:
        # Handle processing error
        error_msg = e.to_sheet_value()
        logger.error(f"Error in _process_chain_step: {error_msg}")
        
        # Store error in the output column
        c_row[step.col] = error_msg
        
        # Store error details in status columns
        c_row[f"{step.col}_status"] = "error"
        c_row[f"{step.col}_error_type"] = e.error_type.value
        c_row[f"{step.col}_error_message"] = e.message
        c_row[f"{step.col}_retry_count"] = e.retry_count
        
    except Exception as e:
        # Handle unexpected exceptions
        error = ProcessingError.from_exception(e)
        error_msg = error.to_sheet_value()
        logger.error(f"Unexpected error in _process_chain_step: {error_msg}")
        
        # Store error in the output column
        c_row[step.col] = error_msg
        
        # Store error details in status columns
        c_row[f"{step.col}_status"] = "error"
        c_row[f"{step.col}_error_type"] = error.error_type.value
        c_row[f"{step.col}_error_message"] = error.message
```

## Architecture Diagram

```mermaid
classDiagram
    class ErrorType {
        <<enumeration>>
        API_ERROR
        TIMEOUT
        RATE_LIMIT
        VALIDATION
        PARSING
        INTERNAL
        UNKNOWN
    }
    
    class ProcessingError {
        +ErrorType error_type
        +str message
        +Exception exception
        +Dict details
        +int retry_count
        +bool is_retryable
        +float timestamp
        +__str__()
        +to_dict()
        +to_sheet_value()
        +from_exception(e, retry_count)
    }
    
    class RetryStrategy {
        +create_retry_decorator(max_attempts, min_wait, max_wait, exponential_base, jitter)
        +is_retryable_error(exception)
    }
    
    class TimeoutHandler {
        +with_timeout(coro, timeout_seconds, timeout_message)
        +timeout_decorator(timeout_seconds, timeout_message)
    }
    
    class LLMProcessor {
        +_async_get_response_with_error_handling(message, temperature, max_tokens, model, returns_list, timeout_seconds, verbose)
        +_process_chain_step(c_row, step, mapping, max_attempts, timeout_seconds)
    }
    
    ErrorType <-- ProcessingError : uses
    ProcessingError <-- RetryStrategy : uses
    TimeoutHandler <-- LLMProcessor : uses
    RetryStrategy <-- LLMProcessor : uses
    ProcessingError <-- LLMProcessor : uses
```

## Context and Considerations

1. **Error Classification**: The implementation classifies errors into different types, making it easier to handle them appropriately and provide meaningful feedback to users.
2. **Retry Strategy**: The enhanced retry strategy uses exponential backoff with jitter and only retries errors that are actually retryable, improving efficiency and reliability.
3. **Timeout Handling**: The implementation adds explicit timeout handling to prevent requests from hanging indefinitely.
4. **Error Reporting**: The implementation stores detailed error information in the DataFrame and formats it for display in the sheet, making it easier for users to understand and troubleshoot issues.
5. **Logging**: The implementation includes comprehensive logging of errors and retry attempts, making it easier to diagnose issues.
6. **Integration with Existing Code**: The implementation builds on the existing error handling and retry mechanisms, maintaining compatibility with the current code.
7. **Future Enhancements**: 
   - More sophisticated error classification based on LLM provider-specific error codes
   - Adaptive retry strategies based on error types and patterns
   - Circuit breaker pattern to prevent overwhelming failing services
   - Error aggregation and reporting across multiple runs