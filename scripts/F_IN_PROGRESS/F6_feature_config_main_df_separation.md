# Feature: Config and Main DataFrame Separation

Original requirement:
> Handling the Config Rows
> Split handling of config and main df
> "DF TERMINOLOGY: Apply = take a function and apply it to every row in a DF
> Take worksheet.get(""A1:MaxCol"")"
> header row = header row
> [r2-20] -> df_config
> df_1 = remove_r2-20(df_1)

## Implementation Status: Partially Implemented

The current implementation in `main2.py` does separate the configuration rows from the data rows, but it doesn't create distinct DataFrames for configuration and data. Instead, it extracts configuration values directly from specific rows and processes data rows starting from row 20.

## Feature Description

This feature would improve the handling of configuration and data in the Google Sheet by:

1. Creating separate DataFrames for configuration and data
2. Clearly defining the structure of the configuration section (rows 2-20)
3. Making it easier to add, modify, and access configuration parameters
4. Improving code readability and maintainability by separating concerns

## Current Implementation

The current code extracts configuration from specific rows:

```python
# From main2.py
prompt_templates = list(
    df.iloc[[3]][prompt_output_cols].to_dict("records")[0].values()
)
models = list(df.iloc[[4]][prompt_output_cols].to_dict("records")[0].values())
temperatures = list(
    map(
        float,
        df.iloc[[5]][prompt_output_cols].to_dict("records")[0].values(),
    )
)
max_output_tokens = list(
    map(
        int,
        df.iloc[[6]][prompt_output_cols].to_dict("records")[0].values(),
    )
)
flags = list(df.iloc[[7]][prompt_output_cols].to_dict("records")[0].values())

prompt_inputs = (
    df[prompt_input_cols]
    .iloc[19:]
    .replace("", pd.NA)
    .dropna(how="all", ignore_index=True)
)
```

## Implementation Recommendation

### 1. Split DataFrame into Config and Data

```python
def split_sheet_data(df):
    """
    Split the sheet data into configuration and data DataFrames.
    
    Args:
        df: DataFrame with the full sheet data
        
    Returns:
        Tuple of (df_config, df_data)
    """
    # Extract header row (row 1)
    header = df.iloc[0].tolist()
    
    # Create configuration DataFrame (rows 2-20)
    df_config = df.iloc[1:20].copy()
    df_config.columns = header
    
    # Create data DataFrame (rows 21+)
    df_data = df.iloc[20:].copy()
    df_data.columns = header
    df_data = df_data.reset_index(drop=True)
    
    return df_config, df_data
```

### 2. Create Configuration Class

```python
class SheetConfig:
    """
    Class to handle sheet configuration.
    """
    
    def __init__(self, df_config):
        """
        Initialize with configuration DataFrame.
        
        Args:
            df_config: DataFrame with configuration data (rows 2-20)
        """
        self.df = df_config
        self._parse_config()
    
    def _parse_config(self):
        """Parse configuration from DataFrame."""
        # Find column references
        self.input_cols = [
            key for key, value in self.df.iloc[0].to_dict().items() if "inputs" in str(value)
        ]
        self.output_cols = [
            key for key, value in self.df.iloc[0].to_dict().items() if "py-llm" in str(value)
        ]
        
        # Extract configuration for each output column
        self.column_config = {}
        for col in self.output_cols:
            self.column_config[col] = self._extract_column_config(col)
    
    def _extract_column_config(self, column):
        """
        Extract configuration for a specific column.
        
        Args:
            column: Column name
            
        Returns:
            Dictionary with column configuration
        """
        # Find configuration rows by looking at first column
        config_rows = {}
        for i, row in self.df.iterrows():
            row_name = row.iloc[0]
            if pd.notna(row_name) and isinstance(row_name, str):
                config_rows[row_name.strip().upper()] = i
        
        # Extract configuration values
        config = {}
        
        # Extract template (row 3 or TEMPLATE row)
        template_row = config_rows.get("TEMPLATE", 2)  # Default to row 3 (index 2)
        config["template"] = self.df.loc[template_row, column] if column in self.df.columns else ""
        
        # Extract model (row 4 or MODEL row)
        model_row = config_rows.get("MODEL", 3)  # Default to row 4 (index 3)
        config["model"] = self.df.loc[model_row, column] if column in self.df.columns else "gpt-3.5-turbo"
        
        # Extract temperature (row 5 or TEMPERATURE row)
        temp_row = config_rows.get("TEMPERATURE", 4)  # Default to row 5 (index 4)
        temp_value = self.df.loc[temp_row, column] if column in self.df.columns else 0.7
        try:
            config["temperature"] = float(temp_value)
        except (ValueError, TypeError):
            config["temperature"] = 0.7
        
        # Extract max tokens (row 6 or MAX_TOKENS row)
        tokens_row = config_rows.get("MAX_TOKENS", 5)  # Default to row 6 (index 5)
        tokens_value = self.df.loc[tokens_row, column] if column in self.df.columns else 1000
        try:
            config["max_tokens"] = int(tokens_value)
        except (ValueError, TypeError):
            config["max_tokens"] = 1000
        
        # Extract flags (row 7 or FLAGS row)
        flags_row = config_rows.get("FLAGS", 6)  # Default to row 7 (index 6)
        config["flags"] = self.df.loc[flags_row, column] if column in self.df.columns else ""
        
        return config
    
    def get_templates(self):
        """Get templates for all output columns."""
        return [self.column_config[col]["template"] for col in self.output_cols]
    
    def get_models(self):
        """Get models for all output columns."""
        return [self.column_config[col]["model"] for col in self.output_cols]
    
    def get_temperatures(self):
        """Get temperatures for all output columns."""
        return [self.column_config[col]["temperature"] for col in self.output_cols]
    
    def get_max_tokens(self):
        """Get max tokens for all output columns."""
        return [self.column_config[col]["max_tokens"] for col in self.output_cols]
    
    def get_flags(self):
        """Get flags for all output columns."""
        return [self.column_config[col]["flags"] for col in self.output_cols]
    
    def get_input_columns(self):
        """Get input column names."""
        return self.input_cols
    
    def get_output_columns(self):
        """Get output column names."""
        return self.output_cols
```

### 3. Update Main Function to Use Separated DataFrames

```python
def main():
    """Main execution function with enhanced quality checks."""
    sheet_id = get_sheet_id_from_url(URL_PATH)
    logger.info(f"{Fore.GREEN}Sheet ID: {sheet_id}{Style.RESET_ALL}")

    # Define the sheet name to use
    sheet_name = "Sheet1"

    # Get data from sheets
    df = get_data_from_sheet(sheet_id, range_name=sheet_name)
    if len(df) == 0:
        logger.info("No prompts to generate")
        sys.exit(0)

    logger.info(
        f"{Fore.CYAN}Debug: DataFrame loaded with shape {df.shape}{Style.RESET_ALL}"
    )

    # Split into configuration and data DataFrames
    df_config, df_data = split_sheet_data(df)
    
    # Create configuration object
    config = SheetConfig(df_config)
    
    # Get configuration values
    prompt_input_cols = config.get_input_columns()
    prompt_output_cols = config.get_output_columns()
    prompt_templates = config.get_templates()
    models = config.get_models()
    temperatures = config.get_temperatures()
    max_output_tokens = config.get_max_tokens()
    flags = config.get_flags()

    logger.info(
        f"{Fore.CYAN}Debug: Input columns: {prompt_input_cols}{Style.RESET_ALL}"
    )
    logger.info(
        f"{Fore.CYAN}Debug: Output columns: {prompt_output_cols}{Style.RESET_ALL}"
    )

    # Process data DataFrame
    prompt_inputs = (
        df_data[prompt_input_cols]
        .replace("", pd.NA)
        .dropna(how="all", ignore_index=True)
    )
    
    # Add col_ref column
    if "col_ref" in df_data.columns:
        prompt_inputs["col_ref"] = df_data["col_ref"].reset_index(drop=True).iloc[: len(prompt_inputs)]

    # Check that col_ref was properly extracted
    logger.info(
        f"{Fore.CYAN}Debug: First few col_ref values: {prompt_inputs['col_ref'].head()}{Style.RESET_ALL}"
    )

    # ... continue with existing code ...
```

### 4. Add Support for Custom Configuration Rows

```python
def get_custom_config_value(df_config, row_name, column, default_value=None):
    """
    Get a custom configuration value from the config DataFrame.
    
    Args:
        df_config: Configuration DataFrame
        row_name: Name of the configuration row (in column A)
        column: Column name to get the value from
        default_value: Default value if not found
        
    Returns:
        Configuration value or default
    """
    # Find row with the given name in column A
    matching_rows = df_config[df_config.iloc[:, 0] == row_name]
    
    if matching_rows.empty:
        return default_value
    
    # Get the value from the first matching row
    row_idx = matching_rows.index[0]
    
    if column in df_config.columns:
        value = df_config.loc[row_idx, column]
        if pd.isna(value):
            return default_value
        return value
    
    return default_value
```

## Architecture Diagram

```mermaid
classDiagram
    class SheetData {
        +DataFrame full_df
        +split_sheet_data()
    }
    
    class SheetConfig {
        +DataFrame df_config
        +List input_cols
        +List output_cols
        +Dict column_config
        +_parse_config()
        +_extract_column_config(column)
        +get_templates()
        +get_models()
        +get_temperatures()
        +get_max_tokens()
        +get_flags()
        +get_input_columns()
        +get_output_columns()
    }
    
    class DataProcessor {
        +DataFrame df_data
        +SheetConfig config
        +process_data()
        +fill_template(row, template, input_cols)
        +execute_llm_chain()
        +update_sheet()
    }
    
    SheetData --> SheetConfig : Creates
    SheetData --> DataProcessor : Creates
    DataProcessor --> SheetConfig : Uses
```

## Process Flow

```mermaid
flowchart TD
    A[Load Sheet Data] --> B[Split into Config and Data]
    B --> C1[Create Config Object]
    B --> C2[Process Data Rows]
    
    C1 --> D1[Extract Input/Output Columns]
    C1 --> D2[Extract Templates]
    C1 --> D3[Extract Models]
    C1 --> D4[Extract Parameters]
    
    D1 --> E[Process Columns]
    D2 --> E
    D3 --> E
    D4 --> E
    C2 --> E
    
    E --> F[Update Sheet]
```

## Context and Considerations

1. **Configuration Flexibility**: The implementation should support both fixed-row configuration (e.g., row 4 for models) and named-row configuration (e.g., "MODEL" in column A).
2. **Backward Compatibility**: The implementation should work with existing sheets that follow the current format.
3. **Code Organization**: Separating configuration and data handling improves code organization and maintainability.
4. **Error Handling**: The implementation should handle missing or invalid configuration gracefully, using sensible defaults.
5. **Documentation**: Clear documentation on the expected sheet format and configuration options would help users set up their sheets correctly.
6. **Future Enhancements**: 
   - Support for more configuration options
   - Configuration validation
   - Configuration inheritance or overrides
   - Support for configuration sections or groups