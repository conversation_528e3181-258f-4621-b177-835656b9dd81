# Feature: Input Column Validation

Original requirement:
> * Running python script checks that all required input cols are present, else error

## Implementation Status: Partially Implemented

The current implementation in `main2.py` identifies input and output columns but doesn't explicitly validate that all required input columns are present before processing. It does detect input columns but doesn't check if they match any expected schema or required set.

## Feature Description

This feature would ensure that the script validates the presence of all required input columns before processing data. If any required columns are missing, the script should exit with an appropriate error message. This validation helps prevent runtime errors and ensures data quality.

## Current Implementation

The current code identifies input columns based on the first row of the sheet:

```python
# From main2.py
df_col_ref = df.iloc[[0]]
# find the column reference for the prompt input
prompt_input_cols = [
    key for key, value in df_col_ref.to_dict("list").items() if "inputs" in value
]
# find the column reference for the prompt output
prompt_output_cols = [
    key for key, value in df_col_ref.to_dict("list").items() if "py-llm" in value
]

logger.info(
    f"{Fore.CYAN}Debug: Input columns: {prompt_input_cols}{Style.RESET_ALL}"
)
logger.info(
    f"{Fore.CYAN}Debug: Output columns: {prompt_output_cols}{Style.RESET_ALL}"
)
```

However, it doesn't check if specific required columns are present.

## Implementation Recommendation

### 1. Define Required Columns

Add a configuration section to define which columns are required:

```python
# Configuration for required columns
REQUIRED_INPUT_COLUMNS = [
    # List of column names that must be present
    # These could be hardcoded or loaded from a config file
]

# Or a more flexible approach with column groups
COLUMN_REQUIREMENTS = {
    "basic": ["name", "age", "occupation"],  # Basic required columns
    "advanced": ["education", "skills"],     # Additional columns for advanced features
    # Add more groups as needed
}
```

### 2. Add Validation Function

```python
def validate_input_columns(df, required_columns=None, column_groups=None):
    """
    Validate that all required input columns are present in the DataFrame.
    
    Args:
        df: DataFrame to validate
        required_columns: List of column names that must be present
        column_groups: Dictionary of column groups to validate
        
    Returns:
        bool: True if validation passes, False otherwise
        
    Raises:
        ValueError: If required columns are missing
    """
    df_col_ref = df.iloc[[0]]
    # find the column reference for the prompt input
    prompt_input_cols = [
        key for key, value in df_col_ref.to_dict("list").items() if "inputs" in value
    ]
    
    logger.info(f"{Fore.CYAN}Debug: Input columns: {prompt_input_cols}{Style.RESET_ALL}")
    
    # Validate required columns
    if required_columns:
        missing_columns = [col for col in required_columns if col not in prompt_input_cols]
        if missing_columns:
            error_msg = f"Missing required input columns: {missing_columns}"
            logger.error(f"{Fore.RED}{error_msg}{Style.RESET_ALL}")
            raise ValueError(error_msg)
    
    # Validate column groups
    if column_groups:
        validation_results = {}
        for group_name, group_columns in column_groups.items():
            missing_in_group = [col for col in group_columns if col not in prompt_input_cols]
            validation_results[group_name] = {
                "valid": len(missing_in_group) == 0,
                "missing": missing_in_group
            }
            
            if missing_in_group:
                logger.warning(
                    f"{Fore.YELLOW}Column group '{group_name}' is missing columns: {missing_in_group}{Style.RESET_ALL}"
                )
        
        return validation_results
    
    return True
```

### 3. Integrate Validation in Main Function

```python
def main():
    # ... existing code ...
    
    # Get data from sheets
    df = get_data_from_sheet(sheet_id, range_name=sheet_name)
    if len(df) == 0:
        logger.info("No prompts to generate")
        sys.exit(0)
    
    # Validate input columns
    try:
        validate_input_columns(df, required_columns=REQUIRED_INPUT_COLUMNS)
        logger.info(f"{Fore.GREEN}All required input columns are present.{Style.RESET_ALL}")
    except ValueError as e:
        logger.error(f"{Fore.RED}Validation error: {str(e)}{Style.RESET_ALL}")
        sys.exit(1)
    
    # ... continue with existing code ...
```

### 4. Add Template Validation

Since templates use input columns, we should also validate that all template variables reference existing columns:

```python
def validate_template_variables(df, prompt_templates, prompt_input_cols):
    """
    Validate that all template variables reference existing input columns.
    
    Args:
        df: DataFrame with the data
        prompt_templates: List of prompt templates
        prompt_input_cols: List of available input columns
        
    Returns:
        bool: True if validation passes
        
    Raises:
        ValueError: If template variables reference non-existent columns
    """
    all_valid = True
    
    for template_idx, template in enumerate(prompt_templates):
        # Extract variables from template using regex
        variables = re.findall(r"\{\{([^}]+)\}\}", template)
        
        # Check if variables exist in input columns
        missing_vars = [var for var in variables if var not in prompt_input_cols]
        
        if missing_vars:
            logger.error(
                f"{Fore.RED}Template {template_idx+1} references non-existent columns: {missing_vars}{Style.RESET_ALL}"
            )
            logger.error(f"{Fore.RED}Template: {template}{Style.RESET_ALL}")
            all_valid = False
    
    if not all_valid:
        raise ValueError("Template validation failed. Some templates reference non-existent columns.")
    
    return True
```

## Architecture Diagram

```mermaid
flowchart TD
    A[Start Script] --> B[Load Sheet Data]
    B --> C{Validate Input Columns}
    C -->|Missing Required Columns| D[Exit with Error]
    C -->|All Required Columns Present| E[Extract Templates]
    E --> F{Validate Template Variables}
    F -->|Invalid Template Variables| G[Exit with Error]
    F -->|All Template Variables Valid| H[Process Data]
    H --> I[Update Sheet]
    
    subgraph Validation Process
        C
        F
    end
```

## Context and Considerations

1. **Configuration Flexibility**: The implementation should allow for different levels of validation strictness, from requiring specific columns to just warning about missing columns.
2. **Template Validation**: Since templates use variables from input columns, validating that all template variables reference existing columns is important to prevent runtime errors.
3. **Dynamic Requirements**: In some cases, the required columns might depend on which output columns are being processed. The validation should account for this.
4. **Error Messages**: Clear error messages help users understand what's missing and how to fix it.
5. **Integration with Existing Code**: The validation should be integrated early in the process to fail fast if requirements aren't met.
6. **Future Enhancements**: 
   - Data type validation for input columns
   - Value range validation for numeric columns
   - Format validation for date/time columns
   - Required vs. optional column distinction