# Feature: Rate Limiting and Fallback Models

Original requirement:
> semaphore, rate limiting and fallback models. Annotate fallbacks with <fb> full output </fb> //fb = model_name

## Implementation Status: Partially Implemented

The current implementation in `processor.py` includes basic rate limiting using the `asyncio_throttle` library, but it doesn't have comprehensive support for fallback models or annotation of fallback outputs. The code uses a throttler to limit the rate of API calls, but it doesn't have a mechanism to automatically switch to fallback models when rate limits are exceeded or when primary models fail.

## Feature Description

This feature would enhance the rate limiting capabilities and add support for fallback models. It would provide:

1. More sophisticated rate limiting based on provider-specific limits
2. Automatic switching to fallback models when rate limits are exceeded
3. Annotation of outputs generated by fallback models
4. Configuration options for rate limits and fallback models

This would make the system more resilient to API rate limits and more transparent about which model generated each output.

## Current Implementation

The current implementation uses a simple throttler for rate limiting:

```python
# From processor.py
def __init__(
    self,
    def_model: str = "gpt-4o",
    def_temperature: float = 0.0,
    def_max_tokens: int = 8192,
    def_async_rate_limit: int = 10,
    def_thread_rate_limit: int = 10,
):
    # ...
    self.throttler = Throttler(
        rate_limit=def_async_rate_limit, period=1.0, retry_interval=0.1
    )
    # ...

async def _async_get_response(
    self,
    message: str,
    temperature: float,
    max_tokens: int,
    model: str,
    returns_list: bool = False,
    verbose: bool = False,
) -> Union[str, List[str]]:
    async with self.throttler:
        # Process LLM request...
```

## Implementation Recommendation

### 1. Enhanced Rate Limiting

```python
from asyncio_throttle import Throttler
from typing import Dict, Optional, Union, List, Tuple
import time
import asyncio

class ProviderRateLimits:
    """
    Rate limits for different LLM providers.
    """
    
    # Default rate limits (requests per minute)
    DEFAULT_LIMITS = {
        "openai": 60,      # OpenAI default tier
        "anthropic": 40,   # Anthropic default tier
        "google": 60,      # Google default tier
        "mistral": 60,     # Mistral default tier
        "cohere": 60,      # Cohere default tier
        "default": 30,     # Default for unknown providers
    }
    
    # Mapping of model prefixes to providers
    MODEL_TO_PROVIDER = {
        "gpt-": "openai",
        "text-davinci-": "openai",
        "claude-": "anthropic",
        "gemini-": "google",
        "mistral-": "mistral",
        "command-": "cohere",
    }
    
    @classmethod
    def get_provider(cls, model: str) -> str:
        """
        Get the provider for a model.
        
        Args:
            model: Model name
            
        Returns:
            Provider name
        """
        model_lower = model.lower()
        
        # Check for explicit provider prefix (litellm format)
        if "/" in model_lower:
            provider, _ = model_lower.split("/", 1)
            return provider
        
        # Check model prefixes
        for prefix, provider in cls.MODEL_TO_PROVIDER.items():
            if model_lower.startswith(prefix):
                return provider
        
        # Default to unknown provider
        return "default"
    
    @classmethod
    def get_rate_limit(cls, model: str) -> int:
        """
        Get the rate limit for a model.
        
        Args:
            model: Model name
            
        Returns:
            Rate limit (requests per minute)
        """
        provider = cls.get_provider(model)
        return cls.DEFAULT_LIMITS.get(provider, cls.DEFAULT_LIMITS["default"])

class AdaptiveRateLimiter:
    """
    Rate limiter that adapts to different providers and handles rate limit errors.
    """
    
    def __init__(self):
        """Initialize the rate limiter."""
        self.throttlers: Dict[str, Throttler] = {}
        self.last_error_time: Dict[str, float] = {}
        self.backoff_until: Dict[str, float] = {}
        self.current_limits: Dict[str, int] = {}
    
    def get_throttler(self, model: str) -> Throttler:
        """
        Get or create a throttler for a model.
        
        Args:
            model: Model name
            
        Returns:
            Throttler for the model
        """
        provider = ProviderRateLimits.get_provider(model)
        
        if provider not in self.throttlers:
            # Get initial rate limit
            rate_limit = ProviderRateLimits.get_rate_limit(model)
            
            # Create throttler
            self.throttlers[provider] = Throttler(
                rate_limit=rate_limit,
                period=60.0,  # 1 minute
                retry_interval=0.1,
            )
            
            # Store current limit
            self.current_limits[provider] = rate_limit
        
        return self.throttlers[provider]
    
    def should_backoff(self, model: str) -> Tuple[bool, float]:
        """
        Check if we should back off from using a model.
        
        Args:
            model: Model name
            
        Returns:
            Tuple of (should_backoff, seconds_remaining)
        """
        provider = ProviderRateLimits.get_provider(model)
        
        # Check if we're in a backoff period
        if provider in self.backoff_until:
            backoff_until = self.backoff_until[provider]
            now = time.time()
            
            if now < backoff_until:
                # Still in backoff period
                return True, backoff_until - now
        
        return False, 0.0
    
    def record_rate_limit_error(self, model: str) -> None:
        """
        Record a rate limit error for a model.
        
        Args:
            model: Model name
        """
        provider = ProviderRateLimits.get_provider(model)
        now = time.time()
        
        # Record error time
        self.last_error_time[provider] = now
        
        # Reduce rate limit
        if provider in self.current_limits:
            current_limit = self.current_limits[provider]
            new_limit = max(1, int(current_limit * 0.8))  # Reduce by 20%
            
            logger.warning(
                f"{Fore.YELLOW}Reducing rate limit for {provider} from {current_limit} to {new_limit} due to rate limit error{Style.RESET_ALL}"
            )
            
            # Update throttler
            if provider in self.throttlers:
                self.throttlers[provider] = Throttler(
                    rate_limit=new_limit,
                    period=60.0,  # 1 minute
                    retry_interval=0.1,
                )
            
            # Update current limit
            self.current_limits[provider] = new_limit
        
        # Set backoff period (exponential backoff based on consecutive errors)
        consecutive_errors = sum(1 for t in self.last_error_time.get(provider, []) if now - t < 300)  # Errors in last 5 minutes
        backoff_seconds = min(60 * 2 ** consecutive_errors, 3600)  # Max 1 hour
        
        logger.warning(
            f"{Fore.YELLOW}Backing off from {provider} for {backoff_seconds} seconds due to rate limit error{Style.RESET_ALL}"
        )
        
        self.backoff_until[provider] = now + backoff_seconds
    
    async def execute_with_rate_limit(self, model: str, func, *args, **kwargs):
        """
        Execute a function with rate limiting.
        
        Args:
            model: Model name
            func: Function to execute
            *args: Arguments for the function
            **kwargs: Keyword arguments for the function
            
        Returns:
            Result of the function
        """
        # Check if we should back off
        should_backoff, seconds_remaining = self.should_backoff(model)
        if should_backoff:
            raise RateLimitError(
                f"Backing off from {ProviderRateLimits.get_provider(model)} for {seconds_remaining:.1f} more seconds due to recent rate limit errors"
            )
        
        # Get throttler
        throttler = self.get_throttler(model)
        
        # Execute with throttling
        async with throttler:
            return await func(*args, **kwargs)
```

### 2. Fallback Model Configuration

```python
class FallbackConfig:
    """
    Configuration for fallback models.
    """
    
    def __init__(
        self,
        enabled: bool = True,
        models: Optional[List[str]] = None,
        max_fallbacks: int = 2,
        annotate: bool = True,
    ):
        """
        Initialize fallback configuration.
        
        Args:
            enabled: Whether fallbacks are enabled
            models: List of fallback models in order of preference
            max_fallbacks: Maximum number of fallbacks to try
            annotate: Whether to annotate fallback outputs
        """
        self.enabled = enabled
        self.models = models or ["gpt-3.5-turbo", "text-davinci-003"]
        self.max_fallbacks = max_fallbacks
        self.annotate = annotate
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'FallbackConfig':
        """
        Create a FallbackConfig from a dictionary.
        
        Args:
            config_dict: Dictionary with configuration values
            
        Returns:
            FallbackConfig instance
        """
        return cls(
            enabled=config_dict.get("enabled", True),
            models=config_dict.get("models", ["gpt-3.5-turbo", "text-davinci-003"]),
            max_fallbacks=config_dict.get("max_fallbacks", 2),
            annotate=config_dict.get("annotate", True),
        )

class ModelConfig:
    """
    Configuration for a model.
    """
    
    def __init__(
        self,
        name: str,
        temperature: float = 0.7,
        max_tokens: int = 1000,
        fallback_config: Optional[FallbackConfig] = None,
    ):
        """
        Initialize model configuration.
        
        Args:
            name: Model name
            temperature: Temperature setting
            max_tokens: Maximum tokens
            fallback_config: Fallback configuration
        """
        self.name = name
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.fallback_config = fallback_config or FallbackConfig()
```

### 3. Enhanced LLM Processor with Fallbacks

```python
class RateLimitError(Exception):
    """Exception raised when a rate limit is exceeded."""
    pass

class EnhancedLLMProcessor(ParallelLLMDataFrameProcessor):
    """
    Enhanced LLM processor with rate limiting and fallback models.
    """
    
    def __init__(
        self,
        def_model: str = "gpt-4o",
        def_temperature: float = 0.0,
        def_max_tokens: int = 8192,
        def_async_rate_limit: int = 10,
        def_thread_rate_limit: int = 10,
        fallback_config: Optional[FallbackConfig] = None,
    ):
        """
        Initialize the enhanced LLM processor.
        
        Args:
            def_model: Default model name
            def_temperature: Default temperature
            def_max_tokens: Default max tokens
            def_async_rate_limit: Default async rate limit
            def_thread_rate_limit: Default thread rate limit
            fallback_config: Fallback configuration
        """
        super().__init__(
            def_model=def_model,
            def_temperature=def_temperature,
            def_max_tokens=def_max_tokens,
            def_async_rate_limit=def_async_rate_limit,
            def_thread_rate_limit=def_thread_rate_limit,
        )
        
        self.fallback_config = fallback_config or FallbackConfig()
        self.rate_limiter = AdaptiveRateLimiter()
    
    async def _async_get_response_with_fallbacks(
        self,
        message: str,
        temperature: float,
        max_tokens: int,
        model: str,
        returns_list: bool = False,
        timeout_seconds: float = 60.0,
        verbose: bool = False,
    ) -> Tuple[str, Optional[str]]:
        """
        Get a response from the LLM model with fallback support.
        
        Args:
            message: The prompt message
            temperature: Temperature setting
            max_tokens: Maximum tokens
            model: Model name
            returns_list: Whether to return a list
            timeout_seconds: Timeout in seconds
            verbose: Verbose logging
            
        Returns:
            Tuple of (response, fallback_model)
        """
        # Try primary model first
        try:
            # Execute with rate limiting
            response = await self.rate_limiter.execute_with_rate_limit(
                model=model,
                func=self._async_get_response,
                message=message,
                temperature=temperature,
                max_tokens=max_tokens,
                model=model,
                returns_list=returns_list,
                verbose=verbose,
            )
            
            return response, None
            
        except RateLimitError as e:
            # Rate limit error, try fallbacks
            if not self.fallback_config.enabled:
                raise
            
            logger.warning(
                f"{Fore.YELLOW}Rate limit error for {model}: {str(e)}. Trying fallbacks.{Style.RESET_ALL}"
            )
            
            # Try fallback models
            for i, fallback_model in enumerate(self.fallback_config.models):
                if i >= self.fallback_config.max_fallbacks:
                    logger.warning(
                        f"{Fore.YELLOW}Reached maximum fallbacks ({self.fallback_config.max_fallbacks}). Giving up.{Style.RESET_ALL}"
                    )
                    raise
                
                try:
                    logger.info(
                        f"{Fore.CYAN}Trying fallback model {i+1}/{len(self.fallback_config.models)}: {fallback_model}{Style.RESET_ALL}"
                    )
                    
                    # Execute with rate limiting
                    response = await self.rate_limiter.execute_with_rate_limit(
                        model=fallback_model,
                        func=self._async_get_response,
                        message=message,
                        temperature=temperature,
                        max_tokens=max_tokens,
                        model=fallback_model,
                        returns_list=returns_list,
                        verbose=verbose,
                    )
                    
                    return response, fallback_model
                    
                except Exception as e:
                    logger.warning(
                        f"{Fore.YELLOW}Fallback model {fallback_model} failed: {str(e)}{Style.RESET_ALL}"
                    )
            
            # All fallbacks failed
            logger.error(
                f"{Fore.RED}All fallback models failed. Giving up.{Style.RESET_ALL}"
            )
            raise
            
        except Exception as e:
            # Other error, try fallbacks if enabled
            if not self.fallback_config.enabled:
                raise
            
            logger.warning(
                f"{Fore.YELLOW}Error with {model}: {str(e)}. Trying fallbacks.{Style.RESET_ALL}"
            )
            
            # Try fallback models
            for i, fallback_model in enumerate(self.fallback_config.models):
                if i >= self.fallback_config.max_fallbacks:
                    logger.warning(
                        f"{Fore.YELLOW}Reached maximum fallbacks ({self.fallback_config.max_fallbacks}). Giving up.{Style.RESET_ALL}"
                    )
                    raise
                
                try:
                    logger.info(
                        f"{Fore.CYAN}Trying fallback model {i+1}/{len(self.fallback_config.models)}: {fallback_model}{Style.RESET_ALL}"
                    )
                    
                    # Execute with rate limiting
                    response = await self.rate_limiter.execute_with_rate_limit(
                        model=fallback_model,
                        func=self._async_get_response,
                        message=message,
                        temperature=temperature,
                        max_tokens=max_tokens,
                        model=fallback_model,
                        returns_list=returns_list,
                        verbose=verbose,
                    )
                    
                    return response, fallback_model
                    
                except Exception as e:
                    logger.warning(
                        f"{Fore.YELLOW}Fallback model {fallback_model} failed: {str(e)}{Style.RESET_ALL}"
                    )
            
            # All fallbacks failed
            logger.error(
                f"{Fore.RED}All fallback models failed. Giving up.{Style.RESET_ALL}"
            )
            raise
    
    def annotate_fallback_response(self, response: str, fallback_model: str) -> str:
        """
        Annotate a response from a fallback model.
        
        Args:
            response: Response to annotate
            fallback_model: Fallback model that generated the response
            
        Returns:
            Annotated response
        """
        if not self.fallback_config.annotate:
            return response
        
        return f"<fb>{response}</fb> //fb = {fallback_model}"
    
    async def _process_chain_step(
        self,
        c_row: pd.Series,
        step: ChainStep,
        mapping: dict,
        max_attempts: int,
    ) -> None:
        """
        Process a single chain step for a given row.
        
        Args:
            c_row (pd.Series): The current row of the DataFrame.
            step (ChainStep): The chain step to process.
            mapping (dict): The mapping of prompt keywords to column names.
            max_attempts (int): The maximum number of retry attempts for failed API calls.
        """
        prompt = c_row.get(step.pt)
        if c_row.get(step.col) and not step.overwrite:
            logger.debug(f"{step.col} already exists (skip)")
            return
        for k, v in mapping.items():
            prompt = prompt.replace(f"{{{k}}}", str(c_row.get(v, "N/A")))

        temperature = (
            c_row.get("__temperature")
            or step.temperature
            or self.def_temperature
        )
        max_tokens = (
            c_row.get("__max_tokens") or step.max_tokens or self.def_max_tokens
        )
        model = c_row.get("__model") or step.model or self.def_model

        try:
            if max_attempts > 1:
                foo = tenacity.retry(
                    stop=tenacity.stop_after_attempt(max_attempts),
                    wait=tenacity.wait_exponential(exp_base=2, multiplier=2),
                    after=tenacity.after_log(logger=logger, log_level=1),
                )(self._async_get_response_with_fallbacks)
                response, fallback_model = await foo(
                    message=prompt,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    model=model,
                    returns_list=step.fanout,
                )
            else:
                response, fallback_model = await self._async_get_response_with_fallbacks(
                    message=prompt,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    model=model,
                    returns_list=step.fanout,
                )
        except Exception as e:
            error_msg = str(e).replace("\n", " ")
            logger.error(f"Error in _process_chain_step: {error_msg}")
            c_row[step.col] = f"[N/A] [ERROR: {error_msg}]"
        else:
            # Annotate fallback response if needed
            if fallback_model is not None:
                if step.fanout:
                    # Annotate each item in the list
                    response = [
                        self.annotate_fallback_response(item, fallback_model)
                        for item in response
                    ]
                else:
                    # Annotate single response
                    response = self.annotate_fallback_response(response, fallback_model)
            
            c_row[step.col] = response
```

### 4. Add Configuration for Rate Limits and Fallbacks

```python
def load_rate_limit_config(config_file: str) -> Dict[str, int]:
    """
    Load rate limit configuration from a file.
    
    Args:
        config_file: Path to configuration file
        
    Returns:
        Dictionary mapping providers to rate limits
    """
    try:
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        # Update default limits
        for provider, limit in config.items():
            ProviderRateLimits.DEFAULT_LIMITS[provider] = limit
        
        logger.info(f"{Fore.GREEN}Loaded rate limit configuration from {config_file}{Style.RESET_ALL}")
        return config
    except Exception as e:
        logger.warning(f"{Fore.YELLOW}Error loading rate limit configuration: {str(e)}. Using defaults.{Style.RESET_ALL}")
        return {}

def load_fallback_config(config_file: str) -> FallbackConfig:
    """
    Load fallback configuration from a file.
    
    Args:
        config_file: Path to configuration file
        
    Returns:
        FallbackConfig instance
    """
    try:
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        fallback_config = FallbackConfig.from_dict(config)
        
        logger.info(f"{Fore.GREEN}Loaded fallback configuration from {config_file}{Style.RESET_ALL}")
        return fallback_config
    except Exception as e:
        logger.warning(f"{Fore.YELLOW}Error loading fallback configuration: {str(e)}. Using defaults.{Style.RESET_ALL}")
        return FallbackConfig()
```

### 5. Update Main Function to Use Enhanced LLM Processor

```python
def parse_arguments():
    parser = argparse.ArgumentParser(description='Process Google Sheet data with LLM.')
    # ... existing arguments ...
    parser.add_argument('--rate-limit-config', type=str,
                        help='Path to rate limit configuration file')
    parser.add_argument('--fallback-config', type=str,
                        help='Path to fallback configuration file')
    parser.add_argument('--disable-fallbacks', action='store_true',
                        help='Disable fallback models')
    return parser.parse_args()

def main():
    args = parse_arguments()
    
    # ... existing code ...
    
    # Load rate limit configuration
    if args.rate_limit_config:
        rate_limit_config = load_rate_limit_config(args.rate_limit_config)
    else:
        rate_limit_config = {}
    
    # Load fallback configuration
    if args.fallback_config:
        fallback_config = load_fallback_config(args.fallback_config)
    else:
        fallback_config = FallbackConfig()
    
    # Override fallback enabled flag if specified
    if args.disable_fallbacks:
        fallback_config.enabled = False
    
    # Create enhanced LLM processor
    llm_proc = EnhancedLLMProcessor(
        def_model=model,
        def_temperature=temperature,
        def_max_tokens=max_tokens,
        def_async_rate_limit=10,
        def_thread_rate_limit=5,
        fallback_config=fallback_config,
    )
    
    # ... continue with existing code ...
```

### 6. Add Support for Extracting Fallback Annotations

```python
def extract_fallback_info(text: str) -> Tuple[str, Optional[str]]:
    """
    Extract fallback information from annotated text.
    
    Args:
        text: Annotated text
        
    Returns:
        Tuple of (original_text, fallback_model)
    """
    import re
    
    # Check for fallback annotation
    match = re.search(r"<fb>(.*?)</fb>\s*//fb\s*=\s*(.*?)(?:\s|$)", text, re.DOTALL)
    
    if match:
        original_text = match.group(1)
        fallback_model = match.group(2).strip()
        return original_text, fallback_model
    
    # No fallback annotation
    return text, None
```

## Architecture Diagram

```mermaid
classDiagram
    class ProviderRateLimits {
        +Dict DEFAULT_LIMITS
        +Dict MODEL_TO_PROVIDER
        +get_provider(model)
        +get_rate_limit(model)
    }
    
    class AdaptiveRateLimiter {
        +Dict throttlers
        +Dict last_error_time
        +Dict backoff_until
        +Dict current_limits
        +get_throttler(model)
        +should_backoff(model)
        +record_rate_limit_error(model)
        +execute_with_rate_limit(model, func, *args, **kwargs)
    }
    
    class FallbackConfig {
        +bool enabled
        +List models
        +int max_fallbacks
        +bool annotate
        +from_dict(config_dict)
    }
    
    class ModelConfig {
        +str name
        +float temperature
        +int max_tokens
        +FallbackConfig fallback_config
    }
    
    class EnhancedLLMProcessor {
        +FallbackConfig fallback_config
        +AdaptiveRateLimiter rate_limiter
        +_async_get_response_with_fallbacks(message, temperature, max_tokens, model, returns_list, timeout_seconds, verbose)
        +annotate_fallback_response(response, fallback_model)
        +_process_chain_step(c_row, step, mapping, max_attempts)
    }
    
    ProviderRateLimits <-- AdaptiveRateLimiter : uses
    FallbackConfig <-- ModelConfig : contains
    FallbackConfig <-- EnhancedLLMProcessor : contains
    AdaptiveRateLimiter <-- EnhancedLLMProcessor : contains
    ParallelLLMDataFrameProcessor <|-- EnhancedLLMProcessor : extends
```

## Process Flow

```mermaid
flowchart TD
    A[Start] --> B[Load Configuration]
    B --> C[Create Enhanced LLM Processor]
    C --> D[Process Each Column]
    
    D --> E{For Each Row}
    E --> F[Prepare Prompt]
    F --> G[Execute LLM Request]
    
    G --> H{Rate Limit?}
    H -->|No| I[Return Response]
    H -->|Yes| J[Record Rate Limit Error]
    
    J --> K{Fallbacks Enabled?}
    K -->|No| L[Raise Error]
    K -->|Yes| M[Try Fallback Models]
    
    M --> N{Fallback Success?}
    N -->|Yes| O[Annotate Response]
    N -->|No| P[All Fallbacks Failed]
    
    O --> Q[Return Annotated Response]
    P --> L
    
    I --> R[Store Response]
    Q --> R
    L --> S[Store Error]
    
    R --> E
    S --> E
    
    E --> T[Update Sheet]
    
    subgraph Rate Limiting
        H
        J
    end
    
    subgraph Fallback Handling
        K
        M
        N
        O
        P
    end
```

## Example Configuration Files

### Rate Limit Configuration

```json
{
  "openai": 60,
  "anthropic": 40,
  "google": 60,
  "mistral": 60,
  "cohere": 60,
  "default": 30
}
```

### Fallback Configuration

```json
{
  "enabled": true,
  "models": [
    "gpt-3.5-turbo",
    "claude-instant-1.2",
    "text-davinci-003"
  ],
  "max_fallbacks": 2,
  "annotate": true
}
```

## Context and Considerations

1. **Provider-Specific Rate Limits**: The implementation respects different rate limits for different providers, making it more efficient and less likely to hit rate limits.
2. **Adaptive Rate Limiting**: The implementation adapts rate limits based on errors, automatically reducing limits when rate limit errors occur.
3. **Fallback Models**: The implementation supports automatic fallback to alternative models when primary models fail or rate limits are exceeded.
4. **Annotation**: The implementation annotates outputs generated by fallback models, making it transparent which model generated each output.
5. **Configuration**: The implementation supports configuration of rate limits and fallback models through configuration files and command line arguments.
6. **Backoff Strategy**: The implementation includes an exponential backoff strategy for rate limit errors, reducing the frequency of requests when rate limits are exceeded.
7. **Future Enhancements**: 
   - More sophisticated rate limit detection and adaptation
   - Provider-specific fallback strategies
   - Cost-aware model selection
   - Automatic model selection based on prompt complexity