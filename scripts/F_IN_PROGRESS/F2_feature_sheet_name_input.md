# Feature: <PERSON><PERSON><PERSON> takes sheet name as input

Original requirement:
> "* <PERSON><PERSON><PERSON> takes sheet name as input, doesn't create new sheet unless sheet name doesn't exist, logs if doing so
> - new sheet won't have this format.
> - <feat> default to having a base sheet -> duplicate it and remove the data"

## Implementation Status: Partially Implemented

The current implementation in `main2.py` has a hardcoded sheet name rather than taking it as an input parameter. Let's examine the relevant code:

```python
# Define the sheet name to use
sheet_name = "Sheet1"

# Get data from sheets
df = get_data_from_sheet(sheet_id, range_name=sheet_name)
```

The script uses a fixed sheet name "Sheet1" rather than accepting it as a parameter. It does not create a new sheet if the specified sheet doesn't exist, nor does it duplicate a base sheet.

## Feature Description

This feature would allow users to:
1. Specify which sheet in a Google Spreadsheet to use for processing
2. Have the script automatically create a new sheet if the specified one doesn't exist
3. Optionally duplicate a "base" or "template" sheet when creating a new sheet

## Implementation Recommendation

### 1. Add Command Line Arguments

```python
import argparse

def parse_arguments():
    parser = argparse.ArgumentParser(description='Process Google Sheet data with LLM.')
    parser.add_argument('--sheet-name', type=str, default='Sheet1',
                        help='Name of the sheet to process')
    parser.add_argument('--create-if-missing', action='store_true',
                        help='Create the sheet if it does not exist')
    parser.add_argument('--base-sheet', type=str, default=None,
                        help='Base sheet to duplicate if creating a new sheet')
    return parser.parse_args()

def main():
    args = parse_arguments()
    sheet_name = args.sheet_name
    # Rest of the code...
```

### 2. Add Sheet Existence Check and Creation Logic

```python
def main():
    args = parse_arguments()
    sheet_name = args.sheet_name
    sheet_id = get_sheet_id_from_url(URL_PATH)
    
    # Check if sheet exists
    available_sheets = get_sheet_ranges(sheet_id)
    
    if sheet_name not in available_sheets:
        if args.create_if_missing:
            logger.info(f"Sheet '{sheet_name}' does not exist. Creating it...")
            
            if args.base_sheet and args.base_sheet in available_sheets:
                # Duplicate base sheet
                logger.info(f"Duplicating base sheet '{args.base_sheet}'...")
                duplicate_sheet(sheet_id, args.base_sheet, sheet_name)
                # Clear data but keep formatting
                clear_sheet_data(sheet_id, sheet_name)
            else:
                # Create empty sheet
                add_new_sheet(sheet_id, sheet_name)
                logger.warning(f"Created empty sheet '{sheet_name}'. Note: This sheet won't have the required format.")
        else:
            logger.error(f"Sheet '{sheet_name}' does not exist and --create-if-missing flag was not set.")
            sys.exit(1)
    
    # Continue with existing code...
    df = get_data_from_sheet(sheet_id, range_name=sheet_name)
    # ...
```

### 3. Add Helper Functions for Sheet Duplication and Data Clearing

```python
def duplicate_sheet(sheet_id, source_sheet_name, target_sheet_name):
    """Duplicate a sheet within the same spreadsheet."""
    # Get source sheet ID
    source_sheet_id = get_sheet_id(sheet_id, source_sheet_name)
    
    # Create duplicate request
    request = {
        'requests': [{
            'duplicateSheet': {
                'sourceSheetId': source_sheet_id,
                'insertSheetIndex': 0,  # Insert at the beginning
                'newSheetName': target_sheet_name
            }
        }]
    }
    
    # Execute request
    service.spreadsheets().batchUpdate(spreadsheetId=sheet_id, body=request).execute()
    logger.info(f"Sheet '{source_sheet_name}' duplicated as '{target_sheet_name}'")

def clear_sheet_data(sheet_id, sheet_name):
    """Clear data from a sheet while preserving formatting."""
    # Get sheet data to determine data range
    df = get_data_from_sheet(sheet_id, range_name=sheet_name)
    
    if len(df) > 0:
        # Keep header row (row 1) and configuration rows (rows 2-19)
        # Clear only data rows (row 20+)
        range_to_clear = f"{sheet_name}!A20:Z"
        
        request = {
            'requests': [{
                'updateCells': {
                    'range': {
                        'sheetId': get_sheet_id(sheet_id, sheet_name),
                        'startRowIndex': 19,  # 0-based index, so row 20 is index 19
                    },
                    'fields': 'userEnteredValue'
                }
            }]
        }
        
        # Execute request
        service.spreadsheets().batchUpdate(spreadsheetId=sheet_id, body=request).execute()
        logger.info(f"Data cleared from sheet '{sheet_name}' while preserving formatting")
```

## Architecture Diagram

```mermaid
flowchart TD
    A[Start Script] --> B{Parse Arguments}
    B --> C{Sheet Exists?}
    C -->|Yes| G[Load Sheet Data]
    C -->|No| D{Create If Missing?}
    D -->|No| E[Exit with Error]
    D -->|Yes| F{Base Sheet Specified?}
    F -->|Yes| H[Duplicate Base Sheet]
    F -->|No| I[Create Empty Sheet]
    H --> J[Clear Data Rows]
    I --> G
    J --> G
    G --> K[Process Data]
    K --> L[Update Sheet]
```

## Context and Considerations

1. **Sheet Format Consistency**: When creating a new sheet, it's important to ensure it has the correct format (headers, configuration rows, etc.). Duplicating a base sheet is the most reliable way to achieve this.
2. **Error Handling**: The implementation should gracefully handle cases where the sheet doesn't exist or can't be created.
3. **User Experience**: Clear logging messages help users understand what the script is doing, especially when it's creating or modifying sheets.
4. **Integration with Existing Code**: This feature builds on the existing Google Sheets utilities in the codebase, particularly `get_sheet_ranges()`, `add_new_sheet()`, and `get_sheet_id()`.
5. **Future Enhancements**: This could be extended to support creating multiple sheets at once or to support more complex sheet templates.