# Feature: Template Variable Parsing

Original requirement:
> parsing -> what to parse out, and where to put it? Could put in following cols, make light gray, and group. 
> Parsing for templates e.g., {{name.output}} should return the section within <o> (.*)</o> within 'name' col for the given row.

## Implementation Status: Partially Implemented

The current implementation in `main2.py` supports basic template variable substitution using the `{{column_name}}` syntax, but it doesn't support the advanced parsing feature described in the requirement, where `{{name.output}}` would extract content within `<o>...</o>` tags from the 'name' column.

## Feature Description

This feature would enhance the template variable system to support more advanced parsing operations:

1. **Attribute Access**: Allow accessing specific parts of a column's content using dot notation (e.g., `{{column.attribute}}`)
2. **Tag Extraction**: Extract content within specific XML-like tags (e.g., `<o>...</o>`)
3. **Structured Data Access**: Access specific fields in structured data (JSON, etc.)

This would make templates more powerful and flexible, allowing for more complex data transformations without requiring custom code.

## Current Implementation

The current template filling implementation is basic:

```python
# From main2.py
def fill_template(row, template, input_cols):
    """
    Fill template with values from a DataFrame row.

    Args:
        row: DataFrame row with input values
        template: String template with {variable} placeholders
        input_cols: List of column names to use for variable replacement

    Returns:
        Filled template with all variables replaced
    """
    for col in input_cols:
        if col in row:
            template = template.replace("{{" + col + "}}", str(row[col]))
        else:
            # Replace with empty string if column doesn't exist
            template = template.replace("{{" + col + "}}", "")

    # Find any remaining template variables and replace them with empty strings
    remaining_vars = re.findall(r"\{\{([^}]+)\}\}", template)
    for var in remaining_vars:
        template = template.replace("{{" + var + "}}", "")

    return template
```

## Implementation Recommendation

### 1. Enhanced Template Variable Parsing

```python
import re
from typing import Dict, Any, Optional, List, Tuple

def parse_variable_with_attributes(variable: str) -> Tuple[str, List[str]]:
    """
    Parse a template variable with attributes.
    
    Args:
        variable: Template variable (e.g., "name.output.trim")
        
    Returns:
        Tuple of (base_variable, attributes)
    """
    parts = variable.split(".")
    base_variable = parts[0]
    attributes = parts[1:] if len(parts) > 1 else []
    return base_variable, attributes

def extract_tagged_content(text: str, tag: str) -> Optional[str]:
    """
    Extract content within XML-like tags.
    
    Args:
        text: Text to extract from
        tag: Tag name (without brackets)
        
    Returns:
        Extracted content or None if not found
    """
    if not text or not isinstance(text, str):
        return None
    
    pattern = f"<{tag}>(.*?)</{tag}>"
    match = re.search(pattern, text, re.DOTALL)
    
    if match:
        return match.group(1)
    
    return None

def apply_attribute(value: Any, attribute: str) -> Any:
    """
    Apply an attribute transformation to a value.
    
    Args:
        value: Value to transform
        attribute: Attribute name
        
    Returns:
        Transformed value
    """
    if value is None:
        return None
    
    # Convert to string for text operations
    if not isinstance(value, str):
        value = str(value)
    
    # Handle different attributes
    if attribute == "output" or attribute == "o":
        # Extract content within <o>...</o> tags
        return extract_tagged_content(value, "o")
    
    elif attribute == "input" or attribute == "i":
        # Extract content within <i>...</i> tags
        return extract_tagged_content(value, "i")
    
    elif attribute == "trim":
        # Trim whitespace
        return value.strip()
    
    elif attribute == "upper":
        # Convert to uppercase
        return value.upper()
    
    elif attribute == "lower":
        # Convert to lowercase
        return value.lower()
    
    elif attribute == "title":
        # Convert to title case
        return value.title()
    
    elif attribute == "length" or attribute == "len":
        # Get length
        return str(len(value))
    
    elif attribute.startswith("json."):
        # Extract JSON field
        try:
            import json
            json_data = json.loads(value)
            json_path = attribute[5:].split(".")
            
            for key in json_path:
                if isinstance(json_data, dict) and key in json_data:
                    json_data = json_data[key]
                else:
                    return None
            
            return json_data
        except:
            return None
    
    # If attribute is not recognized, return the original value
    return value

def enhanced_fill_template(row: Dict[str, Any], template: str, input_cols: List[str]) -> str:
    """
    Fill template with values from a DataFrame row, with enhanced variable parsing.
    
    Args:
        row: DataFrame row with input values
        template: String template with {{variable}} placeholders
        input_cols: List of column names to use for variable replacement
        
    Returns:
        Filled template with all variables replaced
    """
    # Find all template variables
    variables = re.findall(r"\{\{([^}]+)\}\}", template)
    
    for var in variables:
        # Parse variable and attributes
        base_var, attributes = parse_variable_with_attributes(var)
        
        # Get base value
        if base_var in row and base_var in input_cols:
            value = row[base_var]
        else:
            # Replace with empty string if column doesn't exist
            template = template.replace("{{" + var + "}}", "")
            continue
        
        # Apply attributes in sequence
        for attr in attributes:
            value = apply_attribute(value, attr)
            
            # If value becomes None after applying an attribute, stop processing
            if value is None:
                break
        
        # Replace variable in template
        if value is not None:
            template = template.replace("{{" + var + "}}", str(value))
        else:
            template = template.replace("{{" + var + "}}", "")
    
    return template
```

### 2. Update Main Function to Use Enhanced Template Filling

```python
def main():
    # ... existing code ...
    
    # Fill templates with available variables
    prompt_inputs[template_col] = prompt_inputs.apply(
        lambda row: enhanced_fill_template(row, prompt_template, current_input_cols),
        axis=1,
    )
    
    # ... continue with existing code ...
```

### 3. Add Support for Custom Attribute Handlers

For more flexibility, we can add support for custom attribute handlers:

```python
# Dictionary of custom attribute handlers
CUSTOM_ATTRIBUTE_HANDLERS = {}

def register_attribute_handler(attribute_name, handler_function):
    """
    Register a custom attribute handler.
    
    Args:
        attribute_name: Name of the attribute
        handler_function: Function that takes a value and returns a transformed value
    """
    CUSTOM_ATTRIBUTE_HANDLERS[attribute_name] = handler_function

def apply_attribute(value: Any, attribute: str) -> Any:
    """
    Apply an attribute transformation to a value.
    
    Args:
        value: Value to transform
        attribute: Attribute name
        
    Returns:
        Transformed value
    """
    if value is None:
        return None
    
    # Check for custom attribute handler
    if attribute in CUSTOM_ATTRIBUTE_HANDLERS:
        return CUSTOM_ATTRIBUTE_HANDLERS[attribute](value)
    
    # Convert to string for text operations
    if not isinstance(value, str):
        value = str(value)
    
    # Handle different attributes
    # ... existing attribute handlers ...
    
    # If attribute is not recognized, return the original value
    return value

# Example custom attribute handler
def extract_first_sentence(value):
    """Extract the first sentence from a text."""
    if not value or not isinstance(value, str):
        return None
    
    sentences = re.split(r'(?<=[.!?])\s+', value)
    if sentences:
        return sentences[0]
    
    return value

# Register custom attribute handler
register_attribute_handler("first_sentence", extract_first_sentence)
```

### 4. Add Documentation for Template Variables

```python
def get_template_variable_documentation():
    """
    Get documentation for template variables.
    
    Returns:
        Documentation string
    """
    docs = """
    Template Variable Documentation
    ------------------------------
    
    Basic Usage:
    {{column_name}} - Insert the value from the column_name column
    
    Attributes:
    {{column_name.attribute}} - Apply an attribute transformation to the column value
    
    Available Attributes:
    - output or o: Extract content within <o>...</o> tags
    - input or i: Extract content within <i>...</i> tags
    - trim: Remove leading and trailing whitespace
    - upper: Convert to uppercase
    - lower: Convert to lowercase
    - title: Convert to title case
    - length or len: Get the length of the value
    - json.field: Extract a field from JSON data
    
    Custom Attributes:
    """
    
    # Add custom attribute documentation
    for attr_name in sorted(CUSTOM_ATTRIBUTE_HANDLERS.keys()):
        handler = CUSTOM_ATTRIBUTE_HANDLERS[attr_name]
        docs += f"- {attr_name}: {handler.__doc__ or 'No documentation available'}\n"
    
    return docs

# Add command to print documentation
def parse_arguments():
    parser = argparse.ArgumentParser(description='Process Google Sheet data with LLM.')
    # ... existing arguments ...
    parser.add_argument('--show-template-docs', action='store_true',
                        help='Show documentation for template variables')
    return parser.parse_args()

def main():
    args = parse_arguments()
    
    if args.show_template_docs:
        print(get_template_variable_documentation())
        sys.exit(0)
    
    # ... continue with existing code ...
```

## Architecture Diagram

```mermaid
flowchart TD
    A[Template with Variables] --> B[Parse Variables]
    B --> C{For Each Variable}
    
    C --> D[Parse Base Variable and Attributes]
    D --> E[Get Base Value from Row]
    
    E --> F{For Each Attribute}
    F --> G[Apply Attribute Transformation]
    G --> F
    
    F --> H[Replace Variable in Template]
    H --> C
    
    C --> I[Filled Template]
    
    subgraph Attribute Processing
        J[Built-in Attributes] --> G
        K[Custom Attribute Handlers] --> G
        L[Tag Extraction] --> G
        M[JSON Field Access] --> G
    end
```

## Example Usage

```
# Template with basic variable
"The customer's name is {{name}}."

# Template with attribute for tag extraction
"The customer's output is {{name.output}}."

# Template with multiple attributes
"The customer's name in uppercase is {{name.trim.upper}}."

# Template with JSON field access
"The customer's age is {{data.json.personal.age}}."

# Template with custom attribute
"The first sentence of the response is {{response.first_sentence}}."
```

## Context and Considerations

1. **Backward Compatibility**: The enhanced implementation should be backward compatible with the existing `{{column_name}}` syntax.
2. **Error Handling**: The implementation should handle errors gracefully, such as missing columns, invalid attributes, or malformed tags.
3. **Performance**: Since template filling happens for every row, the implementation should be efficient.
4. **Extensibility**: The custom attribute handler system allows for adding new transformations without modifying the core code.
5. **Documentation**: Clear documentation on available attributes and their usage is important for users.
6. **Security**: The implementation should consider security implications, especially when parsing structured data like JSON.
7. **Future Enhancements**: 
   - Support for more complex transformations
   - Support for conditional logic in templates
   - Support for formatting options (e.g., number formatting)
   - Support for aggregation across multiple rows