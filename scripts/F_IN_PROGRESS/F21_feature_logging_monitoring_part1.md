# Feature: Logging and Monitoring (Part 1)

Original requirement:
> Logging and monitoring -> how to log and monitor the runs?

## Implementation Status: Partially Implemented

The current implementation in `main2.py` and related files includes basic logging using the Python `logging` module and the `loguru` library. However, it doesn't have comprehensive monitoring capabilities or structured logging that would make it easy to track and analyze runs.

## Feature Description

This feature would enhance the logging and monitoring capabilities of the system to make it easier to track, analyze, and debug runs. In Part 1, we'll focus on:

1. Structured logging
2. Run tracking
3. Performance metrics

This would help users understand what's happening during runs, identify issues, and optimize performance.

## Current Implementation

The current implementation uses basic logging:

```python
# From main2.py
import logging
from loguru import logger
from colorama import Fore, Style

# ... 

logger.info(
    f"{Fore.CYAN}Debug: Input columns: {prompt_input_cols}{Style.RESET_ALL}"
)
logger.info(
    f"{Fore.CYAN}Debug: Output columns: {prompt_output_cols}{Style.RESET_ALL}"
)

# ...

logger.info(
    f"{Fore.GREEN}Processing {len(chain_input)} rows for {output_col} with '<generate>' tag.{Style.RESET_ALL}"
)
```

And in `google_sheets_utils.py`, there's some debug logging:

```python
# From google_sheets_utils.py
if (
    not value
    or value.strip() == ""
    or value.strip() == "<generate>"
    or value.strip() == "//"
):
    logger.debug(
        f"Skipping empty or <generate> or // value for {col_letter}{sheet_row}"
    )
    continue
```

## Implementation Recommendation: Structured Logging

### 1. Define Structured Logging Classes

```python
import json
import time
import uuid
import socket
import getpass
import platform
import os
from typing import Dict, Any, Optional, List, Union
from enum import Enum
from dataclasses import dataclass, field, asdict

class LogLevel(Enum):
    """Log levels."""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class LogEventType(Enum):
    """Types of log events."""
    RUN_START = "run_start"
    RUN_END = "run_end"
    COLUMN_START = "column_start"
    COLUMN_END = "column_end"
    ROW_START = "row_start"
    ROW_END = "row_end"
    LLM_REQUEST = "llm_request"
    LLM_RESPONSE = "llm_response"
    SHEET_UPDATE = "sheet_update"
    ERROR = "error"
    WARNING = "warning"
    METRIC = "metric"
    CUSTOM = "custom"

@dataclass
class LogContext:
    """Context information for log events."""
    run_id: str
    hostname: str = field(default_factory=socket.gethostname)
    username: str = field(default_factory=getpass.getuser)
    platform: str = field(default_factory=platform.platform)
    python_version: str = field(default_factory=platform.python_version)
    working_directory: str = field(default_factory=os.getcwd)
    command_line: List[str] = field(default_factory=lambda: list(sys.argv))
    
    sheet_id: Optional[str] = None
    sheet_name: Optional[str] = None
    
    current_column: Optional[str] = None
    current_row: Optional[int] = None
    
    custom_context: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)
    
    def update(self, **kwargs) -> None:
        """Update context with new values."""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
            else:
                self.custom_context[key] = value

@dataclass
class LogEvent:
    """A structured log event."""
    timestamp: float = field(default_factory=time.time)
    level: LogLevel = LogLevel.INFO
    event_type: LogEventType = LogEventType.CUSTOM
    message: str = ""
    context: LogContext = None
    data: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        result = {
            "timestamp": self.timestamp,
            "level": self.level.value,
            "event_type": self.event_type.value,
            "message": self.message,
            "data": self.data,
        }
        
        if self.context:
            result["context"] = self.context.to_dict()
        
        return result
    
    def to_json(self) -> str:
        """Convert to JSON string."""
        return json.dumps(self.to_dict())
```

### 2. Implement Structured Logger

```python
class StructuredLogger:
    """
    Logger that produces structured log events.
    """
    
    def __init__(
        self,
        run_id: Optional[str] = None,
        context: Optional[LogContext] = None,
        handlers: Optional[List[callable]] = None,
    ):
        """
        Initialize the structured logger.
        
        Args:
            run_id: ID for the current run
            context: Context information
            handlers: List of handler functions for log events
        """
        self.run_id = run_id or str(uuid.uuid4())
        self.context = context or LogContext(run_id=self.run_id)
        self.handlers = handlers or []
        
        # Add default handler that logs to loguru
        if not handlers:
            self.add_handler(self._default_handler)
    
    def add_handler(self, handler: callable) -> None:
        """
        Add a handler function for log events.
        
        Args:
            handler: Function that takes a LogEvent
        """
        self.handlers.append(handler)
    
    def _default_handler(self, event: LogEvent) -> None:
        """
        Default handler that logs to loguru.
        
        Args:
            event: Log event
        """
        # Map log level to loguru level
        level_map = {
            LogLevel.DEBUG: "DEBUG",
            LogLevel.INFO: "INFO",
            LogLevel.WARNING: "WARNING",
            LogLevel.ERROR: "ERROR",
            LogLevel.CRITICAL: "CRITICAL",
        }
        
        # Format message with color
        color_map = {
            LogLevel.DEBUG: Fore.CYAN,
            LogLevel.INFO: Fore.GREEN,
            LogLevel.WARNING: Fore.YELLOW,
            LogLevel.ERROR: Fore.RED,
            LogLevel.CRITICAL: Fore.RED,
        }
        
        color = color_map.get(event.level, Fore.WHITE)
        
        # Format message
        message = f"{color}[{event.event_type.value}] {event.message}{Style.RESET_ALL}"
        
        # Add context information
        if event.context and event.context.current_column:
            message = f"{color}[{event.context.current_column}] {message}"
        
        if event.context and event.context.current_row is not None:
            message = f"{color}[Row {event.context.current_row}] {message}"
        
        # Log with loguru
        logger_level = level_map.get(event.level, "INFO")
        getattr(logger, logger_level.lower())(message)
    
    def log(
        self,
        level: LogLevel,
        event_type: LogEventType,
        message: str,
        data: Optional[Dict[str, Any]] = None,
    ) -> LogEvent:
        """
        Log an event.
        
        Args:
            level: Log level
            event_type: Event type
            message: Log message
            data: Additional data
            
        Returns:
            The log event
        """
        event = LogEvent(
            level=level,
            event_type=event_type,
            message=message,
            context=self.context,
            data=data or {},
        )
        
        # Call all handlers
        for handler in self.handlers:
            try:
                handler(event)
            except Exception as e:
                # Log handler error with loguru directly
                logger.error(f"Error in log handler: {str(e)}")
        
        return event
    
    def debug(
        self,
        message: str,
        event_type: LogEventType = LogEventType.CUSTOM,
        data: Optional[Dict[str, Any]] = None,
    ) -> LogEvent:
        """Log a debug event."""
        return self.log(LogLevel.DEBUG, event_type, message, data)
    
    def info(
        self,
        message: str,
        event_type: LogEventType = LogEventType.CUSTOM,
        data: Optional[Dict[str, Any]] = None,
    ) -> LogEvent:
        """Log an info event."""
        return self.log(LogLevel.INFO, event_type, message, data)
    
    def warning(
        self,
        message: str,
        event_type: LogEventType = LogEventType.WARNING,
        data: Optional[Dict[str, Any]] = None,
    ) -> LogEvent:
        """Log a warning event."""
        return self.log(LogLevel.WARNING, event_type, message, data)
    
    def error(
        self,
        message: str,
        event_type: LogEventType = LogEventType.ERROR,
        data: Optional[Dict[str, Any]] = None,
    ) -> LogEvent:
        """Log an error event."""
        return self.log(LogLevel.ERROR, event_type, message, data)
    
    def critical(
        self,
        message: str,
        event_type: LogEventType = LogEventType.ERROR,
        data: Optional[Dict[str, Any]] = None,
    ) -> LogEvent:
        """Log a critical event."""
        return self.log(LogLevel.CRITICAL, event_type, message, data)
    
    def metric(
        self,
        name: str,
        value: Union[int, float, str],
        unit: Optional[str] = None,
        data: Optional[Dict[str, Any]] = None,
    ) -> LogEvent:
        """
        Log a metric.
        
        Args:
            name: Metric name
            value: Metric value
            unit: Unit of measurement
            data: Additional data
            
        Returns:
            The log event
        """
        metric_data = {
            "name": name,
            "value": value,
        }
        
        if unit:
            metric_data["unit"] = unit
        
        if data:
            metric_data.update(data)
        
        return self.log(
            LogLevel.INFO,
            LogEventType.METRIC,
            f"Metric: {name}={value}" + (f" {unit}" if unit else ""),
            metric_data,
        )
    
    def update_context(self, **kwargs) -> None:
        """
        Update the logger context.
        
        Args:
            **kwargs: Context values to update
        """
        self.context.update(**kwargs)
    
    def with_context(self, **kwargs) -> 'StructuredLogger':
        """
        Create a new logger with updated context.
        
        Args:
            **kwargs: Context values to update
            
        Returns:
            New logger with updated context
        """
        new_context = LogContext(run_id=self.run_id)
        new_context.__dict__.update(self.context.__dict__)
        new_context.update(**kwargs)
        
        return StructuredLogger(
            run_id=self.run_id,
            context=new_context,
            handlers=self.handlers,
        )
```

### 3. Implement File Logging Handler

```python
class FileLogHandler:
    """
    Handler that logs events to a file.
    """
    
    def __init__(
        self,
        file_path: str,
        mode: str = "a",
        encoding: str = "utf-8",
        format_json: bool = True,
    ):
        """
        Initialize the file log handler.
        
        Args:
            file_path: Path to log file
            mode: File open mode
            encoding: File encoding
            format_json: Whether to format JSON for readability
        """
        self.file_path = file_path
        self.mode = mode
        self.encoding = encoding
        self.format_json = format_json
        
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(os.path.abspath(file_path)), exist_ok=True)
    
    def __call__(self, event: LogEvent) -> None:
        """
        Handle a log event.
        
        Args:
            event: Log event to handle
        """
        try:
            with open(self.file_path, self.mode, encoding=self.encoding) as f:
                if self.format_json:
                    json.dump(event.to_dict(), f, indent=2)
                    f.write("\n")
                else:
                    f.write(event.to_json() + "\n")
        except Exception as e:
            # Log error with loguru directly
            logger.error(f"Error writing to log file {self.file_path}: {str(e)}")
```

### 4. Implement Run Tracker

```python
class RunTracker:
    """
    Tracks information about a run.
    """
    
    def __init__(
        self,
        logger: StructuredLogger,
        sheet_id: Optional[str] = None,
        sheet_name: Optional[str] = None,
    ):
        """
        Initialize the run tracker.
        
        Args:
            logger: Structured logger to use
            sheet_id: ID of the sheet being processed
            sheet_name: Name of the sheet being processed
        """
        self.logger = logger
        self.run_id = logger.run_id
        self.start_time = time.time()
        self.end_time = None
        
        # Update logger context
        self.logger.update_context(
            sheet_id=sheet_id,
            sheet_name=sheet_name,
        )
        
        # Track metrics
        self.metrics = {
            "columns_processed": 0,
            "rows_processed": 0,
            "cells_updated": 0,
            "llm_requests": 0,
            "llm_tokens": 0,
            "errors": 0,
            "warnings": 0,
        }
        
        # Track timings
        self.timings = {
            "total_duration": 0,
            "llm_duration": 0,
            "sheet_update_duration": 0,
        }
        
        # Track current state
        self.current_column = None
        self.current_row = None
        self.column_start_time = None
        self.row_start_time = None
        
        # Log run start
        self.logger.info(
            f"Starting run {self.run_id}",
            event_type=LogEventType.RUN_START,
            data={
                "run_id": self.run_id,
                "start_time": self.start_time,
                "sheet_id": sheet_id,
                "sheet_name": sheet_name,
            },
        )
    
    def start_column(self, column: str) -> None:
        """
        Start tracking a column.
        
        Args:
            column: Column name
        """
        self.current_column = column
        self.column_start_time = time.time()
        
        # Update logger context
        self.logger.update_context(current_column=column)
        
        # Log column start
        self.logger.info(
            f"Starting column {column}",
            event_type=LogEventType.COLUMN_START,
            data={
                "column": column,
                "start_time": self.column_start_time,
            },
        )
    
    def end_column(self) -> None:
        """End tracking the current column."""
        if self.current_column and self.column_start_time:
            duration = time.time() - self.column_start_time
            
            # Log column end
            self.logger.info(
                f"Finished column {self.current_column} in {duration:.2f}s",
                event_type=LogEventType.COLUMN_END,
                data={
                    "column": self.current_column,
                    "duration": duration,
                },
            )
            
            # Update metrics
            self.metrics["columns_processed"] += 1
            
            # Reset state
            self.current_column = None
            self.column_start_time = None
            
            # Update logger context
            self.logger.update_context(current_column=None)
    
    def start_row(self, row: int) -> None:
        """
        Start tracking a row.
        
        Args:
            row: Row index
        """
        self.current_row = row
        self.row_start_time = time.time()
        
        # Update logger context
        self.logger.update_context(current_row=row)
        
        # Log row start
        self.logger.debug(
            f"Starting row {row}",
            event_type=LogEventType.ROW_START,
            data={
                "row": row,
                "start_time": self.row_start_time,
            },
        )
    
    def end_row(self) -> None:
        """End tracking the current row."""
        if self.current_row is not None and self.row_start_time:
            duration = time.time() - self.row_start_time
            
            # Log row end
            self.logger.debug(
                f"Finished row {self.current_row} in {duration:.2f}s",
                event_type=LogEventType.ROW_END,
                data={
                    "row": self.current_row,
                    "duration": duration,
                },
            )
            
            # Update metrics
            self.metrics["rows_processed"] += 1
            
            # Reset state
            self.current_row = None
            self.row_start_time = None
            
            # Update logger context
            self.logger.update_context(current_row=None)
    
    def log_llm_request(
        self,
        model: str,
        prompt: str,
        temperature: float,
        max_tokens: int,
    ) -> None:
        """
        Log an LLM request.
        
        Args:
            model: Model name
            prompt: Prompt text
            temperature: Temperature setting
            max_tokens: Maximum tokens
        """
        # Log LLM request
        self.logger.debug(
            f"LLM request to {model}",
            event_type=LogEventType.LLM_REQUEST,
            data={
                "model": model,
                "temperature": temperature,
                "max_tokens": max_tokens,
                "prompt": prompt,
                "prompt_length": len(prompt),
            },
        )
        
        # Update metrics
        self.metrics["llm_requests"] += 1
    
    def log_llm_response(
        self,
        model: str,
        response: str,
        duration: float,
        tokens: Optional[int] = None,
    ) -> None:
        """
        Log an LLM response.
        
        Args:
            model: Model name
            response: Response text
            duration: Request duration in seconds
            tokens: Number of tokens used
        """
        # Log LLM response
        self.logger.debug(
            f"LLM response from {model} in {duration:.2f}s",
            event_type=LogEventType.LLM_RESPONSE,
            data={
                "model": model,
                "response": response,
                "response_length": len(response),
                "duration": duration,
                "tokens": tokens,
            },
        )
        
        # Update metrics
        if tokens:
            self.metrics["llm_tokens"] += tokens
        
        # Update timings
        self.timings["llm_duration"] += duration
    
    def log_sheet_update(
        self,
        cells_updated: int,
        duration: float,
    ) -> None:
        """
        Log a sheet update.
        
        Args:
            cells_updated: Number of cells updated
            duration: Update duration in seconds
        """
        # Log sheet update
        self.logger.info(
            f"Updated {cells_updated} cells in {duration:.2f}s",
            event_type=LogEventType.SHEET_UPDATE,
            data={
                "cells_updated": cells_updated,
                "duration": duration,
            },
        )
        
        # Update metrics
        self.metrics["cells_updated"] += cells_updated
        
        # Update timings
        self.timings["sheet_update_duration"] += duration
    
    def log_error(
        self,
        message: str,
        exception: Optional[Exception] = None,
        data: Optional[Dict[str, Any]] = None,
    ) -> None:
        """
        Log an error.
        
        Args:
            message: Error message
            exception: Exception object
            data: Additional data
        """
        error_data = data or {}
        
        if exception:
            error_data["exception"] = str(exception)
            error_data["exception_type"] = type(exception).__name__
        
        # Log error
        self.logger.error(
            message,
            event_type=LogEventType.ERROR,
            data=error_data,
        )
        
        # Update metrics
        self.metrics["errors"] += 1
    
    def log_warning(
        self,
        message: str,
        data: Optional[Dict[str, Any]] = None,
    ) -> None:
        """
        Log a warning.
        
        Args:
            message: Warning message
            data: Additional data
        """
        # Log warning
        self.logger.warning(
            message,
            event_type=LogEventType.WARNING,
            data=data or {},
        )
        
        # Update metrics
        self.metrics["warnings"] += 1
    
    def end_run(self) -> Dict[str, Any]:
        """
        End tracking the run.
        
        Returns:
            Dictionary with run metrics
        """
        self.end_time = time.time()
        self.timings["total_duration"] = self.end_time - self.start_time
        
        # Log run end
        self.logger.info(
            f"Finished run {self.run_id} in {self.timings['total_duration']:.2f}s",
            event_type=LogEventType.RUN_END,
            data={
                "run_id": self.run_id,
                "start_time": self.start_time,
                "end_time": self.end_time,
                "duration": self.timings["total_duration"],
                "metrics": self.metrics,
                "timings": self.timings,
            },
        )
        
        # Return run metrics
        return {
            "run_id": self.run_id,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "duration": self.timings["total_duration"],
            "metrics": self.metrics,
            "timings": self.timings,
        }
```

## Architecture Diagram

```mermaid
classDiagram
    class LogLevel {
        <<enumeration>>
        DEBUG
        INFO
        WARNING
        ERROR
        CRITICAL
    }
    
    class LogEventType {
        <<enumeration>>
        RUN_START
        RUN_END
        COLUMN_START
        COLUMN_END
        ROW_START
        ROW_END
        LLM_REQUEST
        LLM_RESPONSE
        SHEET_UPDATE
        ERROR
        WARNING
        METRIC
        CUSTOM
    }
    
    class LogContext {
        +str run_id
        +str hostname
        +str username
        +str platform
        +str python_version
        +str working_directory
        +List[str] command_line
        +str sheet_id
        +str sheet_name
        +str current_column
        +int current_row
        +Dict custom_context
        +to_dict()
        +update(**kwargs)
    }
    
    class LogEvent {
        +float timestamp
        +LogLevel level
        +LogEventType event_type
        +str message
        +LogContext context
        +Dict data
        +to_dict()
        +to_json()
    }
    
    class StructuredLogger {
        +str run_id
        +LogContext context
        +List handlers
        +add_handler(handler)
        +log(level, event_type, message, data)
        +debug(message, event_type, data)
        +info(message, event_type, data)
        +warning(message, event_type, data)
        +error(message, event_type, data)
        +critical(message, event_type, data)
        +metric(name, value, unit, data)
        +update_context(**kwargs)
        +with_context(**kwargs)
    }
    
    class FileLogHandler {
        +str file_path
        +str mode
        +str encoding
        +bool format_json
        +__call__(event)
    }
    
    class RunTracker {
        +StructuredLogger logger
        +str run_id
        +float start_time
        +float end_time
        +Dict metrics
        +Dict timings
        +str current_column
        +int current_row
        +float column_start_time
        +float row_start_time
        +start_column(column)
        +end_column()
        +start_row(row)
        +end_row()
        +log_llm_request(model, prompt, temperature, max_tokens)
        +log_llm_response(model, response, duration, tokens)
        +log_sheet_update(cells_updated, duration)
        +log_error(message, exception, data)
        +log_warning(message, data)
        +end_run()
    }
    
    LogLevel <-- LogEvent : uses
    LogEventType <-- LogEvent : uses
    LogContext <-- LogEvent : contains
    LogEvent <-- StructuredLogger : creates
    StructuredLogger <-- RunTracker : uses
    LogEvent <-- FileLogHandler : handles
```

## Context and Considerations

1. **Structured Logging**: The implementation provides structured logging with consistent event types, levels, and context information, making it easier to analyze logs.

2. **Run Tracking**: The implementation includes run tracking with metrics and timings, helping users understand what happened during a run and identify performance issues.

3. **Contextual Information**: The implementation captures contextual information like the current column and row, making it easier to understand the context of log events.

4. **Multiple Handlers**: The implementation supports multiple log handlers, allowing logs to be sent to different destinations (console, file, etc.).

5. **Performance Metrics**: The implementation tracks performance metrics like LLM request duration and token usage, helping users optimize their prompts and workflows.

6. **Integration with Existing Logging**: The implementation integrates with the existing logging system, maintaining compatibility with the current code.

7. **Future Enhancements**: 
   - Integration with monitoring systems like Prometheus
   - Web-based dashboard for viewing logs and metrics
   - Alerting for errors and performance issues
   - Historical trend analysis
