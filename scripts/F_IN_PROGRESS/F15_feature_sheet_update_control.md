# Feature: Sheet Update Control

Original requirement:
> Sheets code -> how to not overwrite it? Only fill empty rows? Could mark rows requesting updates with <target> or UPDATE

## Implementation Status: Partially Implemented

The current implementation in `main2.py` already has a mechanism to control which cells get updated using the `<generate>` tag. It only updates cells that have this specific tag, preserving other cell values. However, it doesn't have the additional suggested features like using `<target>` or `UPDATE` tags, or specifically targeting empty rows.

## Feature Description

This feature would enhance the control over which cells in the Google Sheet get updated by the script. It would provide more options for users to specify which cells should be updated, such as:

1. Only updating cells with specific tags (e.g., `<generate>`, `<target>`, `UPDATE`)
2. Only filling empty cells
3. Providing different update modes for different scenarios

This gives users more fine-grained control over the update process and helps prevent accidental overwrites of existing data.

## Current Implementation

The current implementation uses the `<generate>` tag to identify cells that should be updated:

```python
# From main2.py
# Get current values for prompt_output_cols to identify rows with "<generate>" tag
for output_col in prompt_output_cols:
    col_data = (
        df[output_col].iloc[19:].reset_index(drop=True).iloc[: len(prompt_inputs)]
    )
    prompt_inputs[f"original_{output_col}"] = col_data

    # Check for presence of "<generate>" tags
    generate_count = (col_data == "<generate>").sum()
    
    # Initialize output column with existing values (excluding "<generate>" tags)
    prompt_inputs[output_col] = col_data.apply(
        lambda x: "" if x == "<generate>" else x
    )
```

And later, it filters rows for processing based on this tag:

```python
# Find rows that have "<generate>" tag for this output column
generate_mask = prompt_inputs[f"original_{output_col}"] == "<generate>"

# ... validation and debugging ...

if not generate_mask.any():
    logger.info(
        f"{Fore.YELLOW}No rows with '<generate>' tag found for {output_col}. Skipping.{Style.RESET_ALL}"
    )
    continue

generate_indices = generate_mask[generate_mask].index.tolist()
rows_to_update[output_col] = generate_indices
```

## Implementation Recommendation

### 1. Define Update Tags and Modes

```python
# Update tags and modes
class UpdateTag:
    """Tags used to mark cells for update."""
    GENERATE = "<generate>"  # Generate new content
    TARGET = "<target>"      # Target for update
    UPDATE = "UPDATE"        # Update existing content
    EMPTY = "<empty>"        # Only update if empty

class UpdateMode:
    """Modes for updating cells."""
    ALL = "all"              # Update all cells
    TAGGED = "tagged"        # Update only tagged cells
    EMPTY = "empty"          # Update only empty cells
    TAGGED_OR_EMPTY = "tagged_or_empty"  # Update tagged or empty cells
```

### 2. Add Configuration for Update Mode

Add a new configuration option for the update mode:

```python
# Configuration for update mode
UPDATE_MODE = UpdateMode.TAGGED  # Default to updating only tagged cells

# Add command line argument
def parse_arguments():
    parser = argparse.ArgumentParser(description='Process Google Sheet data with LLM.')
    # ... existing arguments ...
    parser.add_argument('--update-mode', type=str, choices=["all", "tagged", "empty", "tagged_or_empty"],
                        default=UPDATE_MODE, help='Mode for updating cells')
    return parser.parse_args()
```

### 3. Enhance Cell Update Detection

```python
def is_cell_tagged(value, tags=None):
    """
    Check if a cell value contains any of the specified tags.
    
    Args:
        value: Cell value to check
        tags: List of tags to check for (default: all update tags)
        
    Returns:
        bool: True if the cell is tagged, False otherwise
    """
    if tags is None:
        tags = [UpdateTag.GENERATE, UpdateTag.TARGET, UpdateTag.UPDATE]
    
    if not value or not isinstance(value, str):
        return False
    
    value = value.strip()
    return any(tag == value for tag in tags)

def is_cell_empty(value):
    """
    Check if a cell is empty.
    
    Args:
        value: Cell value to check
        
    Returns:
        bool: True if the cell is empty, False otherwise
    """
    if pd.isna(value):
        return True
    
    if not isinstance(value, str):
        return False
    
    return value.strip() == ""

def should_update_cell(value, update_mode):
    """
    Check if a cell should be updated based on the update mode.
    
    Args:
        value: Cell value to check
        update_mode: Update mode to use
        
    Returns:
        bool: True if the cell should be updated, False otherwise
    """
    if update_mode == UpdateMode.ALL:
        return True
    
    if update_mode == UpdateMode.TAGGED:
        return is_cell_tagged(value)
    
    if update_mode == UpdateMode.EMPTY:
        return is_cell_empty(value)
    
    if update_mode == UpdateMode.TAGGED_OR_EMPTY:
        return is_cell_tagged(value) or is_cell_empty(value)
    
    # Default to tagged mode
    return is_cell_tagged(value)
```

### 4. Update Main Function to Use Enhanced Update Control

```python
def main():
    args = parse_arguments()
    update_mode = args.update_mode
    
    # ... existing code ...
    
    # Get current values for prompt_output_cols to identify rows for update
    for output_col in prompt_output_cols:
        col_data = (
            df[output_col].iloc[19:].reset_index(drop=True).iloc[: len(prompt_inputs)]
        )
        prompt_inputs[f"original_{output_col}"] = col_data

        # Check for cells that should be updated
        update_mask = col_data.apply(lambda x: should_update_cell(x, update_mode))
        update_count = update_mask.sum()
        
        logger.info(
            f"{Fore.CYAN}Debug: Found {update_count} rows to update for column {output_col} using mode '{update_mode}'{Style.RESET_ALL}"
        )

        # Log the exact values from a sample of rows
        sample_rows = col_data[update_mask].head()
        if not sample_rows.empty:
            logger.info(
                f"{Fore.CYAN}Debug: Sample rows to update for {output_col}: {sample_rows.index.tolist()}{Style.RESET_ALL}"
            )

        # Initialize output column with existing values (excluding cells to update)
        prompt_inputs[output_col] = col_data.apply(
            lambda x: "" if should_update_cell(x, update_mode) else x
        )
    
    # ... existing code ...
    
    # Process columns in sequence to handle dependencies between columns
    for idx, (
        prompt_template,
        model,
        temperature,
        max_tokens,
        flags,
        output_col,
    ) in enumerate(
        zip(
            prompt_templates,
            models,
            temperatures,
            max_output_tokens,
            flags,
            prompt_output_cols,
        )
    ):
        # ... existing code ...
        
        # Find rows that should be updated for this output column
        update_mask = prompt_inputs[f"original_{output_col}"].apply(
            lambda x: should_update_cell(x, update_mode)
        )
        
        # Debug the mask to see if it's finding the rows
        logger.info(
            f"{Fore.CYAN}Debug: Update mask for {output_col} has {update_mask.sum()} matches{Style.RESET_ALL}"
        )
        
        if not update_mask.any():
            logger.info(
                f"{Fore.YELLOW}No rows to update for {output_col}. Skipping.{Style.RESET_ALL}"
            )
            continue
        
        update_indices = update_mask[update_mask].index.tolist()
        rows_to_update[output_col] = update_indices
        
        # Filter to only process rows that should be updated
        chain_input = prompt_inputs.loc[update_indices].copy()
        
        # ... continue with existing code ...
```

### 5. Add Support for Per-Column Update Modes

For more flexibility, add support for specifying different update modes for each column:

```python
# Add a new configuration row in the sheet
# Row A: "UPDATE_MODE"
# Row B-Z: Update mode for each column

def main():
    # ... existing code ...
    
    # Extract update mode configuration
    update_modes = get_config_by_name(
        df, "UPDATE_MODE", prompt_output_cols, default_value=args.update_mode
    )
    
    # ... existing code ...
    
    # Process columns in sequence to handle dependencies between columns
    for idx, (
        prompt_template,
        model,
        temperature,
        max_tokens,
        flags,
        output_col,
        column_update_mode,
    ) in enumerate(
        zip(
            prompt_templates,
            models,
            temperatures,
            max_output_tokens,
            flags,
            prompt_output_cols,
            update_modes,
        )
    ):
        # Use column-specific update mode if valid, otherwise use global mode
        if column_update_mode in [UpdateMode.ALL, UpdateMode.TAGGED, UpdateMode.EMPTY, UpdateMode.TAGGED_OR_EMPTY]:
            current_update_mode = column_update_mode
        else:
            current_update_mode = args.update_mode
        
        # ... existing code ...
        
        # Find rows that should be updated for this output column
        update_mask = prompt_inputs[f"original_{output_col}"].apply(
            lambda x: should_update_cell(x, current_update_mode)
        )
        
        # ... continue with existing code ...
```

### 6. Add Support for Preserving Formatting

When updating cells, preserve any existing formatting:

```python
def update_to_sheet_with_formatting(update_df, output_cols, sheet_id, sheet_name):
    """
    Update specific output columns in the Google Sheet based on col_ref,
    preserving existing formatting.
    
    Args:
        update_df (pandas.DataFrame): DataFrame containing the output data to update
        output_cols (list): List of column names to update in the sheet
        sheet_id (str): ID of the Google Sheet
        sheet_name (str): Name of the sheet to update
    """
    try:
        # ... existing code ...
        
        # Prepare update requests
        update_requests = []
        
        for idx, row in update_df.iterrows():
            # Get col_ref value to determine which row to update
            col_ref_value = row.get("col_ref")
            
            # Skip if col_ref is missing or invalid
            if col_ref_value is None or pd.isna(col_ref_value):
                continue
            
            try:
                # Convert col_ref to integer
                row_ref = int(col_ref_value)
                
                # Apply 20-row offset (col_ref=1 corresponds to row 21)
                sheet_row = row_ref + 20
                
                # Prepare row data for each output column
                for col in output_cols:
                    if col in column_indices and col in update_df.columns:
                        col_idx = column_indices[col]
                        
                        # Get cell value safely
                        cell_value = row.get(col)
                        
                        # Skip empty values to avoid overwriting existing content with blanks
                        if pd.isna(cell_value) or (isinstance(cell_value, str) and cell_value.strip() == ""):
                            continue
                        
                        # Convert to string
                        value = str(cell_value)
                        
                        # Add to update requests
                        update_requests.append({
                            "updateCells": {
                                "range": {
                                    "sheetId": get_sheet_id(sheet_id, sheet_name),
                                    "startRowIndex": sheet_row - 1,  # 0-based index
                                    "endRowIndex": sheet_row,
                                    "startColumnIndex": col_idx,
                                    "endColumnIndex": col_idx + 1,
                                },
                                "fields": "userEnteredValue",
                                "rows": [{
                                    "values": [{
                                        "userEnteredValue": {
                                            "stringValue": value
                                        }
                                    }]
                                }]
                            }
                        })
            except (ValueError, TypeError) as e:
                logger.warning(
                    f"{Fore.YELLOW}Skipping row {idx}: Invalid col_ref format: {col_ref_value}. Error: {str(e)}{Style.RESET_ALL}"
                )
                continue
        
        # Execute batch update if we have requests
        if update_requests:
            logger.info(
                f"{Fore.CYAN}Total updates: {len(update_requests)} cells{Style.RESET_ALL}"
            )
            
            # Execute in batches of 100 to avoid API limits
            batch_size = 100
            for i in range(0, len(update_requests), batch_size):
                batch = update_requests[i:i+batch_size]
                
                body = {
                    "requests": batch
                }
                
                service.spreadsheets().batchUpdate(spreadsheetId=sheet_id, body=body).execute()
                
                logger.info(
                    f"{Fore.GREEN}Updated batch {i//batch_size + 1}/{(len(update_requests)-1)//batch_size + 1} ({len(batch)} cells){Style.RESET_ALL}"
                )
            
            logger.info(
                f"{Fore.GREEN}Successfully updated {len(update_requests)} cells in sheet '{sheet_name}'.{Style.RESET_ALL}"
            )
        else:
            logger.warning(
                f"{Fore.YELLOW}No valid updates to make in sheet '{sheet_name}'.{Style.RESET_ALL}"
            )
    
    except Exception as e:
        logger.error(
            f"{Fore.RED}Error updating sheet: {str(e)}{Style.RESET_ALL}"
        )
        raise
```

## Architecture Diagram

```mermaid
flowchart TD
    A[Start Script] --> B[Parse Arguments]
    B --> C[Load Sheet Data]
    C --> D[Extract Configuration]
    
    D --> E[Get Update Modes]
    E --> F[Process Each Column]
    
    F --> G{For Each Column}
    G --> H[Determine Update Mode]
    H --> I[Find Cells to Update]
    
    I --> J{Any Cells to Update?}
    J -->|No| K[Skip Column]
    J -->|Yes| L[Process Cells]
    
    L --> M[Generate Content]
    M --> N[Update Sheet]
    
    K --> G
    N --> G
    
    subgraph Update Control
        E
        H
        I
        J
    end
```

## Process Flow

```mermaid
sequenceDiagram
    participant User as User
    participant Script as Script
    participant Sheet as Google Sheet
    
    User->>Script: Run with update mode
    Script->>Sheet: Load data
    
    loop For Each Column
        Script->>Script: Determine column update mode
        Script->>Script: Find cells to update
        
        alt No cells to update
            Script->>Script: Skip column
        else Cells to update
            Script->>Script: Process cells
            Script->>Script: Generate content
            Script->>Sheet: Update cells
        end
    end
    
    Script->>User: Report results
```

## Example Usage

### 1. Command Line Usage

```bash
# Update only tagged cells (default)
python main2.py

# Update all cells
python main2.py --update-mode all

# Update only empty cells
python main2.py --update-mode empty

# Update tagged or empty cells
python main2.py --update-mode tagged_or_empty
```

### 2. Sheet Configuration

```
Row A: "UPDATE_MODE"
Row B (column 1): "tagged"
Row C (column 2): "empty"
Row D (column 3): "all"
```

### 3. Cell Tagging

```
# Cell with <generate> tag - will be updated in "tagged" mode
<generate>

# Cell with <target> tag - will be updated in "tagged" mode
<target>

# Cell with UPDATE tag - will be updated in "tagged" mode
UPDATE

# Empty cell - will be updated in "empty" or "tagged_or_empty" mode
```

## Context and Considerations

1. **Flexibility**: The implementation provides multiple ways to control which cells get updated, giving users more flexibility.
2. **Backward Compatibility**: The implementation maintains backward compatibility with the existing `<generate>` tag.
3. **Per-Column Control**: The ability to specify different update modes for each column provides fine-grained control.
4. **Formatting Preservation**: The implementation preserves existing cell formatting when updating values.
5. **Performance**: Batch updates improve performance when updating many cells.
6. **User Experience**: Clear logging helps users understand which cells are being updated and why.
7. **Future Enhancements**: 
   - Support for more complex update conditions
   - Support for conditional updates based on cell content
   - Support for partial updates (e.g., appending to existing content)
   - Support for formatting control (e.g., setting cell colors based on update status)