# Feature: Config Reference by Name

Original requirement:
> ✅ -> config pulled as ref to it's name in col A, not row number. We need to agree defaults, which should be GLOB_VARS in guard main.

## Implementation Status: Partially Implemented

The current implementation in `main2.py` uses fixed row indices to extract configuration values rather than referencing them by name from column A. The checkmark (✅) in the requirement suggests this might be implemented elsewhere or was planned to be implemented.

## Feature Description

This feature would change how configuration values are extracted from the Google Sheet. Instead of using fixed row indices (e.g., row 4 for templates, row 5 for models), the script would look for specific configuration names in column A (e.g., "TEMPLATE", "MODEL") and use those rows for configuration values. This makes the sheet format more flexible and self-documenting.

Additionally, there should be default global variables defined in the main script that are used when specific configurations are not found in the sheet.

## Current Implementation

The current code uses fixed row indices to extract configuration:

```python
# From main2.py
prompt_templates = list(
    df.iloc[[3]][prompt_output_cols].to_dict("records")[0].values()
)
models = list(df.iloc[[4]][prompt_output_cols].to_dict("records")[0].values())
temperatures = list(
    map(
        float,
        df.iloc[[5]][prompt_output_cols].to_dict("records")[0].values(),
    )
)
max_output_tokens = list(
    map(
        int,
        df.iloc[[6]][prompt_output_cols].to_dict("records")[0].values(),
    )
)
flags = list(df.iloc[[7]][prompt_output_cols].to_dict("records")[0].values())
```

## Implementation Recommendation

### 1. Define Configuration Names and Defaults

```python
# Global default configuration
class CONFIG:
    LOG_LEVEL = "DEBUG"
    DEFAULT_MODEL = "gpt-4o"
    DEFAULT_TEMPERATURE = 0.7
    DEFAULT_MAX_TOKENS = 1000
    DEFAULT_FLAGS = ""
    
    # Configuration row names in column A
    CONFIG_NAMES = {
        "TEMPLATE": "TEMPLATE",
        "MODEL": "MODEL",
        "TEMPERATURE": "TEMPERATURE",
        "MAX_TOKENS": "MAX_TOKENS",
        "FLAGS": "FLAGS",
    }
```

### 2. Function to Extract Configuration by Name

```python
def get_config_by_name(df, config_name, output_cols, default_value=None):
    """
    Extract configuration values by looking for config_name in column A.
    
    Args:
        df: DataFrame with the data
        config_name: Name of the configuration to find in column A
        output_cols: List of output column names to extract values for
        default_value: Default value to use if config_name is not found
        
    Returns:
        List of configuration values for each output column
    """
    # Look for the configuration name in column A
    config_rows = df[df['A'] == config_name].index.tolist()
    
    if not config_rows:
        logger.warning(
            f"{Fore.YELLOW}Configuration '{config_name}' not found in column A. Using default: {default_value}{Style.RESET_ALL}"
        )
        # Return default value for each output column
        return [default_value] * len(output_cols)
    
    # Use the first matching row
    config_row = config_rows[0]
    
    # Extract values for output columns
    config_values = []
    for col in output_cols:
        if col in df.columns:
            value = df.loc[config_row, col]
            # Handle empty or NaN values
            if pd.isna(value) or (isinstance(value, str) and value.strip() == ""):
                logger.warning(
                    f"{Fore.YELLOW}No {config_name} value for column {col}. Using default: {default_value}{Style.RESET_ALL}"
                )
                config_values.append(default_value)
            else:
                config_values.append(value)
        else:
            logger.warning(
                f"{Fore.YELLOW}Column {col} not found. Using default {config_name}: {default_value}{Style.RESET_ALL}"
            )
            config_values.append(default_value)
    
    return config_values
```

### 3. Update Main Function to Use Named Configuration

```python
def main():
    # ... existing code ...
    
    # Extract configuration by name
    prompt_templates = get_config_by_name(
        df, CONFIG.CONFIG_NAMES["TEMPLATE"], prompt_output_cols, default_value=""
    )
    
    models = get_config_by_name(
        df, CONFIG.CONFIG_NAMES["MODEL"], prompt_output_cols, default_value=CONFIG.DEFAULT_MODEL
    )
    
    # Extract temperatures with type conversion
    temp_values = get_config_by_name(
        df, CONFIG.CONFIG_NAMES["TEMPERATURE"], prompt_output_cols, default_value=str(CONFIG.DEFAULT_TEMPERATURE)
    )
    temperatures = [float(t) for t in temp_values]
    
    # Extract max tokens with type conversion
    token_values = get_config_by_name(
        df, CONFIG.CONFIG_NAMES["MAX_TOKENS"], prompt_output_cols, default_value=str(CONFIG.DEFAULT_MAX_TOKENS)
    )
    max_output_tokens = [int(t) for t in token_values]
    
    flags = get_config_by_name(
        df, CONFIG.CONFIG_NAMES["FLAGS"], prompt_output_cols, default_value=CONFIG.DEFAULT_FLAGS
    )
    
    # ... continue with existing code ...
```

### 4. Add Type Conversion and Validation

```python
def convert_config_values(values, target_type, default_value):
    """
    Convert configuration values to the target type, with error handling.
    
    Args:
        values: List of values to convert
        target_type: Type to convert to (float, int, etc.)
        default_value: Default value to use if conversion fails
        
    Returns:
        List of converted values
    """
    converted = []
    for value in values:
        try:
            converted.append(target_type(value))
        except (ValueError, TypeError):
            logger.warning(
                f"{Fore.YELLOW}Could not convert '{value}' to {target_type.__name__}. Using default: {default_value}{Style.RESET_ALL}"
            )
            converted.append(default_value)
    return converted
```

## Architecture Diagram

```mermaid
flowchart TD
    A[Start Script] --> B[Load Sheet Data]
    B --> C[Extract Column References]
    C --> D[Extract Configuration by Name]
    
    D --> E1[Get Templates]
    D --> E2[Get Models]
    D --> E3[Get Temperatures]
    D --> E4[Get Max Tokens]
    D --> E5[Get Flags]
    
    E1 --> F[Convert and Validate Config Values]
    E2 --> F
    E3 --> F
    E4 --> F
    E5 --> F
    
    F --> G[Process Data]
    G --> H[Update Sheet]
    
    subgraph Configuration Extraction
        D
        E1
        E2
        E3
        E4
        E5
    end
```

## Context and Considerations

1. **Sheet Format Flexibility**: Using named configurations makes the sheet format more flexible and self-documenting. Users can understand what each row is for by looking at column A.
2. **Default Values**: Having default values ensures the script can run even if some configuration is missing, making it more robust.
3. **Type Conversion**: Configuration values from the sheet are strings, so proper type conversion with error handling is important.
4. **Backward Compatibility**: The implementation should consider backward compatibility with existing sheets that might not have named configurations.
5. **Documentation**: Clear documentation on the expected sheet format and configuration names would help users set up their sheets correctly.
6. **Future Enhancements**: 
   - Support for more configuration options
   - Configuration validation (e.g., valid model names)
   - Configuration inheritance or overrides
   - Support for configuration sections or groups