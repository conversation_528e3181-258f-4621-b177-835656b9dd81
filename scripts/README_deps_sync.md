# Dependency Sync Checker

This script checks if dependencies in `pyproject.toml` and `requirements.txt` are in sync, and optionally fixes any discrepancies.

## Features

- Checks if production dependencies in `pyproject.toml` match those in `requirements.txt`
- Checks if development dependencies in `pyproject.toml` match those in `dev-requirements.txt`
- Detects missing dependencies in either file
- Detects version mismatches between the same dependencies
- Checks if dependencies use `>=` instead of `==` version specifier
- Can automatically fix discrepancies by updating the requirements files
- Handles both exact versions (`==`) and minimum versions (`>=`)

## Usage

### Basic Check

To check if dependencies are in sync:

```bash
python scripts/check_deps_sync.py
```

This will check only production dependencies and report any discrepancies.

### Check with Auto-Fix

To check and automatically fix any discrepancies:

```bash
python scripts/check_deps_sync.py --fix
```

This will update `requirements.txt` to match the dependencies in `pyproject.toml`.

### Check Development Dependencies

To also check development dependencies:

```bash
python scripts/check_deps_sync.py --dev
```

This will check both production and development dependencies.

### Check Version Format

To check if dependencies use `>=` instead of `==` version specifier:

```bash
python scripts/check_deps_sync.py --check-format
```

This will check if dependencies in `pyproject.toml` use `>=` instead of `==` version specifier.

### Check and Fix All Dependencies

To check and fix both production and development dependencies, including version format:

```bash
python scripts/check_deps_sync.py --dev --fix --check-format
```

### Using Makefile Commands

The project includes Makefile commands for checking and fixing dependencies:

```bash
# Check dependencies
make deps-check

# Fix dependency issues
make deps-fix
```

## Pre-commit Hook

A pre-commit hook is included to automatically run this script before each commit. If dependencies are not in sync, the commit will be aborted.

To skip the pre-commit hook, use the `--no-verify` flag when committing:

```bash
git commit --no-verify
```

## How It Works

1. The script parses `pyproject.toml` to extract production dependencies from the `[project.dependencies]` section and development dependencies from the `[tool.uv.dev-dependencies]` section.
2. It parses `requirements.txt` and `dev-requirements.txt` to extract the dependencies listed there.
3. It compares the dependencies from both sources and identifies:
   - Dependencies in `pyproject.toml` but missing in the requirements files
   - Dependencies in the requirements files but missing in `pyproject.toml`
   - Dependencies with version mismatches
4. If the `--check-format` flag is provided, it checks if dependencies use `>=` instead of `==` version specifier.
5. If the `--fix` flag is provided, it updates the requirements files to match `pyproject.toml` and reports dependencies in `pyproject.toml` that should be updated to use `>=` instead of `==`.

## Notes

- The script requires Python 3.11+ or the `tomli` package for Python < 3.11.
- The script assumes that `pyproject.toml` is the source of truth for dependencies.
- The script will create `dev-requirements.txt` if it doesn't exist.
- By default, all dependencies should use `>=` instead of `==` version specifier to allow for compatible updates.
- The `--exceptions` flag can be used to specify dependencies that are allowed to use `==` version specifier (not implemented in the Makefile commands).
