# Feature: LiteLLM Processing

Original requirement:
> * LiteLLM doing processing

## Implementation Status: Implemented

The current implementation in the codebase uses LiteLLM for processing LLM requests. This is fully implemented in the `ParallelLLMDataFrameProcessor` class in `processor.py`.

## Feature Description

LiteLLM is a library that provides a unified interface to various LLM providers (OpenAI, Anthropic, etc.). It simplifies the process of sending requests to different LLM models and handling their responses. This feature ensures that the system uses LiteLLM for all LLM processing, which provides:

1. Consistent API for different LLM providers
2. Simplified error handling and retries
3. Rate limiting and throttling
4. Asynchronous processing capabilities

## Implementation Details

The implementation is primarily in the `processor.py` file, specifically in the `ParallelLLMDataFrameProcessor` class. Let's examine the key components:

### LiteLLM Import and Usage

```python
# From processor.py
import litellm

# ...

async def _async_get_response(
    self,
    message: str,
    temperature: float,
    max_tokens: int,
    model: str,
    returns_list: bool = False,
    verbose: bool = False,
) -> Union[str, List[str]]:
    """
    Get a response from the LLM model asynchronously.
    """
    async with self.throttler:
        start = time.time()
        try:
            messages = [
                {
                    "role": "system",
                    "content": "You are a helpful assistant.",
                },
                {"role": "user", "content": message},
            ]
            resp = await litellm.acompletion(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
            )
            text = resp["choices"][0]["message"]["content"]
        except Exception as e:
            # Error handling...
            raise
        else:
            # Process response...
            return text
```

### Retry Mechanism with Tenacity

The implementation includes a retry mechanism using the Tenacity library, which works with LiteLLM to handle transient errors:

```python
# From processor.py
import tenacity

# ...

if max_attempts > 1:
    foo = tenacity.retry(
        stop=tenacity.stop_after_attempt(max_attempts),
        wait=tenacity.wait_exponential(exp_base=2, multiplier=2),
        after=tenacity.after_log(logger=logger, log_level=1),
    )(self._async_get_response)
    response = await foo(
        message=prompt,
        temperature=temperature,
        max_tokens=max_tokens,
        model=model,
        returns_list=step.fanout,
    )
else:
    response = await self._async_get_response(
        message=prompt,
        temperature=temperature,
        max_tokens=max_tokens,
        model=model,
        returns_list=step.fanout,
    )
```

### Rate Limiting and Throttling

The implementation includes rate limiting and throttling to prevent overwhelming the LLM APIs:

```python
# From processor.py
from asyncio_throttle import Throttler

# ...

def __init__(
    self,
    def_model: str = "gpt-4o",
    def_temperature: float = 0.0,
    def_max_tokens: int = 8192,
    def_async_rate_limit: int = 10,
    def_thread_rate_limit: int = 10,
):
    # ...
    self.throttler = Throttler(
        rate_limit=def_async_rate_limit, period=1.0, retry_interval=0.1
    )
    # ...

async def _async_get_response(
    self,
    message: str,
    temperature: float,
    max_tokens: int,
    model: str,
    returns_list: bool = False,
    verbose: bool = False,
) -> Union[str, List[str]]:
    async with self.throttler:
        # Process LLM request...
```

## Architecture Diagram

```mermaid
sequenceDiagram
    participant Main as main2.py
    participant Processor as ParallelLLMDataFrameProcessor
    participant LiteLLM as litellm
    participant LLMProvider as LLM Provider (OpenAI, etc.)
    
    Main->>Processor: execute_chain(df, chain)
    Processor->>Processor: _process_df_with_llm_parallel()
    loop For each row
        Processor->>Processor: _process_chain()
        loop For each chain step
            Processor->>Processor: _process_chain_step()
            Processor->>Processor: _async_get_response()
            
            alt With retries
                Processor->>Processor: tenacity.retry(_async_get_response)
            end
            
            Processor->>LiteLLM: litellm.acompletion()
            LiteLLM->>LLMProvider: API Request
            LLMProvider-->>LiteLLM: API Response
            LiteLLM-->>Processor: Response
            
            alt Returns list
                Processor->>Processor: _fanout_list()
            end
        end
    end
    Processor-->>Main: result_df
```

## Context and Considerations

1. **Model Flexibility**: LiteLLM allows the system to work with various LLM providers without changing the core code. The model name is specified in the configuration and passed to LiteLLM.
2. **Asynchronous Processing**: The implementation uses async/await patterns to process multiple requests concurrently, improving throughput.
3. **Error Handling**: The implementation includes comprehensive error handling for LLM API failures, with optional retries.
4. **Rate Limiting**: The throttler prevents overwhelming the LLM APIs with too many requests at once.
5. **Configuration**: Model parameters (temperature, max_tokens, etc.) can be configured at multiple levels:
   - Default values in the processor
   - Chain step parameters
   - DataFrame columns (__model, __temperature, __max_tokens)
6. **Future Enhancements**: 
   - Support for more LiteLLM features like caching
   - Better handling of model-specific parameters
   - Integration with LiteLLM's logging and monitoring capabilities