# Feature: Generate Flag for Cell Processing

Original requirement:
> <generate> <<only generate for cells where I've added this flag? See col I20-I34

## Implementation Status: Implemented

The current implementation in `main2.py` already supports the `<generate>` flag to selectively process cells. It only generates content for cells that have this specific tag.

## Feature Description

This feature allows users to selectively mark specific cells for processing by adding a `<generate>` tag. The script only processes cells with this tag, rather than processing all cells in the output columns. This provides fine-grained control over which cells get updated.

## Current Implementation

The implementation is found in `main2.py` and includes several components:

### 1. Identifying Cells with the Generate Tag

```python
# From main2.py
# Get current values for prompt_output_cols to identify rows with "<generate>" tag
# Also prepopulate output columns with existing values for chain dependency
for output_col in prompt_output_cols:
    col_data = (
        df[output_col].iloc[19:].reset_index(drop=True).iloc[: len(prompt_inputs)]
    )
    prompt_inputs[f"original_{output_col}"] = col_data

    # Check for presence of "<generate>" tags
    generate_count = (col_data == "<generate>").sum()
    logger.info(
        f"{Fore.CYAN}Debug: Found {generate_count} rows with '<generate>' for column {output_col}{Style.RESET_ALL}"
    )

    # Log the exact values from a sample of rows
    sample_rows = col_data[col_data == "<generate>"].head()
    if not sample_rows.empty:
        logger.info(
            f"{Fore.CYAN}Debug: Sample rows with '<generate>' for {output_col}: {sample_rows.index.tolist()}{Style.RESET_ALL}"
        )

    # Initialize output column with existing values (excluding "<generate>" tags)
    prompt_inputs[output_col] = col_data.apply(
        lambda x: "" if x == "<generate>" else x
    )
```

### 2. Filtering Rows for Processing

```python
# From main2.py
# Find rows that have "<generate>" tag for this output column
generate_mask = prompt_inputs[f"original_{output_col}"] == "<generate>"

# Debug the mask to see if it's finding the rows
logger.info(
    f"{Fore.CYAN}Debug: Generate mask for {output_col} has {generate_mask.sum()} matches{Style.RESET_ALL}"
)

# Double-check by directly searching the column values
direct_check = prompt_inputs[f"original_{output_col}"].apply(
    lambda x: str(x).strip() == "<generate>"
)
logger.info(
    f"{Fore.CYAN}Debug: Direct check for {output_col} has {direct_check.sum()} matches{Style.RESET_ALL}"
)

# If there's a discrepancy, log some values for debugging
if generate_mask.sum() != direct_check.sum():
    logger.warning(
        f"{Fore.YELLOW}Discrepancy in '<generate>' detection for {output_col}. Checking values...{Style.RESET_ALL}"
    )
    for i, val in enumerate(prompt_inputs[f"original_{output_col}"].head(10)):
        logger.info(
            f"{Fore.CYAN}Row {i}, value: '{val}', len: {len(str(val))}, is '<generate>'?: {str(val).strip() == '<generate>'}{Style.RESET_ALL}"
        )

    # Use the more permissive check
    generate_mask = direct_check

if not generate_mask.any():
    logger.info(
        f"{Fore.YELLOW}No rows with '<generate>' tag found for {output_col}. Skipping.{Style.RESET_ALL}"
    )
    continue

generate_indices = generate_mask[generate_mask].index.tolist()
rows_to_update[output_col] = generate_indices

logger.info(
    f"{Fore.CYAN}Debug: Generate indices for {output_col}: {generate_indices}{Style.RESET_ALL}"
)

# Filter to only process rows with "<generate>" tag
chain_input = prompt_inputs.loc[generate_indices].copy()
```

### 3. Processing Only Flagged Cells

```python
# From main2.py
if len(chain_input) == 0:
    logger.info(
        f"{Fore.YELLOW}No rows to process for {output_col} after filtering.{Style.RESET_ALL}"
    )
    continue

logger.info(
    f"{Fore.GREEN}Processing {len(chain_input)} rows for {output_col} with '<generate>' tag.{Style.RESET_ALL}"
)

# Debug some of the inputs
if not chain_input.empty:
    logger.info(
        f"{Fore.CYAN}Debug: First row template for {output_col}: {chain_input[template_col].iloc[0]}{Style.RESET_ALL}"
    )

# Process data
llm_proc = ParallelLLMDataFrameProcessor(
    def_model=model,
    def_temperature=temperature,
    def_max_tokens=max_tokens,
    def_async_rate_limit=10,
    def_thread_rate_limit=5,
)

# Set the template column as the source for the prompt
chain = [
    ChainStep(
        pt=template_col,  # Use the template column as source
        model=model,
        temperature=temperature,
        max_tokens=max_tokens,
        col=output_col,
    ),
]

start_time = time.time()
result_df = llm_proc.execute_chain(
    chain_input,
    chain,
    max_attempts=3,
)
```

## Architecture Diagram

```mermaid
flowchart TD
    A[Load Sheet Data] --> B[Extract Column References]
    B --> C[Process Each Output Column]
    
    C --> D[Identify Cells with <generate> Tag]
    D --> E{Any Cells to Generate?}
    
    E -->|No| F[Skip Column]
    E -->|Yes| G[Filter Rows with <generate> Tag]
    
    G --> H[Create Filled Templates]
    H --> I[Process with LLM]
    I --> J[Store Results]
    
    J --> K[Update Sheet]
    F --> C
    
    subgraph Generate Flag Processing
        D
        E
        G
    end
```

## Process Flow

```mermaid
sequenceDiagram
    participant Sheet as Google Sheet
    participant Script as main2.py
    participant LLM as LLM Processor
    
    Sheet->>Script: Load Data
    Script->>Script: Extract Column References
    
    loop For Each Output Column
        Script->>Script: Check for <generate> Tags
        
        alt No <generate> Tags Found
            Script->>Script: Skip Column
        else <generate> Tags Found
            Script->>Script: Filter Rows with <generate> Tag
            Script->>Script: Create Filled Templates
            Script->>LLM: Process Templates
            LLM->>Script: Return Results
            Script->>Script: Store Results
        end
    end
    
    Script->>Sheet: Update Cells
```

## Context and Considerations

1. **Robust Detection**: The implementation includes multiple checks to ensure that `<generate>` tags are correctly detected, including handling edge cases like whitespace.
2. **Debugging Support**: The code includes extensive logging to help debug issues with tag detection.
3. **Efficiency**: By only processing cells with the `<generate>` tag, the implementation is more efficient, especially for large sheets where only a few cells need updating.
4. **User Control**: This feature gives users fine-grained control over which cells get processed, allowing for targeted updates.
5. **Integration with Dependencies**: The implementation preserves existing cell values for cells without the `<generate>` tag, which is important for dependency handling between columns.
6. **Future Enhancements**: 
   - Support for additional flags or tags (e.g., `<update>`, `<refresh>`)
   - Support for conditional generation based on cell content
   - Support for batch operations (e.g., `<generate-all>` to process all cells in a column)
   - Integration with version control to track changes