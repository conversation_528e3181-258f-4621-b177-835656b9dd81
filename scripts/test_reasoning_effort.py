#!/usr/bin/env python3
"""
Test script to verify that the reasoning_effort parameter works correctly.
"""

import os
import sys
import asyncio
from dotenv import load_dotenv

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Now we can import from llm_processor
from llm_processor.processor import _call_litellm_acompletion

# Load environment variables
load_dotenv()


async def test_direct_api_call():
    """Test the reasoning_effort parameter with a direct API call."""
    print("\n=== Testing direct API call with reasoning_effort ===")

    try:
        # Test with o1 model and reasoning_effort parameter
        response = await _call_litellm_acompletion(
            model="o3",
            messages=[
                {"role": "user", "content": "Design a streaming ETL pipeline that handles 1 GB/s log throughput."}
            ],
            temperature=0.7,  # This should be ignored for o1
            max_tokens=6000,
            reasoning_effort="high"
        )

        print("Response from o3 model with reasoning_effort='high':")
        print(f"Content: {response['choices'][0]['message']['content'][:200]}...")
    except Exception as e:
        print(f"Error with o3 model: {str(e)}")

    try:
        # Test with a model that doesn't support reasoning_effort
        response = await _call_litellm_acompletion(
            model="gpt-4o",
            messages=[
                {"role": "user", "content": "Design a streaming ETL pipeline that handles 1 GB/s log throughput."}
            ],
            temperature=0.7,
            max_tokens=1000,
            reasoning_effort="high"  # This should be ignored for gpt-4o
        )

        print("\nResponse from gpt-4o model (reasoning_effort should be ignored):")
        print(f"Content: {response['choices'][0]['message']['content'][:200]}...")
    except Exception as e:
        print(f"Error with gpt-4o model: {str(e)}")


async def main():
    """Run the tests."""
    await test_direct_api_call()

if __name__ == "__main__":
    asyncio.run(main())
