"""
This can be found in /users/dev/Documents/Code2/raycast**
This is a script that can be used to run a sheet in the llm-agent-excel repository.
"""
#!/usr/bin/env zsh
# @raycast.schemaVersion 1
# @raycast.title Just Run Sheet
# @raycast.mode fullOutput
# @raycast.packageName dev-automation
# @raycast.icon 📜
# @raycast.argument1 {"type": "text", "placeholder": "Sheet name"}

# Absolute path to the repository containing the justfile
REPO_DIR="/Users/<USER>/Documents/Code2/llm-agent-excel"

if [ ! -d "$REPO_DIR" ]; then
  echo "Repository directory not found: $REPO_DIR" >&2
  exit 1
fi

# Change to the repository so `just` picks up the correct justfile
cd "$REPO_DIR" || exit 1

# Pass the sheet argument through to `just run`
if [ -z "$1" ]; then
  # If no sheet provided, run without the argument to allow interactive selection
  just run
else
  just run "$1"
fi 