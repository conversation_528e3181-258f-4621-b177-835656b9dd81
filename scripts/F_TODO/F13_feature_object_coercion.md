# Feature: Object Coercion for Structured Outputs

Original requirement:
> Object coersion (like but not eval()) -> Structured outputs are automatically coerced into the object (dict, list_dict, list, whatever it is)

## Implementation Status: Not Implemented

The current implementation in `main2.py` and related files does not include automatic object coercion for structured outputs. The code treats all LLM outputs as strings without attempting to parse them into structured objects like dictionaries or lists.

## Feature Description

This feature would automatically detect and convert structured outputs (like JSON) from LLM responses into appropriate Python objects (dictionaries, lists, etc.). This would make it easier to work with structured data returned by LLMs, allowing for more complex data processing and validation.

Key aspects of this feature include:
1. Detecting when an LLM response contains structured data
2. Safely parsing the structured data into appropriate Python objects
3. Handling parsing errors gracefully
4. Providing access to both the raw string and the parsed object

## Implementation Recommendation

### 1. Add Object Coercion Function

```python
import json
import ast
from typing import Any, Tuple, Optional

def coerce_to_object(text: str) -> Tuple[Any, bool]:
    """
    Attempt to coerce a string into a Python object.
    
    Args:
        text: String to coerce
        
    Returns:
        Tuple of (coerced_object, success_flag)
    """
    if not text or not isinstance(text, str):
        return text, False
    
    text = text.strip()
    
    # Try to parse as JSON
    try:
        # Check if it looks like JSON (starts with { or [)
        if (text.startswith('{') and text.endswith('}')) or (text.startswith('[') and text.endswith(']')):
            obj = json.loads(text)
            return obj, True
    except json.JSONDecodeError:
        pass
    
    # Try to parse as Python literal (safer than eval)
    try:
        # Only attempt if it looks like a Python literal
        if (text.startswith('{') and text.endswith('}')) or \
           (text.startswith('[') and text.endswith(']')) or \
           (text.startswith('(') and text.endswith(')')):
            obj = ast.literal_eval(text)
            return obj, True
    except (SyntaxError, ValueError):
        pass
    
    # If it's a simple string that represents a boolean, convert it
    if text.lower() == 'true':
        return True, True
    elif text.lower() == 'false':
        return False, True
    
    # Try to convert to number
    try:
        if '.' in text:
            obj = float(text)
            return obj, True
        else:
            obj = int(text)
            return obj, True
    except ValueError:
        pass
    
    # If all else fails, return the original string
    return text, False
```

### 2. Add Structured Output Class

```python
class StructuredOutput:
    """
    Class to represent a structured output from an LLM.
    
    This class holds both the raw string and the parsed object,
    and provides methods to access and manipulate the data.
    """
    
    def __init__(self, raw_text: str):
        """
        Initialize with raw text from LLM.
        
        Args:
            raw_text: Raw text from LLM
        """
        self.raw_text = raw_text
        self.object, self.is_structured = coerce_to_object(raw_text)
    
    def __str__(self) -> str:
        """String representation is the raw text."""
        return self.raw_text
    
    def __repr__(self) -> str:
        """Representation shows both raw and parsed forms."""
        return f"StructuredOutput(raw={repr(self.raw_text)}, parsed={repr(self.object)}, is_structured={self.is_structured})"
    
    def get_raw(self) -> str:
        """Get the raw text."""
        return self.raw_text
    
    def get_object(self) -> Any:
        """Get the parsed object."""
        return self.object
    
    def is_object(self) -> bool:
        """Check if the output was successfully parsed as an object."""
        return self.is_structured
    
    def get_field(self, field_name: str, default: Any = None) -> Any:
        """
        Get a field from the parsed object.
        
        Args:
            field_name: Name of the field to get
            default: Default value if field doesn't exist
            
        Returns:
            Field value or default
        """
        if not self.is_structured or not isinstance(self.object, dict):
            return default
        
        return self.object.get(field_name, default)
    
    def get_item(self, index: int, default: Any = None) -> Any:
        """
        Get an item from the parsed object if it's a list.
        
        Args:
            index: Index of the item to get
            default: Default value if index is out of range
            
        Returns:
            Item value or default
        """
        if not self.is_structured or not isinstance(self.object, (list, tuple)):
            return default
        
        try:
            return self.object[index]
        except IndexError:
            return default
    
    def to_json(self) -> str:
        """
        Convert the parsed object to a JSON string.
        
        Returns:
            JSON string or the raw text if not structured
        """
        if not self.is_structured:
            return self.raw_text
        
        try:
            return json.dumps(self.object)
        except (TypeError, OverflowError):
            return self.raw_text
```

### 3. Modify LLM Processor to Use Structured Outputs

Update the `_process_chain_step` method in `processor.py` to use structured outputs:

```python
async def _process_chain_step(
    self,
    c_row: pd.Series,
    step: ChainStep,
    mapping: dict,
    max_attempts: int,
) -> None:
    """
    Process a single chain step for a given row.
    
    Args:
        c_row (pd.Series): The current row of the DataFrame.
        step (ChainStep): The chain step to process.
        mapping (dict): The mapping of prompt keywords to column names.
        max_attempts (int): The maximum number of retry attempts for failed API calls.
    """
    prompt = c_row.get(step.pt)
    if c_row.get(step.col) and not step.overwrite:
        logger.debug(f"{step.col} already exists (skip)")
        return
    for k, v in mapping.items():
        prompt = prompt.replace(f"{{{k}}}", str(c_row.get(v, "N/A")))

    temperature = (
        c_row.get("__temperature")
        or step.temperature
        or self.def_temperature
    )
    max_tokens = (
        c_row.get("__max_tokens") or step.max_tokens or self.def_max_tokens
    )
    model = c_row.get("__model") or step.model or self.def_model

    try:
        if max_attempts > 1:
            foo = tenacity.retry(
                stop=tenacity.stop_after_attempt(max_attempts),
                wait=tenacity.wait_exponential(exp_base=2, multiplier=2),
                after=tenacity.after_log(logger=logger, log_level=1),
            )(self._async_get_response)
            response = await foo(
                message=prompt,
                temperature=temperature,
                max_tokens=max_tokens,
                model=model,
                returns_list=step.fanout,
            )
        else:
            response = await self._async_get_response(
                message=prompt,
                temperature=temperature,
                max_tokens=max_tokens,
                model=model,
                returns_list=step.fanout,
            )
    except Exception as e:
        error_msg = str(e).replace("\n", " ")
        logger.error(f"Error in _process_chain_step: {error_msg}")
        c_row[step.col] = f"[N/A] [ERROR: {error_msg}]"
    else:
        # Wrap response in StructuredOutput if not a list
        if not step.fanout:
            response = StructuredOutput(response)
        else:
            # If it's a list, wrap each item
            response = [StructuredOutput(item) for item in response]
        
        c_row[step.col] = response
```

### 4. Add Configuration for Object Coercion

Add configuration options to control object coercion:

```python
class ChainStep(NamedTuple):
    """
    Represents a single step in an LLM processing chain.
    """
    pt: str  # prompt template
    mapping: Optional[Dict[str, str]] = None  # prompt keyword mapping
    temperature: Optional[float] = None  # model temperature
    max_tokens: Optional[int] = None  # model max_tokens
    model: Optional[str] = None  # model name
    col: str = "response"  # response column name
    fanout: bool = False  # fanout response column as a list
    unfan_col: Optional[str] = None  # if fanout, specify the column to un-fan
    overwrite: bool = False  # overwrite existing response column
    validators: List = None  # LLM validators to apply to the response
    coerce_objects: bool = True  # whether to coerce structured outputs to objects
```

### 5. Update Template Filling to Handle Structured Outputs

Modify the `fill_template` function to handle structured outputs:

```python
def fill_template(row, template, input_cols):
    """
    Fill template with values from a DataFrame row.

    Args:
        row: DataFrame row with input values
        template: String template with {variable} placeholders
        input_cols: List of column names to use for variable replacement

    Returns:
        Filled template with all variables replaced
    """
    for col in input_cols:
        if col in row:
            value = row[col]
            
            # Handle StructuredOutput objects
            if isinstance(value, StructuredOutput):
                # Use the raw text for template filling
                value = value.get_raw()
            
            template = template.replace("{{" + col + "}}", str(value))
        else:
            # Replace with empty string if column doesn't exist
            template = template.replace("{{" + col + "}}", "")

    # Find any remaining template variables and replace them with empty strings
    remaining_vars = re.findall(r"\{\{([^}]+)\}\}", template)
    for var in remaining_vars:
        template = template.replace("{{" + var + "}}", "")

    return template
```

### 6. Add Support for Accessing Structured Data in Templates

Enhance the template filling to support accessing fields in structured outputs:

```python
def enhanced_fill_template(row, template, input_cols):
    """
    Fill template with values from a DataFrame row, with support for structured outputs.

    Args:
        row: DataFrame row with input values
        template: String template with {variable} placeholders
        input_cols: List of column names to use for variable replacement

    Returns:
        Filled template with all variables replaced
    """
    # Find all template variables
    variables = re.findall(r"\{\{([^}]+)\}\}", template)
    
    for var in variables:
        # Check if it's a structured access (contains dots)
        if "." in var:
            parts = var.split(".")
            col_name = parts[0]
            access_path = parts[1:]
            
            if col_name in row and col_name in input_cols:
                value = row[col_name]
                
                # Handle StructuredOutput objects
                if isinstance(value, StructuredOutput) and value.is_object():
                    # Get the parsed object
                    obj = value.get_object()
                    
                    # Navigate the access path
                    for part in access_path:
                        # Handle list indexing
                        if part.isdigit() and isinstance(obj, (list, tuple)):
                            index = int(part)
                            if 0 <= index < len(obj):
                                obj = obj[index]
                            else:
                                obj = None
                                break
                        # Handle dictionary access
                        elif isinstance(obj, dict) and part in obj:
                            obj = obj[part]
                        else:
                            obj = None
                            break
                    
                    # Replace variable in template
                    if obj is not None:
                        template = template.replace("{{" + var + "}}", str(obj))
                    else:
                        template = template.replace("{{" + var + "}}", "")
                else:
                    # Not a structured object or couldn't be parsed
                    template = template.replace("{{" + var + "}}", "")
            else:
                # Column doesn't exist
                template = template.replace("{{" + var + "}}", "")
        else:
            # Regular variable (no dots)
            col_name = var
            
            if col_name in row and col_name in input_cols:
                value = row[col_name]
                
                # Handle StructuredOutput objects
                if isinstance(value, StructuredOutput):
                    # Use the raw text for regular variables
                    value = value.get_raw()
                
                template = template.replace("{{" + var + "}}", str(value))
            else:
                # Column doesn't exist
                template = template.replace("{{" + var + "}}", "")
    
    return template
```

### 7. Update Sheet Update Function to Handle Structured Outputs

Modify the sheet update function to handle structured outputs:

```python
def prepare_value_for_sheet(value):
    """
    Prepare a value for updating to a sheet.
    
    Args:
        value: Value to prepare
        
    Returns:
        String representation suitable for sheet update
    """
    if isinstance(value, StructuredOutput):
        # For structured outputs, use the raw text
        return value.get_raw()
    elif isinstance(value, (dict, list, tuple)):
        # For other structured data, convert to JSON
        try:
            return json.dumps(value)
        except (TypeError, OverflowError):
            return str(value)
    else:
        # For everything else, convert to string
        return str(value)

# In the update_to_sheet function, modify the value handling:
value = prepare_value_for_sheet(cell_value)
```

## Architecture Diagram

```mermaid
flowchart TD
    A[LLM Response] --> B[Coerce to Object]
    B --> C{Is Structured?}
    
    C -->|Yes| D[Create StructuredOutput]
    C -->|No| E[Keep as String]
    
    D --> F[Store in DataFrame]
    E --> F
    
    F --> G[Template Processing]
    G --> H{Access Type?}
    
    H -->|Raw Text| I[Use Raw Text]
    H -->|Structured Access| J[Navigate Object]
    
    I --> K[Fill Template]
    J --> K
    
    K --> L[Process with LLM]
    L --> M[Update Sheet]
    
    subgraph Object Coercion
        B
        C
        D
        E
    end
    
    subgraph Template Processing
        G
        H
        I
        J
        K
    end
```

## Example Usage

```
# Template with basic variable
"The customer's name is {{name}}."

# Template with structured access
"The customer's age is {{customer_data.age}}."

# Template with nested structured access
"The customer's city is {{customer_data.address.city}}."

# Template with list access
"The first item is {{items.0}}."
```

## Context and Considerations

1. **Safety**: The implementation uses `ast.literal_eval` instead of `eval` for safety, as mentioned in the requirement.
2. **Flexibility**: The `StructuredOutput` class provides access to both the raw text and the parsed object, giving users flexibility in how they use the data.
3. **Error Handling**: The implementation handles parsing errors gracefully, falling back to the raw text when parsing fails.
4. **Performance**: Object coercion adds some overhead, so it should be configurable and optimized.
5. **Integration with Templates**: The enhanced template filling allows for accessing fields in structured outputs, making it easier to work with complex data.
6. **Backward Compatibility**: The implementation maintains backward compatibility with existing code that expects string outputs.
7. **Future Enhancements**: 
   - Support for more complex data structures
   - Schema validation for structured outputs
   - Custom serialization/deserialization for specific types
   - Integration with database or API endpoints