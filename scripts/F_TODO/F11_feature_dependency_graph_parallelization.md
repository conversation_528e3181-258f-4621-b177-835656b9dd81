# Feature: Dependency Graph Parallelization

Original requirement:
> How can we better async parellise by 'dependency graph' ? -> potentially construct a pipeline / chain of function calls but also could just have conditional flags for each task (how to make that non-blocking? WHY: if-rerunning a large 1000 row pipeline end-end, waiting for the slowest return from each col to complete the gather before continuing is painful.

## Implementation Status: Not Implemented

The current implementation in `main2.py` processes columns sequentially to handle dependencies between columns. It doesn't use a dependency graph to optimize parallelization. The code in `processor.py` does parallelize the processing of rows within a column, but it doesn't optimize the processing order of columns based on their dependencies.

## Feature Description

This feature would enhance the parallelization of LLM processing by using a dependency graph to determine which columns can be processed in parallel. Instead of processing columns strictly sequentially, the system would analyze the dependencies between columns (based on template variables) and create an optimized execution plan that maximizes parallelism while respecting dependencies.

Key aspects of this feature include:
1. Analyzing template variables to build a dependency graph
2. Determining which columns can be processed in parallel
3. Creating an optimized execution plan
4. Implementing non-blocking processing of columns
5. Handling partial results and incremental updates

## Implementation Recommendation

### 1. Define Dependency Graph Classes

```python
from typing import Dict, List, Set, Optional, Any
import networkx as nx
import asyncio
from loguru import logger
from colorama import Fore, Style

class ColumnNode:
    """
    Represents a column in the dependency graph.
    """
    
    def __init__(self, name: str, template: str = "", model: str = "", temperature: float = 0.7, max_tokens: int = 1000):
        """
        Initialize a column node.
        
        Args:
            name: Column name
            template: Prompt template
            model: LLM model
            temperature: Temperature setting
            max_tokens: Maximum tokens
        """
        self.name = name
        self.template = template
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.dependencies: Set[str] = set()
        self.dependents: Set[str] = set()
        self.is_processed = False
        self.is_processing = False
    
    def __str__(self) -> str:
        return f"Column({self.name})"
    
    def __repr__(self) -> str:
        return self.__str__()
    
    def add_dependency(self, column_name: str) -> None:
        """Add a dependency on another column."""
        self.dependencies.add(column_name)
    
    def add_dependent(self, column_name: str) -> None:
        """Add a dependent column."""
        self.dependents.add(column_name)
    
    def can_process(self, processed_columns: Set[str]) -> bool:
        """
        Check if this column can be processed.
        
        Args:
            processed_columns: Set of already processed column names
            
        Returns:
            bool: True if all dependencies are processed
        """
        return all(dep in processed_columns for dep in self.dependencies)

class DependencyGraph:
    """
    Graph representing dependencies between columns.
    """
    
    def __init__(self):
        """Initialize an empty dependency graph."""
        self.columns: Dict[str, ColumnNode] = {}
        self.graph = nx.DiGraph()
    
    def add_column(self, column: ColumnNode) -> None:
        """
        Add a column to the graph.
        
        Args:
            column: Column node to add
        """
        self.columns[column.name] = column
        self.graph.add_node(column.name)
    
    def add_dependency(self, dependent: str, dependency: str) -> None:
        """
        Add a dependency edge to the graph.
        
        Args:
            dependent: Name of the dependent column
            dependency: Name of the dependency column
        """
        if dependent in self.columns and dependency in self.columns:
            self.columns[dependent].add_dependency(dependency)
            self.columns[dependency].add_dependent(dependent)
            self.graph.add_edge(dependency, dependent)
    
    def get_execution_order(self) -> List[List[str]]:
        """
        Get the optimized execution order as levels of columns that can be processed in parallel.
        
        Returns:
            List of lists of column names, where each inner list represents a level
        """
        if not nx.is_directed_acyclic_graph(self.graph):
            # Handle cycles by breaking them
            logger.warning(f"{Fore.YELLOW}Dependency graph contains cycles. Breaking cycles...{Style.RESET_ALL}")
            # Find and break cycles
            cycles = list(nx.simple_cycles(self.graph))
            for cycle in cycles:
                if len(cycle) > 1:
                    # Break the cycle by removing the last edge
                    self.graph.remove_edge(cycle[-1], cycle[0])
                    logger.warning(f"{Fore.YELLOW}Broke cycle: {' -> '.join(cycle)} -> {cycle[0]}{Style.RESET_ALL}")
        
        # Get topological generations (levels)
        levels = list(nx.topological_generations(self.graph))
        return levels
    
    def visualize(self) -> str:
        """
        Generate a Mermaid diagram of the dependency graph.
        
        Returns:
            Mermaid diagram as a string
        """
        mermaid = ["graph TD"]
        
        # Add nodes
        for name, column in self.columns.items():
            mermaid.append(f'    {name}["{name}"]')
        
        # Add edges
        for name, column in self.columns.items():
            for dependency in column.dependencies:
                mermaid.append(f"    {dependency} --> {name}")
        
        return "\n".join(mermaid)
    
    def extract_dependencies_from_template(self, column_name: str, template: str) -> Set[str]:
        """
        Extract dependencies from a template.
        
        Args:
            column_name: Name of the column
            template: Template string
            
        Returns:
            Set of dependency column names
        """
        import re
        
        # Find all template variables
        variables = re.findall(r"\{\{([^}]+)\}\}", template)
        
        # Extract base variable names (before any dots)
        dependencies = set()
        for var in variables:
            base_var = var.split(".")[0]
            if base_var != column_name and base_var in self.columns:
                dependencies.add(base_var)
        
        return dependencies
    
    def build_from_templates(self, columns: Dict[str, Dict[str, Any]]) -> None:
        """
        Build the dependency graph from column templates.
        
        Args:
            columns: Dictionary mapping column names to their properties
        """
        # First pass: add all columns
        for name, props in columns.items():
            column = ColumnNode(
                name=name,
                template=props.get("template", ""),
                model=props.get("model", ""),
                temperature=props.get("temperature", 0.7),
                max_tokens=props.get("max_tokens", 1000)
            )
            self.add_column(column)
        
        # Second pass: extract dependencies
        for name, props in columns.items():
            template = props.get("template", "")
            dependencies = self.extract_dependencies_from_template(name, template)
            
            for dependency in dependencies:
                self.add_dependency(name, dependency)
```

### 2. Implement Parallel Column Processor

```python
class ParallelColumnProcessor:
    """
    Processor for executing columns in parallel based on a dependency graph.
    """
    
    def __init__(self, dependency_graph: DependencyGraph, df: pd.DataFrame, llm_processor: ParallelLLMDataFrameProcessor):
        """
        Initialize the parallel column processor.
        
        Args:
            dependency_graph: Dependency graph of columns
            df: DataFrame with the data
            llm_processor: LLM processor for executing chains
        """
        self.graph = dependency_graph
        self.df = df
        self.llm_processor = llm_processor
        self.processed_columns: Set[str] = set()
        self.processing_columns: Set[str] = set()
        self.results: Dict[str, pd.DataFrame] = {}
    
    async def process_column(self, column_name: str) -> None:
        """
        Process a single column.
        
        Args:
            column_name: Name of the column to process
        """
        column = self.graph.columns[column_name]
        
        logger.info(f"{Fore.GREEN}Processing column: {column_name}{Style.RESET_ALL}")
        
        # Mark as processing
        self.processing_columns.add(column_name)
        column.is_processing = True
        
        try:
            # Create chain step
            chain = [
                ChainStep(
                    pt=f"filled_template_{column_name}",
                    model=column.model,
                    temperature=column.temperature,
                    max_tokens=column.max_tokens,
                    col=column_name,
                ),
            ]
            
            # Get rows to process
            rows_to_process = self.df[self.df[f"original_{column_name}"] == "<generate>"]
            
            if len(rows_to_process) == 0:
                logger.info(f"{Fore.YELLOW}No rows to process for {column_name}. Skipping.{Style.RESET_ALL}")
                self.results[column_name] = pd.DataFrame()
            else:
                # Fill template with dependencies
                template_col = f"filled_template_{column_name}"
                
                # Get all input columns and processed dependency columns
                input_cols = [col for col in self.df.columns if "inputs" in col]
                dependency_cols = [dep for dep in column.dependencies if dep in self.processed_columns]
                
                # Combine input columns and processed dependencies
                available_cols = input_cols + dependency_cols
                
                # Fill templates
                rows_to_process[template_col] = rows_to_process.apply(
                    lambda row: fill_template(row, column.template, available_cols),
                    axis=1,
                )
                
                # Process with LLM
                result_df = self.llm_processor.execute_chain(
                    rows_to_process,
                    chain,
                    max_attempts=3,
                )
                
                self.results[column_name] = result_df
            
            # Mark as processed
            self.processed_columns.add(column_name)
            column.is_processed = True
            
            logger.info(f"{Fore.GREEN}Completed processing column: {column_name}{Style.RESET_ALL}")
            
            # Update the main DataFrame with results
            self.update_dataframe(column_name)
            
        except Exception as e:
            logger.error(f"{Fore.RED}Error processing column {column_name}: {str(e)}{Style.RESET_ALL}")
            import traceback
            logger.error(f"{Fore.RED}Traceback: {traceback.format_exc()}{Style.RESET_ALL}")
        finally:
            # Remove from processing set
            self.processing_columns.remove(column_name)
            column.is_processing = False
    
    def update_dataframe(self, column_name: str) -> None:
        """
        Update the main DataFrame with results from a processed column.
        
        Args:
            column_name: Name of the processed column
        """
        result_df = self.results.get(column_name)
        
        if result_df is not None and not result_df.empty:
            # Update the main DataFrame
            for idx, row in result_df.iterrows():
                if column_name in row and pd.notna(row[column_name]):
                    try:
                        self.df.at[idx, column_name] = row[column_name]
                    except:
                        logger.warning(f"{Fore.YELLOW}Could not update DataFrame at index {idx} for column {column_name}{Style.RESET_ALL}")
    
    async def process_level(self, level: List[str]) -> None:
        """
        Process a level of columns in parallel.
        
        Args:
            level: List of column names in the level
        """
        logger.info(f"{Fore.CYAN}Processing level with columns: {level}{Style.RESET_ALL}")
        
        # Create tasks for each column in the level
        tasks = [self.process_column(column_name) for column_name in level]
        
        # Execute tasks in parallel
        await asyncio.gather(*tasks)
        
        logger.info(f"{Fore.CYAN}Completed level with columns: {level}{Style.RESET_ALL}")
    
    async def process_all(self) -> pd.DataFrame:
        """
        Process all columns in the dependency graph.
        
        Returns:
            DataFrame with the results
        """
        # Get execution order
        levels = self.graph.get_execution_order()
        
        logger.info(f"{Fore.CYAN}Execution plan: {levels}{Style.RESET_ALL}")
        
        # Process each level in sequence
        for level in levels:
            await self.process_level(level)
        
        return self.df
```

### 3. Integrate with Main Function

```python
async def async_main():
    """Asynchronous main function for parallel processing."""
    sheet_id = get_sheet_id_from_url(URL_PATH)
    logger.info(f"{Fore.GREEN}Sheet ID: {sheet_id}{Style.RESET_ALL}")

    # Define the sheet name to use
    sheet_name = "Sheet1"

    # Get data from sheets
    df = get_data_from_sheet(sheet_id, range_name=sheet_name)
    if len(df) == 0:
        logger.info("No prompts to generate")
        sys.exit(0)

    # ... existing code to extract column references and configuration ...

    # Build column properties dictionary
    column_props = {}
    for idx, (
        prompt_template,
        model,
        temperature,
        max_tokens,
        flags,
        output_col,
    ) in enumerate(
        zip(
            prompt_templates,
            models,
            temperatures,
            max_output_tokens,
            flags,
            prompt_output_cols,
        )
    ):
        column_props[output_col] = {
            "template": prompt_template,
            "model": model,
            "temperature": temperature,
            "max_tokens": max_tokens,
            "flags": flags,
        }
    
    # Create dependency graph
    dependency_graph = DependencyGraph()
    dependency_graph.build_from_templates(column_props)
    
    # Log dependency graph
    logger.info(f"{Fore.CYAN}Dependency Graph:\n{dependency_graph.visualize()}{Style.RESET_ALL}")
    
    # Create LLM processor
    llm_proc = ParallelLLMDataFrameProcessor(
        def_model="gpt-4o",
        def_temperature=0.7,
        def_max_tokens=1000,
        def_async_rate_limit=10,
        def_thread_rate_limit=5,
    )
    
    # Create parallel column processor
    processor = ParallelColumnProcessor(dependency_graph, prompt_inputs, llm_proc)
    
    # Process all columns
    result_df = await processor.process_all()
    
    # ... existing code to update the sheet ...

def main():
    """Main execution function with enhanced quality checks."""
    # Run the async main function
    asyncio.run(async_main())

if __name__ == "__main__":
    main()
```

### 4. Add Visualization of the Dependency Graph

```python
def visualize_dependency_graph(dependency_graph: DependencyGraph, output_file: str = "dependency_graph.md") -> None:
    """
    Visualize the dependency graph and save it to a file.
    
    Args:
        dependency_graph: Dependency graph to visualize
        output_file: Output file path
    """
    mermaid_diagram = dependency_graph.visualize()
    
    with open(output_file, "w") as f:
        f.write("# Column Dependency Graph\n\n")
        f.write("```


mermaid\n")
        f.write(mermaid_diagram)
        f.write("\n```\n")
    
    logger.info(f"{Fore.GREEN}Dependency graph visualization saved to {output_file}{Style.RESET_ALL}")

# Add to async_main function

visualize_dependency_graph(dependency_graph, "column_dependencies.md")
```

### 5. Add Progress Tracking and Visualization

```python
class ProgressTracker:
    """
    Track and visualize progress of column processing.
    """
    
    def __init__(self, dependency_graph: DependencyGraph):
        """
        Initialize the progress tracker.
        
        Args:
            dependency_graph: Dependency graph of columns
        """
        self.graph = dependency_graph
        self.start_times: Dict[str, float] = {}
        self.end_times: Dict[str, float] = {}
        self.status: Dict[str, str] = {col: "pending" for col in self.graph.columns}
    
    def start_column(self, column_name: str) -> None:
        """Mark a column as started."""
        self.start_times[column_name] = time.time()
        self.status[column_name] = "processing"
    
    def complete_column(self, column_name: str) -> None:
        """Mark a column as completed."""
        self.end_times[column_name] = time.time()
        self.status[column_name] = "completed"
    
    def fail_column(self, column_name: str) -> None:
        """Mark a column as failed."""
        self.end_times[column_name] = time.time()
        self.status[column_name] = "failed"
    
    def get_progress(self) -> Dict[str, Any]:
        """Get the current progress."""
        total = len(self.graph.columns)
        completed = sum(1 for status in self.status.values() if status == "completed")
        processing = sum(1 for status in self.status.values() if status == "processing")
        failed = sum(1 for status in self.status.values() if status == "failed")
        pending = total - completed - processing - failed
        
        return {
            "total": total,
            "completed": completed,
            "processing": processing,
            "failed": failed,
            "pending": pending,
            "percent_complete": (completed / total) * 100 if total > 0 else 0,
        }
    
    def print_progress(self) -> None:
        """Print the current progress."""
        progress = self.get_progress()
        
        logger.info(
            f"{Fore.CYAN}Progress: {progress['completed']}/{progress['total']} columns completed "
            f"({progress['percent_complete']:.1f}%) | "
            f"{progress['processing']} processing | "
            f"{progress['failed']} failed | "
            f"{progress['pending']} pending{Style.RESET_ALL}"
        )
    
    def visualize_progress(self) -> str:
        """
        Generate a Mermaid diagram showing the current progress.
        
        Returns:
            Mermaid diagram as a string
        """
        mermaid = ["graph TD"]
        
        # Add nodes with status colors
        for name, status in self.status.items():
            color = {
                "pending": "gray",
                "processing": "yellow",
                "completed": "green",
                "failed": "red",
            }.get(status, "gray")
            
            mermaid.append(f'    {name}["{name}"] style {name} fill:{color}')
        
        # Add edges
        for name, column in self.graph.columns.items():
            for dependency in column.dependencies:
                mermaid.append(f"    {dependency} --> {name}")
        
        return "\n".join(mermaid)
```

### 6. Add Incremental Updates to the Sheet

```python
async def update_sheet_incrementally(sheet_id: str, sheet_name: str, column_name: str, result_df: pd.DataFrame) -> None:
    """
    Update the sheet incrementally with results from a single column.
    
    Args:
        sheet_id: ID of the Google Sheet
        sheet_name: Name of the sheet
        column_name: Name of the column
        result_df: DataFrame with the results
    """
    if result_df.empty:
        logger.info(f"{Fore.YELLOW}No results to update for column {column_name}{Style.RESET_ALL}")
        return
    
    # Prepare update data
    update_data = []
    
    for idx, row in result_df.iterrows():
        if column_name in row and pd.notna(row[column_name]):
            # Get col_ref value
            col_ref_value = row.get("col_ref")
            
            if col_ref_value is not None and not pd.isna(col_ref_value):
                try:
                    # Convert to integer
                    row_ref = int(col_ref_value)
                    
                    # Apply 20-row offset (col_ref=1 corresponds to row 21)
                    sheet_row = row_ref + 20
                    
                    # Get column letter
                    # This is a simplified approach; in practice, you'd need to get the actual column index
                    col_letter = chr(65 + prompt_output_cols.index(column_name) + 1)  # +1 to skip column A
                    
                    # Add to update data
                    update_data.append({
                        "range": f"{sheet_name}!{col_letter}{sheet_row}",
                        "values": [[str(row[column_name])]],
                    })
                except (ValueError, TypeError):
                    logger.warning(f"{Fore.YELLOW}Invalid col_ref value: {col_ref_value}{Style.RESET_ALL}")
    
    if update_data:
        # Execute batch update
        body = {"valueInputOption": "RAW", "data": update_data}
        
        service.spreadsheets().values().batchUpdate(spreadsheetId=sheet_id, body=body).execute()
        
        logger.info(f"{Fore.GREEN}Updated {len(update_data)} cells for column {column_name}{Style.RESET_ALL}")
```

### 7. Modify Parallel Column Processor to Update Sheet Incrementally

```python
class ParallelColumnProcessor:
    # ... existing code ...
    
    async def process_column(self, column_name: str) -> None:
        """
        Process a single column.
        
        Args:
            column_name: Name of the column to process
        """
        column = self.graph.columns[column_name]
        
        logger.info(f"{Fore.GREEN}Processing column: {column_name}{Style.RESET_ALL}")
        
        # Mark as processing
        self.processing_columns.add(column_name)
        column.is_processing = True
        
        try:
            # ... existing processing code ...
            
            # Update the sheet incrementally
            if self.incremental_updates and self.sheet_id and self.sheet_name:
                await update_sheet_incrementally(
                    self.sheet_id,
                    self.sheet_name,
                    column_name,
                    self.results[column_name]
                )
            
            # ... rest of existing code ...
        
        except Exception as e:
            # ... existing error handling ...
```

## Architecture Diagram

```mermaid
classDiagram
    class ColumnNode {
        +str name
        +str template
        +str model
        +float temperature
        +int max_tokens
        +Set[str] dependencies
        +Set[str] dependents
        +bool is_processed
        +bool is_processing
        +add_dependency(column_name)
        +add_dependent(column_name)
        +can_process(processed_columns)
    }
    
    class DependencyGraph {
        +Dict[str, ColumnNode] columns
        +DiGraph graph
        +add_column(column)
        +add_dependency(dependent, dependency)
        +get_execution_order()
        +visualize()
        +extract_dependencies_from_template(column_name, template)
        +build_from_templates(columns)
    }
    
    class ParallelColumnProcessor {
        +DependencyGraph graph
        +DataFrame df
        +ParallelLLMDataFrameProcessor llm_processor
        +Set[str] processed_columns
        +Set[str] processing_columns
        +Dict[str, DataFrame] results
        +process_column(column_name)
        +update_dataframe(column_name)
        +process_level(level)
        +process_all()
    }
    
    class ProgressTracker {
        +DependencyGraph graph
        +Dict[str, float] start_times
        +Dict[str, float] end_times
        +Dict[str, str] status
        +start_column(column_name)
        +complete_column(column_name)
        +fail_column(column_name)
        +get_progress()
        +print_progress()
        +visualize_progress()
    }
    
    DependencyGraph --> ColumnNode : contains
    ParallelColumnProcessor --> DependencyGraph : uses
    ParallelColumnProcessor --> ParallelLLMDataFrameProcessor : uses
    ProgressTracker --> DependencyGraph : tracks
```

## Process Flow

```mermaid
flowchart TD
    A[Start] --> B[Load Sheet Data]
    B --> C[Extract Column Configuration]
    C --> D[Build Dependency Graph]
    D --> E[Visualize Dependency Graph]
    E --> F[Create Parallel Processor]
    
    F --> G[Get Execution Order]
    G --> H{For Each Level}
    H --> I{For Each Column in Level}
    
    I --> J[Process Column in Parallel]
    J --> K[Update DataFrame]
    K --> L[Update Sheet Incrementally]
    
    L --> I
    I --> H
    
    H --> M[Final Sheet Update]
    M --> N[End]
    
    subgraph Dependency Analysis
        D
        E
        G
    end
    
    subgraph Parallel Processing
        H
        I
        J
        K
        L
    end
```

## Example Dependency Graph

```mermaid
graph TD
    name[name] style name fill:green
    age[age] style age fill:green
    profession[profession] style profession fill:green
    skills[skills] style skills fill:yellow
    experience[experience] style experience fill:gray
    career_summary[career_summary] style career_summary fill:gray
    
    name --> skills
    age --> career_summary
    profession --> skills
    profession --> experience
    profession --> career_summary
    skills --> experience
    skills --> career_summary
    experience --> career_summary
```

## Context and Considerations

1. **Dependency Analysis**: The implementation analyzes template variables to automatically build a dependency graph, making it easier to understand and visualize column dependencies.
2. **Parallel Processing**: The implementation processes columns in parallel when possible, while respecting dependencies, improving overall performance.
3. **Incremental Updates**: The implementation updates the sheet incrementally as columns are processed, providing faster feedback to users.
4. **Progress Tracking**: The implementation includes progress tracking and visualization, making it easier to monitor long-running processes.
5. **Error Handling**: The implementation includes comprehensive error handling to ensure that failures in one column don't affect others.
6. **Visualization**: The implementation includes visualization of the dependency graph and progress, making it easier to understand the process.
7. **Integration with Existing Code**: The implementation builds on the existing code, maintaining compatibility with the current sheet format and processing logic.
8. **Future Enhancements**: 
   - Dynamic dependency detection during processing
   - Adaptive parallelism based on system resources
   - Support for more complex dependency types
   - Integration with a workflow engine for more advanced orchestration