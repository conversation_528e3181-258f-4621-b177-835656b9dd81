# Prompt Goal Statement

Original feature description from features_list.md:
```
Prompt Goal Statement
```

## Feature Description

A Prompt Goal Statement is a clear, concise description of what a prompt is intended to achieve. This feature would allow users to define the purpose and expected outcome of each prompt column in the Google Sheet. The goal statements serve multiple purposes:

1. **Documentation**: Clearly document what each prompt is trying to accomplish
2. **Evaluation**: Provide criteria against which to evaluate the LLM's responses
3. **Debugging**: Help identify why a prompt might not be working as expected
4. **Collaboration**: Make it easier for team members to understand each other's prompts
5. **Iteration**: Guide the refinement of prompts over multiple iterations

## Current Implementation Status

Currently, the gsheet-model-breaker-demo does not have a dedicated feature for prompt goal statements. The sheet structure includes rows for prompt templates, models, and other configuration parameters, but no specific row for goal statements:

<augment_code_snippet path="experiments/src/tools/gsheet-model-breaker-demo/scripts/main2.py" mode="EXCERPT">
````python
prompt_templates = list(
    df.iloc[[3]][prompt_output_cols].to_dict("records")[0].values()
)
models = list(df.iloc[[4]][prompt_output_cols].to_dict("records")[0].values())
temperatures = list(
    map(
        float,
        df.iloc[[5]][prompt_output_cols].to_dict("records")[0].values(),
    )
)
max_output_tokens = list(
    map(
        int,
        df.iloc[[6]][prompt_output_cols].to_dict("records")[0].values(),
    )
)
flags = list(df.iloc[[7]][prompt_output_cols].to_dict("records")[0].values())
````
</augment_code_snippet>

## Proposed Implementation

### 1. Sheet Structure Update

Add a new row in the sheet structure for goal statements:

```
Row 1: Column references (e.g., "py-llm")
Row 2: Goal statements (NEW)
Row 3: (Reserved for future use)
Row 4: Prompt templates
Row 5: Models
Row 6: Temperatures
Row 7: Max tokens
Row 8: Flags
```

### 2. Code Changes to Extract Goal Statements

Update the main2.py file to extract and use goal statements:

```python
# Extract goal statements from row 2
goal_statements = list(
    df.iloc[[1]][prompt_output_cols].to_dict("records")[0].values()
)

# Add goal statements to the logging output
logger.info(
    f"{Fore.CYAN}Debug: Goal statements for output columns:{Style.RESET_ALL}"
)
for col, goal in zip(prompt_output_cols, goal_statements):
    logger.info(f"{Fore.CYAN}- {col}: {goal}{Style.RESET_ALL}")
```

### 3. Include Goal Statements in Summary Display

Update the display_summary_table function to include goal statements:

```python
def display_summary_table(direct_update_map, prompt_output_cols, prompt_templates, models, temperatures, max_output_tokens, goal_statements):
    """
    Display a rich table summarizing the updated cells and configuration settings.
    
    Args:
        direct_update_map: Dictionary mapping (row_idx, col_name) to (col_ref, generated_value)
        prompt_output_cols: List of output column names
        prompt_templates: List of prompt templates
        models: List of models used
        temperatures: List of temperature values used
        max_output_tokens: List of max token values used
        goal_statements: List of goal statements for each column
    """
    console.print("\n[bold green]===== MODEL-BREAKER EXECUTION SUMMARY =====[/bold green]")
    
    # 1. Config table - showing models, temperatures, and token limits
    config_table = Table(title="Configuration Settings")
    
    # Add columns
    config_table.add_column("Output Column", style="cyan")
    config_table.add_column("Goal", style="blue")
    config_table.add_column("Model", style="green")
    config_table.add_column("Temperature", style="yellow")
    config_table.add_column("Max Tokens", style="magenta")
    
    # Add rows for each configuration
    for col, goal, model, temp, tokens in zip(prompt_output_cols, goal_statements, models, temperatures, max_output_tokens):
        # Truncate long goal statements for display
        display_goal = goal
        if len(display_goal) > 40:
            display_goal = display_goal[:37] + "..."
        
        config_table.add_row(col, display_goal, model, str(temp), str(tokens))
    
    console.print(config_table)
    
    # Rest of the function remains the same...
```

### 4. Goal-Based Response Evaluation

Implement a function to evaluate responses against their goals:

```python
async def evaluate_response_against_goal(response, goal, model="gpt-4o-mini"):
    """
    Evaluate an LLM response against its stated goal.
    
    Args:
        response (str): The LLM response to evaluate
        goal (str): The goal statement for the prompt
        model (str): The model to use for evaluation
        
    Returns:
        dict: Evaluation results including score and feedback
    """
    evaluation_prompt = f"""
    I need to evaluate an AI response against its intended goal.
    
    GOAL: {goal}
    
    RESPONSE:
    {response}
    
    Please evaluate how well the response achieves the stated goal on a scale of 1-10, 
    where 1 means it completely fails to meet the goal and 10 means it perfectly achieves the goal.
    
    Provide your evaluation in the following format:
    Score: [numerical score 1-10]
    Feedback: [brief explanation of the score, highlighting strengths and weaknesses]
    """
    
    try:
        # Use litellm to get the evaluation
        messages = [
            {"role": "system", "content": "You are an expert evaluator of AI responses."},
            {"role": "user", "content": evaluation_prompt}
        ]
        
        resp = await litellm.acompletion(
            model=model,
            messages=messages,
            temperature=0.1,
            max_tokens=500
        )
        
        evaluation_text = resp["choices"][0]["message"]["content"]
        
        # Parse the evaluation
        import re
        score_match = re.search(r"Score:\s*(\d+(?:\.\d+)?)", evaluation_text)
        feedback_match = re.search(r"Feedback:\s*(.*?)(?=$)", evaluation_text, re.DOTALL)
        
        score = float(score_match.group(1)) if score_match else None
        feedback = feedback_match.group(1).strip() if feedback_match else evaluation_text
        
        return {
            "score": score,
            "feedback": feedback,
            "full_evaluation": evaluation_text
        }
    except Exception as e:
        logger.error(f"Error evaluating response: {str(e)}")
        return {
            "score": None,
            "feedback": f"Evaluation failed: {str(e)}",
            "full_evaluation": None
        }
```

### 5. Integration with Main Processing Flow

Update the main processing flow to use goal statements for evaluation:

```python
# After processing each column, evaluate responses against goals
if goal_statements and idx < len(goal_statements) and goal_statements[idx]:
    logger.info(f"Evaluating responses for {output_col} against goal: {goal_statements[idx]}")
    
    # Create a column for evaluation results
    evaluation_col = f"{output_col}_evaluation"
    
    # Evaluate each response
    for idx_in_result in range(len(result_df)):
        result_row_idx = result_df.index[idx_in_result]
        result_value = result_df.iloc[idx_in_result][output_col]
        
        # Skip empty or error responses
        if pd.isna(result_value) or str(result_value).startswith("[ERROR"):
            continue
        
        # Evaluate the response
        evaluation = await evaluate_response_against_goal(
            result_value, 
            goal_statements[idx]
        )
        
        # Store evaluation in the DataFrame
        result_df.at[result_row_idx, evaluation_col] = f"Score: {evaluation['score']}/10 - {evaluation['feedback']}"
        
        # Log the evaluation
        logger.info(f"Row {result_row_idx} evaluation: Score {evaluation['score']}/10")
```

### 6. Goal-Based Prompt Improvement Suggestions

Implement a function to suggest prompt improvements based on goals and responses:

```python
async def suggest_prompt_improvements(prompt, responses, goal, model="gpt-4o"):
    """
    Suggest improvements to a prompt based on the responses it generated and its goal.
    
    Args:
        prompt (str): The original prompt template
        responses (list): List of responses generated by the prompt
        goal (str): The goal statement for the prompt
        model (str): The model to use for suggestions
        
    Returns:
        str: Suggested improvements to the prompt
    """
    # Sample up to 3 responses for analysis
    sample_responses = responses[:3]
    
    improvement_prompt = f"""
    I need to improve an AI prompt based on the responses it generated and its intended goal.
    
    CURRENT PROMPT:
    {prompt}
    
    GOAL:
    {goal}
    
    SAMPLE RESPONSES:
    {"\n\n---\n\n".join(sample_responses)}
    
    Please analyze how well the prompt achieves its stated goal based on the responses.
    Then suggest specific improvements to the prompt that would help it better achieve the goal.
    
    Your response should include:
    1. Analysis of how the current prompt performs
    2. Specific issues or weaknesses in the prompt
    3. A revised version of the prompt
    4. Explanation of the changes made
    """
    
    try:
        messages = [
            {"role": "system", "content": "You are an expert at designing effective prompts for AI systems."},
            {"role": "user", "content": improvement_prompt}
        ]
        
        resp = await litellm.acompletion(
            model=model,
            messages=messages,
            temperature=0.2,
            max_tokens=1000
        )
        
        return resp["choices"][0]["message"]["content"]
    except Exception as e:
        logger.error(f"Error generating prompt improvements: {str(e)}")
        return f"Failed to generate improvement suggestions: {str(e)}"
```

## Implementation Considerations

1. **Backward Compatibility**: Ensure the code works with sheets that don't have goal statements.

2. **Goal Quality**: Provide guidelines for writing effective goal statements.

3. **Evaluation Costs**: Goal-based evaluation adds additional API calls, which increases costs.

4. **UI Integration**: Consider how goal statements will be displayed and edited in the Google Sheet.

5. **Versioning**: Track changes to goal statements over time to see how they evolve.

## Mermaid Diagram: Goal-Based Evaluation Flow

```mermaid
flowchart TD
    A[Define Prompt Goal<br>in Sheet Row 2] --> B[Extract Goal<br>During Processing]
    B --> C[Execute LLM Chain<br>as Normal]
    C --> D[Generate Responses]
    D --> E[Evaluate Responses<br>Against Goal]
    E --> F{Score >= 7?}
    
    F -->|Yes| G[Store Response<br>and Evaluation]
    F -->|No| H[Suggest Prompt<br>Improvements]
    
    H --> I[Log Improvement<br>Suggestions]
    G --> J[Update Sheet<br>with Results]
    I --> J
    
    subgraph "Optional Feedback Loop"
    J --> K[Review Evaluations<br>and Suggestions]
    K --> L[Refine Goal and<br>Prompt Template]
    L --> A
    end
```

## Example Sheet Structure

Here's how the sheet structure might look with goal statements:

| Column A | Column B | Column C |
|----------|----------|----------|
| col_ref | inputs | py-llm |
| | | Generate a concise summary that captures the main points while maintaining factual accuracy |
| | | |
| Prompt Template | Input data | Summarize the following text: {{input_data}} |
| Model | N/A | gpt-4o |
| Temperature | N/A | 0.3 |
| Max Tokens | N/A | 500 |
| Flags | N/A | |

## Conclusion

Adding Prompt Goal Statements to the gsheet-model-breaker-demo would significantly enhance its usability and effectiveness. By explicitly stating what each prompt is trying to achieve, users can better evaluate, debug, and improve their prompts.

The proposed implementation not only captures goal statements but also leverages them for automated evaluation and improvement suggestions. This creates a more structured approach to prompt engineering, helping users iteratively refine their prompts to better achieve their intended goals.

The feature aligns well with best practices in prompt engineering, where clearly defining the objective is a critical first step. By formalizing this step in the tool's workflow, it encourages users to think more deliberately about what they're trying to accomplish with each prompt.
