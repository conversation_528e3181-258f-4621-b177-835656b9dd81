# Update All Flag

Original feature description:

```text
Add a simple param in the code for 'update all' and a col-based flag (in sheet) for 'update this whole col'.
```

## Feature Description

This feature extends the existing `<generate>` flag functionality with two additional options:

1. **Command-Line Parameter for "Update All"**: A command-line parameter that, when enabled, processes all cells in output columns regardless of whether they have the `<generate>` tag.

2. **Column-Based Flag for "Update Whole Column"**: A flag in the sheet that, when present in a column's configuration, processes all cells in that specific column regardless of individual `<generate>` tags.

These options provide more flexibility for batch processing and column-wide updates, while still maintaining the granular control offered by the existing `<generate>` flag.

## Current Implementation Status

Currently, the gsheet-model-breaker-demo only processes cells that are explicitly marked with the `<generate>` or `//` tag:

<augment_code_snippet path="experiments/src/tools/gsheet-model-breaker-demo/scripts/main2.py" mode="EXCERPT">

````python
# Find rows that have "<generate>" tag or "//" shorthand for this output column
generate_mask = (prompt_inputs[f"original_{output_col}"] == "<generate>") | (prompt_inputs[f"original_{output_col}"] == "//")

# Debug the mask to see if it's finding the rows
logger.info(
    f"{Fore.CYAN}Debug: Generate mask for {output_col} has {generate_mask.sum()} matches{Style.RESET_ALL}"
)

# Double-check by directly searching the column values
direct_check = prompt_inputs[f"original_{output_col}"].apply(
    lambda x: str(x).strip() == "<generate>" or str(x).strip() == "//"
)
logger.info(
    f"{Fore.CYAN}Debug: Direct check for {output_col} has {direct_check.sum()} matches{Style.RESET_ALL}"
)

# If there's a discrepancy, log some values for debugging
if generate_mask.sum() != direct_check.sum():
    logger.warning(
        f"{Fore.YELLOW}Discrepancy in '<generate>' detection for {output_col}. Checking values...{Style.RESET_ALL}"
    )
    for i, val in enumerate(prompt_inputs[f"original_{output_col}"].head(10)):
        logger.info(
            f"{Fore.CYAN}Row {i}, value: '{val}', len: {len(str(val))}, is '<generate>'?: {str(val).strip() == '<generate>'}{Style.RESET_ALL}"
        )

    # Use the more permissive check
    generate_mask = direct_check

if not generate_mask.any():
    logger.info(
        f"{Fore.YELLOW}No rows with '<generate>' tag found for {output_col}. Skipping.{Style.RESET_ALL}"
    )
    continue

generate_indices = generate_mask[generate_mask].index.tolist()
rows_to_update[output_col] = generate_indices
````

</augment_code_snippet>

There is no current support for updating all cells or all cells in a specific column without individual `<generate>` tags.

## Proposed Implementation

### 1. Command-Line Parameter for "Update All"

Add a command-line parameter to enable updating all cells:

```python
import argparse

def parse_arguments():
    parser = argparse.ArgumentParser(description='Run LLM processing on Google Sheets data')
    parser.add_argument('--update-all', action='store_true',
                        help='Process all cells in output columns, ignoring <generate> tags')
    parser.add_argument('--sheet-name', type=str, default="Sheet1",
                        help='Name of the sheet to process (default: Sheet1)')
    return parser.parse_args()

# In main function
def main():
    args = parse_arguments()

    # Use the sheet name from arguments
    sheet_name = args.sheet_name

    # Store the update-all flag for later use
    update_all = args.update_all
    if update_all:
        logger.info(f"{Fore.GREEN}Update-all mode enabled. Will process all cells in output columns.{Style.RESET_ALL}")
```

### 2. Column-Based Flag for "Update Whole Column"

Add support for a column-based flag in the sheet:

```python
# Modify the column processing loop to check for the update-whole-column flag
for idx, (prompt_template, model, temperature, max_tokens, flags, output_col) in enumerate(zip(
    prompt_templates, models, temperatures, max_output_tokens, flags, prompt_output_cols
)):
    # Check if this column has the update-whole-column flag
    update_whole_column = "update-all" in flags.lower() or "update-whole-column" in flags.lower()

    if update_whole_column:
        logger.info(f"{Fore.GREEN}Update-whole-column flag detected for {output_col}. Will process all cells.{Style.RESET_ALL}")
```

### 3. Modify the Generate Mask Logic

Update the generate mask logic to respect both the command-line parameter and column-based flag:

```python
# Find rows that have "<generate>" tag or "//" shorthand for this output column
# Or include all rows if update-all or update-whole-column is enabled
if update_all or update_whole_column:
    # Create a mask that includes all rows
    generate_mask = pd.Series([True] * len(prompt_inputs))
    logger.info(
        f"{Fore.CYAN}Processing all {len(generate_mask)} rows for {output_col} due to update-all/update-whole-column flag.{Style.RESET_ALL}"
    )
else:
    # Use the existing logic for <generate> tags
    generate_mask = (prompt_inputs[f"original_{output_col}"] == "<generate>") | (prompt_inputs[f"original_{output_col}"] == "//")

    # Debug the mask to see if it's finding the rows
    logger.info(
        f"{Fore.CYAN}Debug: Generate mask for {output_col} has {generate_mask.sum()} matches{Style.RESET_ALL}"
    )

    # Double-check by directly searching the column values
    direct_check = prompt_inputs[f"original_{output_col}"].apply(
        lambda x: str(x).strip() == "<generate>" or str(x).strip() == "//"
    )
    logger.info(
        f"{Fore.CYAN}Debug: Direct check for {output_col} has {direct_check.sum()} matches{Style.RESET_ALL}"
    )

    # If there's a discrepancy, log some values for debugging
    if generate_mask.sum() != direct_check.sum():
        logger.warning(
            f"{Fore.YELLOW}Discrepancy in '<generate>' detection for {output_col}. Checking values...{Style.RESET_ALL}"
        )
        for i, val in enumerate(prompt_inputs[f"original_{output_col}"].head(10)):
            logger.info(
                f"{Fore.CYAN}Row {i}, value: '{val}', len: {len(str(val))}, is '<generate>'?: {str(val).strip() == '<generate>'}{Style.RESET_ALL}"
            )

        # Use the more permissive check
        generate_mask = direct_check

if not generate_mask.any():
    logger.info(
        f"{Fore.YELLOW}No rows to process for {output_col}. Skipping.{Style.RESET_ALL}"
    )
    continue
```

### 4. Add a Skip-Empty-Cells Option

To provide more control, add an option to skip cells that are already filled:

```python
# Add to argument parser
parser.add_argument('--skip-filled', action='store_true',
                    help='Skip cells that already have content, even in update-all mode')

# In the generate mask logic
if update_all or update_whole_column:
    if args.skip_filled:
        # Skip cells that already have content
        generate_mask = prompt_inputs[f"original_{output_col}"].apply(
            lambda x: pd.isna(x) or str(x).strip() == "" or str(x).strip() == "<generate>" or str(x).strip() == "//"
        )
        logger.info(
            f"{Fore.CYAN}Processing only empty cells for {output_col} (skip-filled enabled). Found {generate_mask.sum()} cells to process.{Style.RESET_ALL}"
        )
    else:
        # Process all cells
        generate_mask = pd.Series([True] * len(prompt_inputs))
        logger.info(
            f"{Fore.CYAN}Processing all {len(generate_mask)} rows for {output_col}.{Style.RESET_ALL}"
        )
```

### 5. Update the Display Summary to Show Update Mode

Update the display_summary_table function to show the update mode:

```python
def display_summary_table(direct_update_map, prompt_output_cols, prompt_templates, models, temperatures, max_output_tokens, flags, update_all):
    """
    Display a rich table summarizing the updated cells and configuration settings.
    """
    console.print("\n[bold green]===== MODEL-BREAKER EXECUTION SUMMARY =====[/bold green]")

    # Add update mode information
    update_mode_table = Table(title="Update Mode")
    update_mode_table.add_column("Mode", style="cyan")
    update_mode_table.add_column("Description", style="green")

    if update_all:
        update_mode_table.add_row("Update All", "All cells in output columns were processed, ignoring <generate> tags")
    else:
        update_mode_table.add_row("Selective Update", "Only cells with <generate> or // tags were processed")

    # Add column-specific update modes
    for col, flag in zip(prompt_output_cols, flags):
        if "update-all" in flag.lower() or "update-whole-column" in flag.lower():
            update_mode_table.add_row(f"Column: {col}", "All cells in this column were processed")

    console.print(update_mode_table)

    # Rest of the function remains the same...
```

## Implementation Considerations

1. **Performance Impact**: Processing all cells instead of just those with `<generate>` tags can significantly increase processing time and API costs.

2. **Overwriting Existing Data**: The update-all mode risks overwriting existing data that users may want to preserve. The `--skip-filled` option helps mitigate this risk.

3. **Selective Column Updates**: The column-based flag allows for more targeted batch updates, which is useful when only certain columns need to be refreshed.

4. **Backward Compatibility**: The implementation maintains backward compatibility with the existing `<generate>` tag approach.

5. **User Control**: The combination of command-line parameters and column-based flags provides users with multiple levels of control over the update process.

## Mermaid Diagram: Update Flag Processing Flow

```mermaid
flowchart TD
    A[Start Script] --> B{Update-All<br>Parameter?}
    B -->|Yes| C[Set Global<br>Update-All Flag]
    B -->|No| D[Use Default<br>Selective Mode]

    C --> E[Process Each<br>Output Column]
    D --> E

    E --> F{Column Has<br>Update-All Flag?}
    F -->|Yes| G[Set Column-Specific<br>Update-All Flag]
    F -->|No| H{Global<br>Update-All?}

    G --> I[Create Mask<br>for All Rows]
    H -->|Yes| I
    H -->|No| J[Check for<br><generate> Tags]

    J --> K[Create Mask for<br>Tagged Rows Only]
    I --> L{Skip-Filled<br>Parameter?}

    L -->|Yes| M[Exclude Rows<br>with Content]
    L -->|No| N[Include<br>All Rows]

    K --> O[Process Rows<br>in Mask]
    M --> O
    N --> O

    O --> P[Update Sheet<br>with Results]
```

## Example Usage

```bash
# Process only cells with <generate> tags (default behavior)
python main2.py

# Process all cells in all output columns
python main2.py --update-all

# Process all cells but skip those that already have content
python main2.py --update-all --skip-filled

# Process a specific sheet
python main2.py --sheet-name "MySheet"
```

## Example Sheet Configuration

Here's how the sheet configuration might look with the column-based update flag:

| Column A | Column B | Column C | Column D |
|----------|----------|----------|----------|
| col_ref | inputs | py-llm | py-llm |
| | | | |
| | | | |
| Prompt Template | Input data | Generate a summary | Analyze the data |
| Model | N/A | gpt-4o | gpt-4o |
| Temperature | N/A | 0.3 | 0.7 |
| Max Tokens | N/A | 500 | 1000 |
| Flags | N/A | | update-whole-column |

In this example, all cells in Column D would be processed regardless of whether they have `<generate>` tags, while Column C would follow the default behavior of only processing cells with `<generate>` tags (unless the `--update-all` parameter is used).

## Conclusion

Adding the "Update All" parameter and column-based "Update Whole Column" flag would significantly enhance the flexibility of the gsheet-model-breaker-demo tool. These features would allow users to easily switch between selective updates (using `<generate>` tags) and batch updates (processing all cells or all cells in specific columns).

The implementation maintains backward compatibility with the existing approach while adding new options for more efficient batch processing. The addition of the `--skip-filled` parameter provides an extra layer of control to prevent overwriting existing content when using the update-all mode.

These enhancements would make the tool more versatile for different use cases, from targeted updates of specific cells to complete refreshes of entire columns or sheets.

## Regression Tests

1. **DO**: When run without any flags, the tool should ONLY process cells with `<generate>` or `//` tags.

2. **DO**: When run with `--update-all`, the tool should process ALL cells in output columns, regardless of whether they have `<generate>` tags.

3. **DO**: When a column has the `update-whole-column` flag in its configuration, ALL cells in that column should be processed, even without the `--update-all` parameter.

4. **DO**: When run with both `--update-all` and `--skip-filled`, the tool should process only empty cells and cells with `<generate>` tags, skipping cells that already have content.

5. **DO**: The `--update-all` parameter should affect ALL output columns unless they have specific column-based flags.

6. **DO**: Column-based flags should take precedence over the global `--update-all` parameter (e.g., if a column has a specific flag to NOT update all, it should be respected even when `--update-all` is used).

7. **DON'T**: The `--update-all` parameter should NOT affect input columns or configuration rows.

8. **DON'T**: The tool should NOT overwrite existing values in the DataFrame that are used as dependencies for other columns, even when using `--update-all`.

9. **DON'T**: The implementation should NOT break the existing functionality of the `<generate>` tag.

10. **DO**: The summary display should clearly indicate which update mode was used (selective or update-all) and which columns had column-specific update flags.
