# Feature: Structured Schema Configuration

Original requirement:
> "- add config: structured schema
> - accept JSON_MODE etc"

## Implementation Status: Not Implemented

The current implementation in `main2.py` and related files does not include support for structured schema configuration or JSON_MODE. The code does not have specific handling for structured outputs or JSON formatting.

## Feature Description

This feature would add support for structured schema configuration, allowing users to define the expected structure of LLM outputs. It would also add support for JSON_MODE and similar output formatting options, which help ensure that LLM responses conform to a specific structure or format.

Key aspects of this feature include:
1. Defining structured schemas for LLM outputs
2. Configuring LLM requests to use JSON mode or similar structured output modes
3. Validating LLM responses against the defined schemas
4. Handling parsing and error recovery for structured outputs

## Implementation Recommendation

### 1. Define Schema Configuration in the Sheet

Add a new configuration row in the sheet for schema definitions:

```
Row A: "SCHEMA"
Row B-Z: JSON schema definitions for each output column
```

Example schema in a cell:
```json
{
  "type": "object",
  "properties": {
    "name": {"type": "string"},
    "age": {"type": "integer"},
    "skills": {"type": "array", "items": {"type": "string"}}
  },
  "required": ["name", "age"]
}
```

### 2. Add Output Mode Configuration

Add a new configuration row for output modes:

```
Row A: "OUTPUT_MODE"
Row B-Z: Output mode for each column (e.g., "JSON", "TEXT", "MARKDOWN")
```

### 3. Add Schema Parsing and Validation Functions

```python
import json
import jsonschema

def parse_schema_config(df, output_cols):
    """
    Parse schema configuration from the sheet.
    
    Args:
        df: DataFrame with the data
        output_cols: List of output column names
        
    Returns:
        Dictionary mapping output columns to their schemas
    """
    schemas = {}
    
    # Look for SCHEMA row in column A
    schema_rows = df[df['A'] == "SCHEMA"].index.tolist()
    
    if not schema_rows:
        logger.info(f"{Fore.CYAN}No schema configuration found.{Style.RESET_ALL}")
        return schemas
    
    schema_row = schema_rows[0]
    
    # Extract schema for each output column
    for col in output_cols:
        if col in df.columns:
            schema_str = df.loc[schema_row, col]
            
            # Skip empty schemas
            if pd.isna(schema_str) or (isinstance(schema_str, str) and schema_str.strip() == ""):
                continue
            
            try:
                # Parse JSON schema
                schema = json.loads(schema_str)
                schemas[col] = schema
                logger.info(f"{Fore.GREEN}Loaded schema for column {col}{Style.RESET_ALL}")
            except json.JSONDecodeError as e:
                logger.error(f"{Fore.RED}Invalid JSON schema for column {col}: {str(e)}{Style.RESET_ALL}")
    
    return schemas

def parse_output_mode_config(df, output_cols):
    """
    Parse output mode configuration from the sheet.
    
    Args:
        df: DataFrame with the data
        output_cols: List of output column names
        
    Returns:
        Dictionary mapping output columns to their output modes
    """
    output_modes = {}
    
    # Look for OUTPUT_MODE row in column A
    mode_rows = df[df['A'] == "OUTPUT_MODE"].index.tolist()
    
    if not mode_rows:
        logger.info(f"{Fore.CYAN}No output mode configuration found. Using default mode.{Style.RESET_ALL}")
        return {col: "TEXT" for col in output_cols}  # Default to TEXT mode
    
    mode_row = mode_rows[0]
    
    # Extract output mode for each output column
    for col in output_cols:
        if col in df.columns:
            mode = df.loc[mode_row, col]
            
            # Handle empty or invalid modes
            if pd.isna(mode) or (isinstance(mode, str) and mode.strip() == ""):
                output_modes[col] = "TEXT"  # Default
            else:
                # Normalize mode string
                mode_str = str(mode).strip().upper()
                if mode_str in ["JSON", "TEXT", "MARKDOWN"]:
                    output_modes[col] = mode_str
                else:
                    logger.warning(f"{Fore.YELLOW}Invalid output mode '{mode}' for column {col}. Using TEXT.{Style.RESET_ALL}")
                    output_modes[col] = "TEXT"
    
    return output_modes

def validate_against_schema(value, schema):
    """
    Validate a value against a JSON schema.
    
    Args:
        value: The value to validate
        schema: The JSON schema to validate against
        
    Returns:
        (bool, str): Tuple of (is_valid, error_message)
    """
    try:
        # Parse value if it's a string
        if isinstance(value, str):
            parsed_value = json.loads(value)
        else:
            parsed_value = value
        
        # Validate against schema
        jsonschema.validate(instance=parsed_value, schema=schema)
        return True, None
    except json.JSONDecodeError as e:
        return False, f"JSON parsing error: {str(e)}"
    except jsonschema.exceptions.ValidationError as e:
        return False, f"Schema validation error: {str(e)}"
    except Exception as e:
        return False, f"Validation error: {str(e)}"
```

### 4. Modify LLM Request to Use JSON Mode

Update the `_async_get_response` method in `processor.py` to support JSON mode:

```python
async def _async_get_response(
    self,
    message: str,
    temperature: float,
    max_tokens: int,
    model: str,
    returns_list: bool = False,
    output_mode: str = "TEXT",
    schema: dict = None,
    verbose: bool = False,
) -> Union[str, List[str], dict]:
    """
    Get a response from the LLM model asynchronously.
    
    Args:
        message: The prompt message
        temperature: Temperature setting
        max_tokens: Maximum tokens
        model: Model name
        returns_list: Whether to return a list
        output_mode: Output mode (TEXT, JSON, MARKDOWN)
        schema: JSON schema for validation
        verbose: Verbose logging
        
    Returns:
        Response from the LLM
    """
    async with self.throttler:
        start = time.time()
        try:
            messages = [
                {
                    "role": "system",
                    "content": "You are a helpful assistant.",
                },
                {"role": "user", "content": message},
            ]
            
            # Add response format for JSON mode
            kwargs = {
                "model": model,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": max_tokens,
            }
            
            # Add response format for JSON mode if supported by the model
            if output_mode == "JSON" and schema:
                # Different providers have different ways to request JSON
                if "gpt" in model.lower():
                    # OpenAI models
                    kwargs["response_format"] = {"type": "json_object"}
                    
                    # Add schema to system message if provided
                    schema_instruction = f"You must respond with a JSON object that conforms to the following schema:\n{json.dumps(schema, indent=2)}"
                    messages[0]["content"] += f"\n\n{schema_instruction}"
                elif "claude" in model.lower():
                    # Anthropic models
                    schema_instruction = f"You must respond with a JSON object that conforms to the following schema:\n{json.dumps(schema, indent=2)}\nDo not include any explanatory text, only the JSON object."
                    messages[0]["content"] += f"\n\n{schema_instruction}"
            
            resp = await litellm.acompletion(**kwargs)
            text = resp["choices"][0]["message"]["content"]
            
            # Handle JSON parsing if in JSON mode
            if output_mode == "JSON":
                try:
                    result = json.loads(text)
                    
                    # Validate against schema if provided
                    if schema:
                        is_valid, error = validate_against_schema(result, schema)
                        if not is_valid:
                            logger.warning(f"{Fore.YELLOW}Schema validation failed: {error}{Style.RESET_ALL}")
                    
                    return result
                except json.JSONDecodeError as e:
                    logger.error(f"{Fore.RED}JSON parsing error: {str(e)}{Style.RESET_ALL}")
                    logger.error(f"{Fore.RED}Raw response: {text}{Style.RESET_ALL}")
                    # Fall back to returning the raw text
                    return text
            
            # Handle list parsing if requested
            if returns_list:
                text_list = await self._fanout_list(text)
                logger.info(
                    f"Prompt: ['{text_list[0]}',...,'{text_list[-1]}'] len={len(text_list)} ✅ in {time.time() - start:.0f}s using {model}"
                )
                return text_list
            else:
                logger.info(
                    f"Prompt: '{text[:10]}...{text[-10:]}' ✅ in {time.time() - start:.0f}s using {model}"
                )
                return text
                
        except Exception as e:
            error_type = type(e).__name__
            error_message = str(e)
            logger.error(
                f"Failed API call model={model}: {error_type} - {error_message}"
            )
            if verbose:
                logger.error(
                    f"Full traceback: {traceback.format_exc()}"
                )
            raise
```

### 5. Update ChainStep to Include Schema and Output Mode

Modify the `ChainStep` class in `chain_step.py`:

```python
class ChainStep(NamedTuple):
    """
    Represents a single step in an LLM processing chain.
    """
    pt: str  # prompt template
    mapping: Optional[Dict[str, str]] = None  # prompt keyword mapping
    temperature: Optional[float] = None  # model temperature
    max_tokens: Optional[int] = None  # model max_tokens
    model: Optional[str] = None  # model name
    col: str = "response"  # response column name
    fanout: bool = False  # fanout response column as a list
    unfan_col: Optional[str] = None  # if fanout, specify the column to un-fan
    overwrite: bool = False  # overwrite existing response column
    validators: List = None  # LLM validators to apply to the response
    output_mode: str = "TEXT"  # Output mode (TEXT, JSON, MARKDOWN)
    schema: Optional[Dict] = None  # JSON schema for validation
```

### 6. Update Main Function to Use Schema and Output Mode

```python
def main():
    # ... existing code ...
    
    # Parse schema configuration
    schemas = parse_schema_config(df, prompt_output_cols)
    
    # Parse output mode configuration
    output_modes = parse_output_mode_config(df, prompt_output_cols)
    
    # ... existing code ...
    
    # Process columns in sequence to handle dependencies between columns
    for idx, (
        prompt_template,
        model,
        temperature,
        max_tokens,
        flags,
        output_col,
    ) in enumerate(
        zip(
            prompt_templates,
            models,
            temperatures,
            max_output_tokens,
            flags,
            prompt_output_cols,
        )
    ):
        # Get schema and output mode for this column
        schema = schemas.get(output_col)
        output_mode = output_modes.get(output_col, "TEXT")
        
        # ... existing code ...
        
        # Process data
        llm_proc = ParallelLLMDataFrameProcessor(
            def_model=model,
            def_temperature=temperature,
            def_max_tokens=max_tokens,
            def_async_rate_limit=10,
            def_thread_rate_limit=5,
        )
        
        # Set the template column as the source for the prompt
        chain = [
            ChainStep(
                pt=template_col,  # Use the template column as source
                model=model,
                temperature=temperature,
                max_tokens=max_tokens,
                col=output_col,
                output_mode=output_mode,
                schema=schema,
            ),
        ]
        
        # ... continue with existing code ...
```

## Architecture Diagram

```mermaid
flowchart TD
    A[Start Script] --> B[Load Sheet Data]
    B --> C[Extract Column References]
    C --> D[Parse Configuration]
    
    D --> D1[Parse Schemas]
    D --> D2[Parse Output Modes]
    D --> D3[Parse Other Config]
    
    D1 --> E[Process Columns]
    D2 --> E
    D3 --> E
    
    E --> F{For Each Column}
    F --> G[Create ChainStep with Schema & Mode]
    G --> H[Execute LLM Chain]
    H --> I{Output Mode?}
    
    I -->|JSON| J1[Parse & Validate JSON]
    I -->|TEXT| J2[Return Raw Text]
    I -->|MARKDOWN| J3[Return Markdown]
    
    J1 --> K[Store Results]
    J2 --> K
    J3 --> K
    
    K --> L[Update Sheet]
    
    subgraph Schema Processing
        D1
        G
        J1
    end
```

## Context and Considerations

1. **Model Compatibility**: Different LLM providers have different ways of requesting structured outputs. The implementation should handle these differences.
2. **Schema Validation**: Validating responses against schemas helps ensure data quality but adds complexity. The implementation should handle validation failures gracefully.
3. **Error Recovery**: When structured output parsing fails, the system should have fallback mechanisms to avoid losing the response entirely.
4. **User Experience**: The schema configuration should be user-friendly, with clear documentation and examples.
5. **Performance**: Schema validation adds overhead, so it should be optional and configurable.
6. **Integration with Existing Code**: The implementation should work seamlessly with the existing template and dependency handling.
7. **Future Enhancements**: 
   - Support for more output formats (XML, YAML, etc.)
   - Schema inference from example outputs
   - Schema visualization in the sheet
   - Custom validation functions