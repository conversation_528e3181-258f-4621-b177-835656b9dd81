# Feature: Model Name Validation

Original requirement:
> model name validation in-sheet [AF todo]

## Implementation Status: Not Implemented

The current implementation in `main2.py` and related files does not include validation for model names specified in the sheet. The code simply uses the model names as provided without checking if they are valid or supported.

## Feature Description

This feature would add validation for model names specified in the Google Sheet. It would check if the model names are valid and supported by the LLM providers being used (via LiteLLM). This helps prevent runtime errors due to invalid model names and provides immediate feedback to users about supported models.

Key aspects of this feature include:
1. Validating model names against a list of supported models
2. Providing feedback on invalid model names
3. Suggesting alternatives for invalid models
4. Optionally falling back to default models when invalid names are provided

## Implementation Recommendation

### 1. Define Supported Models

Create a module or configuration file that defines supported models:

```python
# models_config.py
from enum import Enum

class ModelProvider(Enum):
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"
    MISTRAL = "mistral"
    COHERE = "cohere"
    # Add more providers as needed

class SupportedModels:
    """Configuration for supported LLM models."""
    
    # Map of provider to list of supported models
    MODELS_BY_PROVIDER = {
        ModelProvider.OPENAI: [
            "gpt-4o",
            "gpt-4-turbo",
            "gpt-4",
            "gpt-3.5-turbo",
            # Add more models as needed
        ],
        ModelProvider.ANTHROPIC: [
            "claude-3-opus-20240229",
            "claude-3-sonnet-20240229",
            "claude-3-haiku-20240307",
            # Add more models as needed
        ],
        ModelProvider.GOOGLE: [
            "gemini-pro",
            "gemini-ultra",
            # Add more models as needed
        ],
        # Add more providers as needed
    }
    
    # Flat list of all supported models
    ALL_MODELS = [
        model
        for provider_models in MODELS_BY_PROVIDER.values()
        for model in provider_models
    ]
    
    # Default models by capability
    DEFAULT_MODELS = {
        "default": "gpt-3.5-turbo",
        "high_quality": "gpt-4o",
        "fast": "gpt-3.5-turbo",
        "cheap": "gpt-3.5-turbo",
    }
    
    # Model aliases (alternative names that map to actual model names)
    MODEL_ALIASES = {
        "gpt4": "gpt-4",
        "gpt4o": "gpt-4o",
        "gpt3": "gpt-3.5-turbo",
        "claude": "claude-3-sonnet-20240229",
        # Add more aliases as needed
    }
    
    @classmethod
    def is_valid_model(cls, model_name):
        """Check if a model name is valid."""
        if not model_name or not isinstance(model_name, str):
            return False
        
        # Normalize model name
        model_name = model_name.strip().lower()
        
        # Check if it's in the list of supported models
        if model_name in [m.lower() for m in cls.ALL_MODELS]:
            return True
        
        # Check if it's an alias
        if model_name in cls.MODEL_ALIASES:
            return True
        
        # Check if it's a provider-prefixed model (litellm format)
        for provider in ModelProvider:
            if model_name.startswith(f"{provider.value}/"):
                provider_model = model_name.split("/", 1)[1]
                if provider_model in [m.lower() for m in cls.MODELS_BY_PROVIDER.get(provider, [])]:
                    return True
        
        return False
    
    @classmethod
    def get_normalized_model_name(cls, model_name):
        """Get the normalized model name, resolving aliases."""
        if not model_name or not isinstance(model_name, str):
            return cls.DEFAULT_MODELS["default"]
        
        # Normalize model name
        model_name = model_name.strip().lower()
        
        # Check if it's an alias
        if model_name in cls.MODEL_ALIASES:
            return cls.MODEL_ALIASES[model_name]
        
        # Check if it's in the list of supported models
        for provider_models in cls.MODELS_BY_PROVIDER.values():
            for model in provider_models:
                if model_name == model.lower():
                    return model
        
        # Check if it's a provider-prefixed model (litellm format)
        for provider in ModelProvider:
            if model_name.startswith(f"{provider.value}/"):
                provider_model = model_name.split("/", 1)[1]
                for model in cls.MODELS_BY_PROVIDER.get(provider, []):
                    if provider_model == model.lower():
                        return f"{provider.value}/{model}"
        
        # If not found, return the default model
        return cls.DEFAULT_MODELS["default"]
    
    @classmethod
    def suggest_alternatives(cls, model_name):
        """Suggest alternative models for an invalid model name."""
        if not model_name or not isinstance(model_name, str):
            return []
        
        # Normalize model name
        model_name = model_name.strip().lower()
        
        # Simple string similarity function
        def similarity(a, b):
            a = a.lower()
            b = b.lower()
            return sum(1 for x, y in zip(a, b) if x == y) / max(len(a), len(b))
        
        # Calculate similarity with all models
        similarities = [
            (model, similarity(model_name, model.lower()))
            for model in cls.ALL_MODELS + list(cls.MODEL_ALIASES.keys())
        ]
        
        # Sort by similarity and return top 3
        return [model for model, sim in sorted(similarities, key=lambda x: x[1], reverse=True)[:3]]
```

### 2. Add Validation Function for Model Names

```python
def validate_model_names(models):
    """
    Validate a list of model names.
    
    Args:
        models: List of model names to validate
        
    Returns:
        Tuple of (valid_models, validation_messages)
    """
    from models_config import SupportedModels
    
    valid_models = []
    validation_messages = []
    
    for i, model in enumerate(models):
        if not model or not isinstance(model, str) or model.strip() == "":
            # Empty model name, use default
            default_model = SupportedModels.DEFAULT_MODELS["default"]
            valid_models.append(default_model)
            validation_messages.append(
                f"Model {i+1}: Empty model name. Using default: {default_model}"
            )
            continue
        
        # Normalize model name
        model_name = model.strip()
        
        if SupportedModels.is_valid_model(model_name):
            # Valid model, normalize it
            normalized_name = SupportedModels.get_normalized_model_name(model_name)
            valid_models.append(normalized_name)
            
            if normalized_name != model_name:
                validation_messages.append(
                    f"Model {i+1}: '{model_name}' normalized to '{normalized_name}'"
                )
        else:
            # Invalid model, suggest alternatives
            alternatives = SupportedModels.suggest_alternatives(model_name)
            default_model = SupportedModels.DEFAULT_MODELS["default"]
            valid_models.append(default_model)
            
            if alternatives:
                alternatives_str = ", ".join(f"'{alt}'" for alt in alternatives)
                validation_messages.append(
                    f"Model {i+1}: Invalid model '{model_name}'. Using default: '{default_model}'. Did you mean one of these? {alternatives_str}"
                )
            else:
                validation_messages.append(
                    f"Model {i+1}: Invalid model '{model_name}'. Using default: '{default_model}'"
                )
    
    return valid_models, validation_messages
```

### 3. Integrate Validation in Main Function

```python
def main():
    # ... existing code ...
    
    # Extract models from the sheet
    models = list(df.iloc[[4]][prompt_output_cols].to_dict("records")[0].values())
    
    # Validate model names
    valid_models, validation_messages = validate_model_names(models)
    
    # Log validation messages
    for message in validation_messages:
        if "Invalid model" in message:
            logger.warning(f"{Fore.YELLOW}{message}{Style.RESET_ALL}")
        else:
            logger.info(f"{Fore.CYAN}{message}{Style.RESET_ALL}")
    
    # Use validated models
    models = valid_models
    
    # ... continue with existing code ...
```

### 4. Add In-Sheet Validation Feedback

To provide feedback directly in the sheet, we can add a validation row:

```python
def add_model_validation_to_sheet(sheet_id, sheet_name, validation_messages, model_row_index=4):
    """
    Add model validation feedback to the sheet.
    
    Args:
        sheet_id: ID of the Google Sheet
        sheet_name: Name of the sheet
        validation_messages: List of validation messages
        model_row_index: Index of the model row in the sheet
    """
    try:
        # Create a validation row
        validation_row_index = model_row_index + 1
        
        # Prepare update data
        update_data = [
            {
                "range": f"{sheet_name}!A{validation_row_index}",
                "values": [["MODEL_VALIDATION"]]
            }
        ]
        
        # Add validation messages for each model
        for i, message in enumerate(validation_messages):
            # Calculate column letter (A, B, C, etc.)
            col_letter = chr(65 + i + 1)  # +1 to skip column A
            
            update_data.append({
                "range": f"{sheet_name}!{col_letter}{validation_row_index}",
                "values": [[message]]
            })
        
        # Execute update
        body = {"valueInputOption": "RAW", "data": update_data}
        service.spreadsheets().values().batchUpdate(spreadsheetId=sheet_id, body=body).execute()
        
        logger.info(f"{Fore.GREEN}Added model validation feedback to sheet{Style.RESET_ALL}")
        
    except Exception as e:
        logger.error(f"{Fore.RED}Error adding validation feedback to sheet: {str(e)}{Style.RESET_ALL}")
```

### 5. Add Model Validation to the Sheet Format

Update the main function to add validation feedback to the sheet:

```python
def main():
    # ... existing code ...
    
    # Extract models from the sheet
    models = list(df.iloc[[4]][prompt_output_cols].to_dict("records")[0].values())
    
    # Validate model names
    valid_models, validation_messages = validate_model_names(models)
    
    # Log validation messages
    for message in validation_messages:
        if "Invalid model" in message:
            logger.warning(f"{Fore.YELLOW}{message}{Style.RESET_ALL}")
        else:
            logger.info(f"{Fore.CYAN}{message}{Style.RESET_ALL}")
    
    # Add validation feedback to the sheet
    add_model_validation_to_sheet(sheet_id, sheet_name, validation_messages)
    
    # Use validated models
    models = valid_models
    
    # ... continue with existing code ...
```

## Architecture Diagram

```mermaid
flowchart TD
    A[Start Script] --> B[Load Sheet Data]
    B --> C[Extract Model Names]
    C --> D[Validate Model Names]
    
    D --> E1[Check Against Supported Models]
    D --> E2[Resolve Aliases]
    D --> E3[Suggest Alternatives]
    
    E1 --> F[Log Validation Messages]
    E2 --> F
    E3 --> F
    
    F --> G[Add Validation to Sheet]
    F --> H[Use Validated Models]
    
    H --> I[Process Data]
    I --> J[Update Sheet]
    
    subgraph Model Validation
        D
        E1
        E2
        E3
    end
```

## Context and Considerations

1. **Model Evolution**: LLM models evolve rapidly, so the list of supported models should be easy to update.
2. **Provider-Specific Formats**: Different LLM providers have different naming conventions. The validation should handle these differences.
3. **Aliases and Normalization**: Users might use shorthand or alternative names for models. The validation should handle common aliases.
4. **Fallback Strategy**: When an invalid model is specified, the system should have a clear fallback strategy (e.g., use a default model).
5. **User Feedback**: Clear feedback on model validation helps users correct issues quickly.
6. **Performance**: Model validation should be efficient and not add significant overhead to the processing.
7. **Integration with LiteLLM**: The validation should align with the models supported by LiteLLM.
8. **Future Enhancements**: 
   - Dynamic model list fetched from providers' APIs
   - Model capability matching (suggest models with similar capabilities)
   - Cost estimation based on model selection
   - Model performance metrics