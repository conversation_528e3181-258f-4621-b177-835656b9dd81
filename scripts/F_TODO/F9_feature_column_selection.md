# Feature: Column Selection for Processing

Original requirement:
> * How to know which LLM_func to run for which col?
>   Option1: Define ChainSteps -> allows arb pythong
>   Option2: singl col name each time
>   "✨Option3: List of col names
>
> ```python
> cols_to_run = [
>     # name,
>     # profession,
>     # age,
>     'new_career',
>     'issues',
> ]
> ```
>
>>> This new_career then issues.
>
> This way we can just comment / uncomment.
> e.g.,:
>
> ```python
> cols_to_run = [
>     #'name',
>     #'profession', 
>     #'age',
>     #'new_career',
>     'issues',
> ]
> ```
>
>>> // this will only run issues col

## Implementation Status: Not Implemented

The current implementation in `main2.py` processes all output columns that have the `<generate>` tag, without providing a way to selectively process specific columns. It doesn't implement the suggested approach of using a configurable list of columns to run.

## Feature Description

This feature would allow users to specify which output columns should be processed, rather than processing all columns with the `<generate>` tag. This provides more control over the execution flow and makes it easier to test and debug specific columns.

The preferred implementation (Option 3) would use a list of column names to specify which columns to process and in what order. This approach is flexible and user-friendly, allowing users to comment/uncomment columns as needed.

## Implementation Recommendation

### 1. Add Column Selection Configuration

Add a configuration option to specify which columns to process:

```python
# Configuration for column selection
# Comment/uncomment columns to control which ones are processed
COLS_TO_RUN = [
    # "name",
    # "profession",
    # "age",
    "new_career",
    "issues",
]

# Set to True to process all columns with <generate> tag
# Set to False to only process columns in COLS_TO_RUN
PROCESS_ALL_COLUMNS = False
```

### 2. Add Command Line Arguments for Column Selection

```python
import argparse

def parse_arguments():
    parser = argparse.ArgumentParser(description='Process Google Sheet data with LLM.')
    parser.add_argument('--sheet-name', type=str, default='Sheet1',
                        help='Name of the sheet to process')
    parser.add_argument('--process-all', action='store_true',
                        help='Process all columns with <generate> tag')
    parser.add_argument('--columns', type=str, nargs='+',
                        help='List of columns to process (overrides COLS_TO_RUN)')
    return parser.parse_args()

def main():
    args = parse_arguments()
  
    # Determine which columns to process
    process_all_columns = args.process_all or PROCESS_ALL_COLUMNS
  
    if args.columns:
        # Use columns specified in command line
        cols_to_run = args.columns
    else:
        # Use columns from configuration
        cols_to_run = COLS_TO_RUN
  
    # ... rest of the code ...
```

### 3. Modify Main Processing Loop to Use Column Selection

```python
def main():
    # ... existing code ...
  
    # Determine which columns to process
    process_all_columns = args.process_all or PROCESS_ALL_COLUMNS
  
    if args.columns:
        # Use columns specified in command line
        cols_to_run = args.columns
    else:
        # Use columns from configuration
        cols_to_run = COLS_TO_RUN
  
    # Filter output columns based on configuration
    if not process_all_columns:
        # Filter to only include columns in cols_to_run
        filtered_output_cols = [col for col in prompt_output_cols if col in cols_to_run]
      
        # Check if any specified columns are missing
        missing_cols = [col for col in cols_to_run if col not in prompt_output_cols]
        if missing_cols:
            logger.warning(
                f"{Fore.YELLOW}Some specified columns are not found in output columns: {missing_cols}{Style.RESET_ALL}"
            )
      
        # Sort filtered columns based on the order in cols_to_run
        filtered_output_cols.sort(key=lambda col: cols_to_run.index(col) if col in cols_to_run else float('inf'))
      
        # Update prompt_output_cols to use filtered list
        prompt_output_cols = filtered_output_cols
  
    logger.info(
        f"{Fore.GREEN}Processing columns: {prompt_output_cols}{Style.RESET_ALL}"
    )
  
    # ... continue with existing code ...
```

### 4. Add Support for Nested Lists for Parallel Processing

To support the advanced feature of parallel processing mentioned in the requirements, we can extend the column selection to support nested lists:

```python
# Configuration for column selection with parallel processing groups
# Columns in the same sublist will be processed in parallel
COLS_TO_RUN = [
    # "name",
    # "profession",
    # "age",
    ["new_career", "new_career2", "new_career3"],  # These will be processed in parallel
    "issues",
]
```

And modify the processing logic:

```python
def flatten_cols_to_run(cols_to_run):
    """
    Flatten the nested cols_to_run list, preserving order.
  
    Args:
        cols_to_run: List of column names, possibly with nested lists
      
    Returns:
        Flattened list of column names
    """
    flattened = []
    for item in cols_to_run:
        if isinstance(item, list):
            flattened.extend(item)
        else:
            flattened.append(item)
    return flattened

def get_parallel_groups(cols_to_run):
    """
    Get groups of columns that can be processed in parallel.
  
    Args:
        cols_to_run: List of column names, possibly with nested lists
      
    Returns:
        List of column groups (each group is a list of column names)
    """
    groups = []
    for item in cols_to_run:
        if isinstance(item, list):
            groups.append(item)
        else:
            groups.append([item])
    return groups

def main():
    # ... existing code ...
  
    # Determine which columns to process
    process_all_columns = args.process_all or PROCESS_ALL_COLUMNS
  
    if args.columns:
        # Use columns specified in command line
        cols_to_run = args.columns
    else:
        # Use columns from configuration
        cols_to_run = COLS_TO_RUN
  
    # Get flattened list of columns to run
    flattened_cols = flatten_cols_to_run(cols_to_run)
  
    # Filter output columns based on configuration
    if not process_all_columns:
        # Filter to only include columns in flattened_cols
        filtered_output_cols = [col for col in prompt_output_cols if col in flattened_cols]
      
        # Check if any specified columns are missing
        missing_cols = [col for col in flattened_cols if col not in prompt_output_cols]
        if missing_cols:
            logger.warning(
                f"{Fore.YELLOW}Some specified columns are not found in output columns: {missing_cols}{Style.RESET_ALL}"
            )
      
        # Sort filtered columns based on the order in flattened_cols
        filtered_output_cols.sort(key=lambda col: flattened_cols.index(col) if col in flattened_cols else float('inf'))
      
        # Update prompt_output_cols to use filtered list
        prompt_output_cols = filtered_output_cols
  
    # Get parallel processing groups
    parallel_groups = get_parallel_groups(cols_to_run)
  
    logger.info(
        f"{Fore.GREEN}Processing columns: {prompt_output_cols}{Style.RESET_ALL}"
    )
  
    if len(parallel_groups) > 1:
        logger.info(
            f"{Fore.GREEN}Using parallel processing groups: {parallel_groups}{Style.RESET_ALL}"
        )
  
    # Process columns in groups (sequential between groups, parallel within groups)
    for group in parallel_groups:
        # Filter group to only include valid output columns
        valid_group = [col for col in group if col in prompt_output_cols]
      
        if not valid_group:
            continue
      
        if len(valid_group) == 1:
            # Process single column as before
            output_col = valid_group[0]
            # ... existing processing code for a single column ...
        else:
            # Process multiple columns in parallel
            # ... parallel processing code ...
            pass
  
    # ... continue with existing code ...
```

### 5. Add Configuration File Support

For more flexibility, we can add support for loading column configuration from a file:

```python
import json
import os

def load_column_config(config_file):
    """
    Load column configuration from a JSON file.
  
    Args:
        config_file: Path to the configuration file
      
    Returns:
        Dictionary with configuration
    """
    if not os.path.exists(config_file):
        logger.warning(
            f"{Fore.YELLOW}Configuration file not found: {config_file}. Using defaults.{Style.RESET_ALL}"
        )
        return {"process_all_columns": PROCESS_ALL_COLUMNS, "cols_to_run": COLS_TO_RUN}
  
    try:
        with open(config_file, 'r') as f:
            config = json.load(f)
      
        logger.info(
            f"{Fore.GREEN}Loaded configuration from {config_file}{Style.RESET_ALL}"
        )
        return config
    except Exception as e:
        logger.error(
            f"{Fore.RED}Error loading configuration file: {str(e)}. Using defaults.{Style.RESET_ALL}"
        )
        return {"process_all_columns": PROCESS_ALL_COLUMNS, "cols_to_run": COLS_TO_RUN}

def parse_arguments():
    parser = argparse.ArgumentParser(description='Process Google Sheet data with LLM.')
    parser.add_argument('--sheet-name', type=str, default='Sheet1',
                        help='Name of the sheet to process')
    parser.add_argument('--process-all', action='store_true',
                        help='Process all columns with <generate> tag')
    parser.add_argument('--columns', type=str, nargs='+',
                        help='List of columns to process (overrides COLS_TO_RUN)')
    parser.add_argument('--config-file', type=str,
                        help='Path to configuration file')
    return parser.parse_args()

def main():
    args = parse_arguments()
  
    # Load configuration
    if args.config_file:
        config = load_column_config(args.config_file)
        process_all_columns = config.get("process_all_columns", PROCESS_ALL_COLUMNS)
        cols_to_run = config.get("cols_to_run", COLS_TO_RUN)
    else:
        process_all_columns = PROCESS_ALL_COLUMNS
        cols_to_run = COLS_TO_RUN
  
    # Command line arguments override configuration
    if args.process_all:
        process_all_columns = True
  
    if args.columns:
        cols_to_run = args.columns
  
    # ... rest of the code ...
```

## Architecture Diagram

```mermaid
flowchart TD
    A[Start Script] --> B[Parse Arguments]
    B --> C[Load Configuration]
    C --> D[Determine Columns to Process]
  
    D --> E{Process All Columns?}
    E -->|Yes| F[Use All Output Columns]
    E -->|No| G[Filter and Sort Columns]
  
    F --> H[Get Parallel Groups]
    G --> H
  
    H --> I{For Each Group}
    I --> J{Single Column?}
    J -->|Yes| K[Process Column Sequentially]
    J -->|No| L[Process Columns in Parallel]
  
    K --> I
    L --> I
  
    I --> M[Update Sheet]
```

## Context and Considerations

1. **Flexibility**: The implementation should support both processing all columns and selectively processing specific columns.
2. **Order Preservation**: When processing selected columns, the order specified in the configuration should be preserved.
3. **Parallel Processing**: The implementation should support processing groups of columns in parallel, as suggested in the requirements.
4. **Command Line Interface**: Command line arguments provide a convenient way to override configuration without changing the code.
5. **Configuration File**: A configuration file allows for more complex configurations and makes it easier to switch between different configurations.
6. **Error Handling**: The implementation should handle missing or invalid column names gracefully.
7. **Documentation**: Clear documentation on how to use the column selection feature would help users take advantage of it.
8. **Future Enhancements**:
   - Support for more complex dependency graphs
   - Integration with a workflow engine
   - Support for conditional processing based on column values
   - Support for column aliases or groups
