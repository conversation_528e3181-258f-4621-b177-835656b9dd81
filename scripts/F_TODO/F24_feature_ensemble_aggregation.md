# Feature: Ensemble Aggregation Techniques

Original requirement:
> * Ensemble aggregation techniques (best-of-n, multi-party-vote, etc.)

## Implementation Status: Not Implemented

The current implementation in `main2.py` and related files does not include ensemble aggregation techniques. The code processes each prompt with a single LLM model and doesn't have mechanisms for combining multiple responses or selecting the best response from multiple attempts.

## Feature Description

This feature would add ensemble aggregation techniques to improve the quality and reliability of LLM responses. Ensemble methods involve generating multiple responses for the same prompt (using different models, parameters, or random seeds) and then combining or selecting from these responses using various strategies.

Key aggregation techniques include:

1. **Best-of-N**: Generate N responses and select the "best" one based on a scoring function
2. **Multi-Party-Vote**: Generate multiple responses and use voting to determine the final answer
3. **Disagreement Analysis**: Analyze where different models or attempts disagree to identify uncertain areas
4. **Agreement Analysis**: Focus on areas where different models or attempts agree to increase confidence

These techniques can significantly improve the quality, reliability, and robustness of LLM outputs.

## Implementation Recommendation

### 1. Define Ensemble Configuration Classes

```python
from enum import Enum
from typing import List, Dict, Any, Optional, Callable, Union
from dataclasses import dataclass

class EnsembleMethod(Enum):
    """Methods for ensemble aggregation."""
    BEST_OF_N = "best_of_n"
    MULTI_PARTY_VOTE = "multi_party_vote"
    AGREEMENT_ANALYSIS = "agreement_analysis"
    DISAGREEMENT_ANALYSIS = "disagreement_analysis"

@dataclass
class EnsembleConfig:
    """Configuration for ensemble aggregation."""
    method: EnsembleMethod
    num_samples: int = 3
    models: Optional[List[str]] = None
    temperatures: Optional[List[float]] = None
    scoring_function: Optional[str] = None
    custom_aggregator: Optional[Callable] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "method": self.method.value,
            "num_samples": self.num_samples,
            "models": self.models,
            "temperatures": self.temperatures,
            "scoring_function": self.scoring_function,
        }
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'EnsembleConfig':
        """Create from dictionary."""
        method = EnsembleMethod(config_dict.get("method", "best_of_n"))
        return cls(
            method=method,
            num_samples=config_dict.get("num_samples", 3),
            models=config_dict.get("models"),
            temperatures=config_dict.get("temperatures"),
            scoring_function=config_dict.get("scoring_function"),
        )
```

### 2. Implement Ensemble Aggregators

```python
class EnsembleAggregator:
    """Base class for ensemble aggregators."""
    
    def aggregate(self, responses: List[str]) -> str:
        """
        Aggregate multiple responses into a single response.
        
        Args:
            responses: List of responses to aggregate
            
        Returns:
            Aggregated response
        """
        raise NotImplementedError("Subclasses must implement aggregate()")

class BestOfNAggregator(EnsembleAggregator):
    """Aggregator that selects the best response from N responses."""
    
    def __init__(self, scoring_function: Optional[Callable[[str], float]] = None):
        """
        Initialize the aggregator.
        
        Args:
            scoring_function: Function that scores a response (higher is better)
        """
        self.scoring_function = scoring_function or self._default_scoring_function
    
    def _default_scoring_function(self, response: str) -> float:
        """
        Default scoring function based on response length and complexity.
        
        Args:
            response: Response to score
            
        Returns:
            Score (higher is better)
        """
        # Simple heuristic: longer responses are usually better
        length_score = min(len(response) / 500, 1.0)  # Cap at 1.0
        
        # Complexity heuristic: more unique words is better
        words = response.lower().split()
        unique_words = set(words)
        complexity_score = min(len(unique_words) / 100, 1.0)  # Cap at 1.0
        
        # Combine scores
        return 0.5 * length_score + 0.5 * complexity_score
    
    def aggregate(self, responses: List[str]) -> str:
        """
        Select the best response based on the scoring function.
        
        Args:
            responses: List of responses to choose from
            
        Returns:
            Best response
        """
        if not responses:
            return ""
        
        if len(responses) == 1:
            return responses[0]
        
        # Score each response
        scores = [self.scoring_function(response) for response in responses]
        
        # Select the response with the highest score
        best_index = scores.index(max(scores))
        return responses[best_index]

class MultiPartyVoteAggregator(EnsembleAggregator):
    """Aggregator that uses voting to determine the final answer."""
    
    def __init__(self, voting_method: str = "majority"):
        """
        Initialize the aggregator.
        
        Args:
            voting_method: Method for voting ("majority" or "weighted")
        """
        self.voting_method = voting_method
    
    def _extract_key_points(self, response: str) -> List[str]:
        """
        Extract key points from a response.
        
        Args:
            response: Response to extract from
            
        Returns:
            List of key points
        """
        # Simple implementation: split by sentences and clean up
        import re
        sentences = re.split(r'[.!?]', response)
        key_points = [s.strip() for s in sentences if s.strip()]
        return key_points
    
    def _find_common_points(self, all_points: List[List[str]]) -> Dict[str, int]:
        """
        Find common points across multiple responses.
        
        Args:
            all_points: List of lists of key points
            
        Returns:
            Dictionary mapping points to vote counts
        """
        from collections import Counter
        
        # Flatten and count
        all_flat = [point for points in all_points for point in points]
        
        # Use fuzzy matching to group similar points
        grouped_points = {}
        for point in all_flat:
            matched = False
            for group in grouped_points:
                # Simple similarity check: 80% of words in common
                point_words = set(point.lower().split())
                group_words = set(group.lower().split())
                if len(point_words) == 0 or len(group_words) == 0:
                    continue
                
                common_words = point_words.intersection(group_words)
                similarity = len(common_words) / max(len(point_words), len(group_words))
                
                if similarity > 0.8:
                    grouped_points[group] += 1
                    matched = True
                    break
            
            if not matched:
                grouped_points[point] = 1
        
        return grouped_points
    
    def aggregate(self, responses: List[str]) -> str:
        """
        Aggregate responses using voting.
        
        Args:
            responses: List of responses to aggregate
            
        Returns:
            Aggregated response
        """
        if not responses:
            return ""
        
        if len(responses) == 1:
            return responses[0]
        
        # Extract key points from each response
        all_points = [self._extract_key_points(response) for response in responses]
        
        # Find common points and their vote counts
        point_votes = self._find_common_points(all_points)
        
        # Sort by vote count (descending)
        sorted_points = sorted(point_votes.items(), key=lambda x: x[1], reverse=True)
        
        # Take points with majority votes (more than half of responses)
        majority_threshold = len(responses) / 2
        majority_points = [point for point, votes in sorted_points if votes > majority_threshold]
        
        # If no points have majority, take the top 3
        if not majority_points and sorted_points:
            majority_points = [point for point, _ in sorted_points[:3]]
        
        # Combine into a coherent response
        if majority_points:
            return " ".join(majority_points)
        else:
            # Fallback to the first response
            return responses[0]

class AgreementAnalysisAggregator(EnsembleAggregator):
    """Aggregator that focuses on areas where responses agree."""
    
    def _find_agreement(self, responses: List[str]) -> Dict[str, float]:
        """
        Find areas of agreement between responses.
        
        Args:
            responses: List of responses to analyze
            
        Returns:
            Dictionary mapping statements to agreement scores
        """
        # Extract statements from each response
        import re
        all_statements = []
        
        for response in responses:
            statements = re.split(r'[.!?]', response)
            statements = [s.strip() for s in statements if s.strip()]
            all_statements.append(statements)
        
        # Find agreement between statements
        agreement_scores = {}
        
        for i, statements1 in enumerate(all_statements):
            for statement1 in statements1:
                if statement1 not in agreement_scores:
                    agreement_scores[statement1] = 0.0
                
                # Compare with statements from other responses
                for j, statements2 in enumerate(all_statements):
                    if i == j:
                        continue
                    
                    # Find the most similar statement
                    max_similarity = 0.0
                    for statement2 in statements2:
                        similarity = self._calculate_similarity(statement1, statement2)
                        max_similarity = max(max_similarity, similarity)
                    
                    # Add to agreement score
                    agreement_scores[statement1] += max_similarity
        
        # Normalize scores
        num_comparisons = len(responses) - 1
        if num_comparisons > 0:
            for statement in agreement_scores:
                agreement_scores[statement] /= num_comparisons
        
        return agreement_scores
    
    def _calculate_similarity(self, statement1: str, statement2: str) -> float:
        """
        Calculate similarity between two statements.
        
        Args:
            statement1: First statement
            statement2: Second statement
            
        Returns:
            Similarity score (0.0 to 1.0)
        """
        # Simple word overlap similarity
        words1 = set(statement1.lower().split())
        words2 = set(statement2.lower().split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union)
    
    def aggregate(self, responses: List[str]) -> str:
        """
        Aggregate responses by focusing on areas of agreement.
        
        Args:
            responses: List of responses to aggregate
            
        Returns:
            Aggregated response
        """
        if not responses:
            return ""
        
        if len(responses) == 1:
            return responses[0]
        
        # Find agreement between statements
        agreement_scores = self._find_agreement(responses)
        
        # Filter statements with high agreement
        high_agreement = [s for s, score in agreement_scores.items() if score > 0.7]
        
        # Sort by agreement score
        high_agreement.sort(key=lambda s: agreement_scores[s], reverse=True)
        
        # Combine into a coherent response
        if high_agreement:
            return " ".join(high_agreement)
        else:
            # Fallback to the first response
            return responses[0]

class DisagreementAnalysisAggregator(EnsembleAggregator):
    """Aggregator that analyzes where responses disagree."""
    
    def _find_disagreement(self, responses: List[str]) -> Dict[str, List[str]]:
        """
        Find areas of disagreement between responses.
        
        Args:
            responses: List of responses to analyze
            
        Returns:
            Dictionary mapping topics to different opinions
        """
        # This is a simplified implementation
        # A more sophisticated approach would use NLP techniques
        
        # Extract key topics and opinions
        topics = {}
        
        for response in responses:
            # Simple topic extraction: look for "topic: opinion" patterns
            import re
            matches = re.findall(r'([^.!?:]+):\s*([^.!?]+)', response)
            
            for topic, opinion in matches:
                topic = topic.strip().lower()
                opinion = opinion.strip()
                
                if topic not in topics:
                    topics[topic] = []
                
                # Check if this opinion is different from existing ones
                is_new = True
                for existing_opinion in topics[topic]:
                    similarity = self._calculate_similarity(opinion, existing_opinion)
                    if similarity > 0.7:  # High similarity threshold
                        is_new = False
                        break
                
                if is_new:
                    topics[topic].append(opinion)
        
        # Filter to topics with multiple opinions
        disagreements = {topic: opinions for topic, opinions in topics.items() if len(opinions) > 1}
        
        return disagreements
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """
        Calculate similarity between two texts.
        
        Args:
            text1: First text
            text2: Second text
            
        Returns:
            Similarity score (0.0 to 1.0)
        """
        # Simple word overlap similarity
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union)
    
    def aggregate(self, responses: List[str]) -> str:
        """
        Aggregate responses by analyzing disagreements.
        
        Args:
            responses: List of responses to aggregate
            
        Returns:
            Aggregated response with disagreement analysis
        """
        if not responses:
            return ""
        
        if len(responses) == 1:
            return responses[0]
        
        # Find disagreements
        disagreements = self._find_disagreement(responses)
        
        if not disagreements:
            # No clear disagreements found, use the first response
            return responses[0]
        
        # Create a response that highlights disagreements
        parts = ["Here's an analysis of different perspectives:"]
        
        for topic, opinions in disagreements.items():
            parts.append(f"\n\nOn the topic of {topic}:")
            for i, opinion in enumerate(opinions, 1):
                parts.append(f"\n- Perspective {i}: {opinion}")
        
        return "".join(parts)
```

### 3. Implement Ensemble Processor

```python
class EnsembleProcessor:
    """
    Processor for ensemble aggregation.
    """
    
    def __init__(self, llm_processor: ParallelLLMDataFrameProcessor):
        """
        Initialize the ensemble processor.
        
        Args:
            llm_processor: LLM processor to use
        """
        self.llm_processor = llm_processor
        
        # Register aggregators
        self.aggregators = {
            EnsembleMethod.BEST_OF_N: BestOfNAggregator(),
            EnsembleMethod.MULTI_PARTY_VOTE: MultiPartyVoteAggregator(),
            EnsembleMethod.AGREEMENT_ANALYSIS: AgreementAnalysisAggregator(),
            EnsembleMethod.DISAGREEMENT_ANALYSIS: DisagreementAnalysisAggregator(),
        }
    
    def get_aggregator(self, method: EnsembleMethod) -> EnsembleAggregator:
        """
        Get an aggregator for a method.
        
        Args:
            method: Ensemble method
            
        Returns:
            Aggregator for the method
        """
        return self.aggregators.get(method, self.aggregators[EnsembleMethod.BEST_OF_N])
    
    async def process_with_ensemble(
        self,
        prompt: str,
        config: EnsembleConfig,
        base_model: str = "gpt-4o",
        base_temperature: float = 0.7,
        base_max_tokens: int = 1000,
    ) -> str:
        """
        Process a prompt with ensemble aggregation.
        
        Args:
            prompt: Prompt to process
            config: Ensemble configuration
            base_model: Base model to use
            base_temperature: Base temperature
            base_max_tokens: Base max tokens
            
        Returns:
            Aggregated response
        """
        responses = []
        
        # Generate responses
        if config.models:
            # Use different models
            for model in config.models:
                response = await self.llm_processor._async_get_response(
                    message=prompt,
                    temperature=base_temperature,
                    max_tokens=base_max_tokens,
                    model=model,
                )
                responses.append(response)
        elif config.temperatures:
            # Use different temperatures
            for temperature in config.temperatures:
                response = await self.llm_processor._async_get_response(
                    message=prompt,
                    temperature=temperature,
                    max_tokens=base_max_tokens,
                    model=base_model,
                )
                responses.append(response)
        else:
            # Use same parameters multiple times
            for _ in range(config.num_samples):
                response = await self.llm_processor._async_get_response(
                    message=prompt,
                    temperature=base_temperature,
                    max_tokens=base_max_tokens,
                    model=base_model,
                )
                responses.append(response)
        
        # Get aggregator
        aggregator = self.get_aggregator(config.method)
        
        # Aggregate responses
        return aggregator.aggregate(responses)
```

### 4. Extend ChainStep to Support Ensemble Aggregation

```python
class ChainStep(NamedTuple):
    """
    Represents a single step in an LLM processing chain.
    """
    pt: str  # prompt template
    mapping: Optional[Dict[str, str]] = None  # prompt keyword mapping
    temperature: Optional[float] = None  # model temperature
    max_tokens: Optional[int] = None  # model max_tokens
    model: Optional[str] = None  # model name
    col: str = "response"  # response column name
    fanout: bool = False  # fanout response column as a list
    unfan_col: Optional[str] = None  # if fanout, specify the column to un-fan
    overwrite: bool = False  # overwrite existing response column
    validators: List = None  # LLM validators to apply to the response
    ensemble_config: Optional[EnsembleConfig] = None  # Ensemble configuration
```

### 5. Update Process Chain Step to Use Ensemble Aggregation

```python
async def _process_chain_step_with_ensemble(
    self,
    c_row: pd.Series,
    step: ChainStep,
    mapping: dict,
    max_attempts: int,
) -> None:
    """
    Process a single chain step for a given row with ensemble aggregation.
    
    Args:
        c_row (pd.Series): The current row of the DataFrame.
        step (ChainStep): The chain step to process.
        mapping (dict): The mapping of prompt keywords to column names.
        max_attempts (int): The maximum number of retry attempts for failed API calls.
    """
    prompt = c_row.get(step.pt)
    if c_row.get(step.col) and not step.overwrite:
        logger.debug(f"{step.col} already exists (skip)")
        return
    for k, v in mapping.items():
        prompt = prompt.replace(f"{{{k}}}", str(c_row.get(v, "N/A")))

    temperature = (
        c_row.get("__temperature")
        or step.temperature
        or self.def_temperature
    )
    max_tokens = (
        c_row.get("__max_tokens") or step.max_tokens or self.def_max_tokens
    )
    model = c_row.get("__model") or step.model or self.def_model

    try:
        if step.ensemble_config:
            # Use ensemble processing
            ensemble_processor = EnsembleProcessor(self)
            response = await ensemble_processor.process_with_ensemble(
                prompt=prompt,
                config=step.ensemble_config,
                base_model=model,
                base_temperature=temperature,
                base_max_tokens=max_tokens,
            )
        elif max_attempts > 1:
            # Use regular processing with retries
            foo = tenacity.retry(
                stop=tenacity.stop_after_attempt(max_attempts),
                wait=tenacity.wait_exponential(exp_base=2, multiplier=2),
                after=tenacity.after_log(logger=logger, log_level=1),
            )(self._async_get_response)
            response = await foo(
                message=prompt,
                temperature=temperature,
                max_tokens=max_tokens,
                model=model,
                returns_list=step.fanout,
            )
        else:
            # Use regular processing without retries
            response = await self._async_get_response(
                message=prompt,
                temperature=temperature,
                max_tokens=max_tokens,
                model=model,
                returns_list=step.fanout,
            )
    except Exception as e:
        error_msg = str(e).replace("\n", " ")
        logger.error(f"Error in _process_chain_step: {error_msg}")
        c_row[step.col] = f"[N/A] [ERROR: {error_msg}]"
    else:
        c_row[step.col] = response
```

### 6. Add Configuration for Ensemble Aggregation

```python
def parse_arguments():
    parser = argparse.ArgumentParser(description='Process Google Sheet data with LLM.')
    # ... existing arguments ...
    parser.add_argument('--ensemble-method', type=str, choices=["best_of_n", "multi_party_vote", "agreement_analysis", "disagreement_analysis"],
                        help='Ensemble aggregation method')
    parser.add_argument('--ensemble-samples', type=int, default=3,
                        help='Number of samples for ensemble aggregation')
    parser.add_argument('--ensemble-models', type=str, nargs='+',
                        help='Models to use for ensemble aggregation')
    parser.add_argument('--ensemble-temperatures', type=float, nargs='+',
                        help='Temperatures to use for ensemble aggregation')
    return parser.parse_args()

def main():
    args = parse_arguments()
    
    # ... existing code ...
    
    # Create ensemble configuration if requested
    ensemble_config = None
    if args.ensemble_method:
        ensemble_config = EnsembleConfig(
            method=EnsembleMethod(args.ensemble_method),
            num_samples=args.ensemble_samples,
            models=args.ensemble_models,
            temperatures=args.ensemble_temperatures,
        )
    
    # ... existing code ...
    
    # Process columns in sequence to handle dependencies between columns
    for idx, (
        prompt_template,
        model,
        temperature,
        max_tokens,
        flags,
        output_col,
    ) in enumerate(
        zip(
            prompt_templates,
            models,
            temperatures,
            max_output_tokens,
            flags,
            prompt_output_cols,
        )
    ):
        # ... existing code ...
        
        # Set the template column as the source for the prompt
        chain = [
            ChainStep(
                pt=template_col,  # Use the template column as source
                model=model,
                temperature=temperature,
                max_tokens=max_tokens,
                col=output_col,
                ensemble_config=ensemble_config,
            ),
        ]
        
        # ... continue with existing code ...
```

## Architecture Diagram

```mermaid
classDiagram
    class EnsembleMethod {
        <<enumeration>>
        BEST_OF_N
        MULTI_PARTY_VOTE
        AGREEMENT_ANALYSIS
        DISAGREEMENT_ANALYSIS
    }
    
    class EnsembleConfig {
        +EnsembleMethod method
        +int num_samples
        +List[str] models
        +List[float] temperatures
        +str scoring_function
        +Callable custom_aggregator
        +to_dict()
        +from_dict(config_dict)
    }
    
    class EnsembleAggregator {
        <<interface>>
        +aggregate(responses)
    }
    
    class BestOfNAggregator {
        +Callable scoring_function
        +_default_scoring_function(response)
        +aggregate(responses)
    }
    
    class MultiPartyVoteAggregator {
        +str voting_method
        +_extract_key_points(response)
        +_find_common_points(all_points)
        +aggregate(responses)
    }
    
    class AgreementAnalysisAggregator {
        +_find_agreement(responses)
        +_calculate_similarity(statement1, statement2)
        +aggregate(responses)
    }
    
    class DisagreementAnalysisAggregator {
        +_find_disagreement(responses)
        +_calculate_similarity(text1, text2)
        +aggregate(responses)
    }
    
    class EnsembleProcessor {
        +ParallelLLMDataFrameProcessor llm_processor
        +Dict aggregators
        +get_aggregator(method)
        +process_with_ensemble(prompt, config, base_model, base_temperature, base_max_tokens)
    }
    
    class ChainStep {
        +str pt
        +Dict mapping
        +float temperature
        +int max_tokens
        +str model
        +str col
        +bool fanout
        +str unfan_col
        +bool overwrite
        +List validators
        +EnsembleConfig ensemble_config
    }
    
    EnsembleMethod <-- EnsembleConfig : uses
    EnsembleAggregator <|-- BestOfNAggregator : implements
    EnsembleAggregator <|-- MultiPartyVoteAggregator : implements
    EnsembleAggregator <|-- AgreementAnalysisAggregator : implements
    EnsembleAggregator <|-- DisagreementAnalysisAggregator : implements
    EnsembleAggregator <-- EnsembleProcessor : uses
    EnsembleConfig <-- ChainStep : contains
    EnsembleProcessor --> ParallelLLMDataFrameProcessor : uses
```

## Example Usage

### 1. Best-of-N

```bash
python main2.py --ensemble-method best_of_n --ensemble-samples 5
```

This will generate 5 responses for each prompt and select the best one based on the default scoring function.

### 2. Multi-Party Vote

```bash
python main2.py --ensemble-method multi_party_vote --ensemble-models gpt-4o gpt-3.5-turbo claude-3-sonnet-20240229
```

This will generate responses from three different models and use voting to determine the final answer.

### 3. Agreement Analysis

```bash
python main2.py --ensemble-method agreement_analysis --ensemble-temperatures 0.3 0.5 0.7 0.9
```

This will generate responses at four different temperature settings and focus on areas where they agree.

### 4. Disagreement Analysis

```bash
python main2.py --ensemble-method disagreement_analysis --ensemble-models gpt-4o claude-3-sonnet-20240229 gemini-pro
```

This will generate responses from three different models and analyze where they disagree.

## Context and Considerations

1. **Improved Quality**: Ensemble methods can significantly improve the quality and reliability of LLM outputs by combining multiple perspectives or selecting the best response.

2. **Robustness**: Ensemble methods can make the system more robust to model-specific biases and errors by incorporating multiple models or parameter settings.

3. **Flexibility**: The implementation supports multiple aggregation methods, allowing users to choose the most appropriate method for their use case.

4. **Configurability**: The implementation allows for configuration of the ensemble method, number of samples, models, and temperatures.

5. **Integration with Existing Code**: The implementation builds on the existing code, maintaining compatibility with the current workflow.

6. **Performance Impact**: Ensemble methods require generating multiple responses, which can increase processing time and costs. The implementation should be used judiciously.

7. **Future Enhancements**: 
   - More sophisticated scoring functions for Best-of-N
   - Better semantic similarity measures for agreement and disagreement analysis
   - Support for more aggregation methods
   - Integration with model-specific quality metrics
