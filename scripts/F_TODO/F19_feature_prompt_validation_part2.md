# Feature: Prompt Validation and Debugging (Part 2)

Original requirement:
> Prompt validation and debugging -> how to debug prompts? Validate that all vars are filled in, etc.

## Implementation Status: Not Implemented

In Part 1, we covered prompt validation. In Part 2, we'll focus on prompt debugging tools and integration with the main workflow.

## Implementation Recommendation: Prompt Debugging

### 1. Implement Prompt Debugging Tools

```python
class PromptDebugInfo:
    """
    Information for debugging a prompt.
    """
    
    def __init__(
        self,
        original_template: str,
        filled_template: str,
        template_vars: List[str],
        available_vars: Dict[str, Any],
        used_vars: Dict[str, Any],
        validation_result: Optional[PromptValidationResult] = None,
    ):
        """
        Initialize prompt debug information.
        
        Args:
            original_template: Original template string
            filled_template: Template with variables filled in
            template_vars: List of template variables found
            available_vars: Dictionary of available variables
            used_vars: Dictionary of variables that were used
            validation_result: Result of prompt validation
        """
        self.original_template = original_template
        self.filled_template = filled_template
        self.template_vars = template_vars
        self.available_vars = available_vars
        self.used_vars = used_vars
        self.validation_result = validation_result
    
    def __str__(self) -> str:
        """String representation of debug information."""
        parts = [
            "Prompt Debug Information",
            "=======================",
            "",
            "Original Template:",
            self.original_template,
            "",
            "Filled Template:",
            self.filled_template,
            "",
            f"Template Variables ({len(self.template_vars)}):",
        ]
        
        for var in self.template_vars:
            parts.append(f"  - {var}")
        
        parts.extend([
            "",
            f"Available Variables ({len(self.available_vars)}):",
        ])
        
        for var, value in self.available_vars.items():
            parts.append(f"  - {var}: {value}")
        
        parts.extend([
            "",
            f"Used Variables ({len(self.used_vars)}):",
        ])
        
        for var, value in self.used_vars.items():
            parts.append(f"  - {var}: {value}")
        
        if self.validation_result:
            parts.extend([
                "",
                "Validation Result:",
                str(self.validation_result),
            ])
        
        return "\n".join(parts)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage or display."""
        return {
            "original_template": self.original_template,
            "filled_template": self.filled_template,
            "template_vars": self.template_vars,
            "available_vars": self.available_vars,
            "used_vars": self.used_vars,
            "validation_result": self.validation_result.to_dict() if self.validation_result else None,
        }
    
    def to_markdown(self) -> str:
        """Convert to markdown for display."""
        parts = [
            "# Prompt Debug Information",
            "",
            "## Original Template",
            "```",
            self.original_template,
            "```",
            "",
            "## Filled Template",
            "```",
            self.filled_template,
            "```",
            "",
            f"## Template Variables ({len(self.template_vars)})",
        ]
        
        for var in self.template_vars:
            parts.append(f"- `{var}`")
        
        parts.extend([
            "",
            f"## Available Variables ({len(self.available_vars)})",
            "",
            "| Variable | Value |",
            "| --- | --- |",
        ])
        
        for var, value in self.available_vars.items():
            parts.append(f"| `{var}` | `{value}` |")
        
        parts.extend([
            "",
            f"## Used Variables ({len(self.used_vars)})",
            "",
            "| Variable | Value |",
            "| --- | --- |",
        ])
        
        for var, value in self.used_vars.items():
            parts.append(f"| `{var}` | `{value}` |")
        
        if self.validation_result:
            parts.extend([
                "",
                "## Validation Result",
            ])
            
            if not self.validation_result.has_issues():
                parts.append("No validation issues found.")
            else:
                errors = self.validation_result.get_issues_by_level(ValidationLevel.ERROR)
                if errors:
                    parts.extend([
                        "",
                        f"### Errors ({len(errors)})",
                        "",
                    ])
                    for i, error in enumerate(errors, 1):
                        parts.extend([
                            f"{i}. **{error.message}**",
                            f"   - Location: {error.location}" if error.location else "",
                            f"   - Suggestion: {error.suggestion}" if error.suggestion else "",
                            "",
                        ])
                
                warnings = self.validation_result.get_issues_by_level(ValidationLevel.WARNING)
                if warnings:
                    parts.extend([
                        "",
                        f"### Warnings ({len(warnings)})",
                        "",
                    ])
                    for i, warning in enumerate(warnings, 1):
                        parts.extend([
                            f"{i}. **{warning.message}**",
                            f"   - Location: {warning.location}" if warning.location else "",
                            f"   - Suggestion: {warning.suggestion}" if warning.suggestion else "",
                            "",
                        ])
                
                infos = self.validation_result.get_issues_by_level(ValidationLevel.INFO)
                if infos:
                    parts.extend([
                        "",
                        f"### Info ({len(infos)})",
                        "",
                    ])
                    for i, info in enumerate(infos, 1):
                        parts.extend([
                            f"{i}. **{info.message}**",
                            f"   - Location: {info.location}" if info.location else "",
                            f"   - Suggestion: {info.suggestion}" if info.suggestion else "",
                            "",
                        ])
        
        return "\n".join(parts)

class PromptDebugger:
    """
    Tools for debugging prompts.
    """
    
    @staticmethod
    def debug_template(
        template: str,
        row: Dict[str, Any],
        input_cols: List[str],
        validate: bool = True,
    ) -> PromptDebugInfo:
        """
        Debug a template.
        
        Args:
            template: Template string
            row: DataFrame row with input values
            input_cols: List of column names to use for variable replacement
            validate: Whether to validate the filled template
            
        Returns:
            PromptDebugInfo instance
        """
        # Find all template variables
        template_vars = re.findall(r"\{\{([^}]+)\}\}", template)
        
        # Track which variables were used and their values
        used_vars = {}
        available_vars = {col: row.get(col, None) for col in input_cols if col in row}
        
        # Fill template variables
        filled_template = template
        for var in template_vars:
            base_var = var.split(".")[0]  # Handle dot notation
            
            if base_var in row and base_var in input_cols:
                value = row[base_var]
                filled_template = filled_template.replace("{{" + var + "}}", str(value))
                used_vars[base_var] = value
        
        # Validate if requested
        validation_result = None
        if validate:
            validator = create_default_validator()
            validation_result = validator.validate(filled_template, {
                "used_vars": used_vars,
                "available_vars": available_vars,
                "template_vars": template_vars,
            })
        
        # Create debug info
        debug_info = PromptDebugInfo(
            original_template=template,
            filled_template=filled_template,
            template_vars=template_vars,
            available_vars=available_vars,
            used_vars=used_vars,
            validation_result=validation_result,
        )
        
        return debug_info
    
    @staticmethod
    def save_debug_info(debug_info: PromptDebugInfo, file_path: str) -> None:
        """
        Save debug information to a file.
        
        Args:
            debug_info: Debug information to save
            file_path: Path to save to
        """
        with open(file_path, "w") as f:
            f.write(debug_info.to_markdown())
    
    @staticmethod
    def highlight_template_vars(template: str) -> str:
        """
        Highlight template variables in a string.
        
        Args:
            template: Template string
            
        Returns:
            Template with variables highlighted
        """
        # Find all template variables
        template_vars = re.findall(r"\{\{([^}]+)\}\}", template)
        
        # Highlight each variable
        highlighted = template
        for var in template_vars:
            highlighted = highlighted.replace(
                "{{" + var + "}}",
                f"[HIGHLIGHT]{{{{{{var}}}}}}[/HIGHLIGHT]",
            )
        
        return highlighted
    
    @staticmethod
    def compare_templates(template1: str, template2: str) -> str:
        """
        Compare two templates and highlight differences.
        
        Args:
            template1: First template
            template2: Second template
            
        Returns:
            Markdown with differences highlighted
        """
        import difflib
        
        # Generate diff
        diff = difflib.ndiff(template1.splitlines(), template2.splitlines())
        
        # Format diff as markdown
        parts = [
            "# Template Comparison",
            "",
            "```diff",
        ]
        
        for line in diff:
            if line.startswith("+ "):
                parts.append(line)
            elif line.startswith("- "):
                parts.append(line)
            elif line.startswith("? "):
                # Skip diff markers
                continue
            else:
                parts.append(line)
        
        parts.append("```")
        
        return "\n".join(parts)
```

### 2. Add Prompt Debugging to Main Function

```python
def parse_arguments():
    parser = argparse.ArgumentParser(description='Process Google Sheet data with LLM.')
    # ... existing arguments ...
    parser.add_argument('--debug-prompts', action='store_true',
                        help='Enable prompt debugging')
    parser.add_argument('--debug-output-dir', type=str, default='debug_output',
                        help='Directory for debug output')
    parser.add_argument('--validate-prompts', action='store_true',
                        help='Validate prompts before sending to LLM')
    return parser.parse_args()

def main():
    args = parse_arguments()
    
    # ... existing code ...
    
    # Create debug output directory if needed
    if args.debug_prompts:
        os.makedirs(args.debug_output_dir, exist_ok=True)
    
    # ... existing code ...
    
    # Process columns in sequence to handle dependencies between columns
    for idx, (
        prompt_template,
        model,
        temperature,
        max_tokens,
        flags,
        output_col,
    ) in enumerate(
        zip(
            prompt_templates,
            models,
            temperatures,
            max_output_tokens,
            flags,
            prompt_output_cols,
        )
    ):
        # ... existing code ...
        
        # Fill templates with available variables
        if args.debug_prompts or args.validate_prompts:
            # Use enhanced template filling with validation
            chain_input[template_col] = chain_input.apply(
                lambda row: fill_template_with_validation(
                    row,
                    prompt_template,
                    current_input_cols,
                    validate=True,
                )[0],
                axis=1,
            )
            
            # Debug the first few prompts
            if args.debug_prompts:
                for i, (_, row) in enumerate(chain_input.head(3).iterrows()):
                    debug_info = PromptDebugger.debug_template(
                        template=prompt_template,
                        row=row,
                        input_cols=current_input_cols,
                        validate=True,
                    )
                    
                    # Save debug info
                    debug_file = os.path.join(
                        args.debug_output_dir,
                        f"{output_col}_row{i+1}_debug.md",
                    )
                    PromptDebugger.save_debug_info(debug_info, debug_file)
                    
                    logger.info(
                        f"{Fore.CYAN}Saved prompt debug info to {debug_file}{Style.RESET_ALL}"
                    )
        else:
            # Use regular template filling
            chain_input[template_col] = chain_input.apply(
                lambda row: fill_template(row, prompt_template, current_input_cols),
                axis=1,
            )
        
        # ... continue with existing code ...
```

### 3. Add Prompt Validation to LLM Processor

```python
async def _process_chain_step_with_validation(
    self,
    c_row: pd.Series,
    step: ChainStep,
    mapping: dict,
    max_attempts: int,
    validate_prompt: bool = False,
) -> None:
    """
    Process a single chain step for a given row with prompt validation.
    
    Args:
        c_row (pd.Series): The current row of the DataFrame.
        step (ChainStep): The chain step to process.
        mapping (dict): The mapping of prompt keywords to column names.
        max_attempts (int): The maximum number of retry attempts for failed API calls.
        validate_prompt (bool): Whether to validate the prompt before sending to LLM.
    """
    prompt = c_row.get(step.pt)
    if c_row.get(step.col) and not step.overwrite:
        logger.debug(f"{step.col} already exists (skip)")
        return
    
    # Apply mapping
    for k, v in mapping.items():
        prompt = prompt.replace(f"{{{k}}}", str(c_row.get(v, "N/A")))

    # Get parameters
    temperature = (
        c_row.get("__temperature")
        or step.temperature
        or self.def_temperature
    )
    max_tokens = (
        c_row.get("__max_tokens") or step.max_tokens or self.def_max_tokens
    )
    model = c_row.get("__model") or step.model or self.def_model

    # Validate prompt if requested
    if validate_prompt:
        validator = create_default_validator()
        validation_result = validator.validate(prompt)
        
        if validation_result.has_errors():
            # Log validation errors
            logger.warning(
                f"{Fore.YELLOW}Prompt validation errors for {step.col}:{Style.RESET_ALL}\n{validation_result}"
            )
            
            # Store validation errors in the output column
            error_msg = f"[VALIDATION ERROR] {validation_result.get_issues_by_level(ValidationLevel.ERROR)[0].message}"
            c_row[step.col] = error_msg
            return
        
        if validation_result.has_warnings():
            # Log validation warnings
            logger.warning(
                f"{Fore.YELLOW}Prompt validation warnings for {step.col}:{Style.RESET_ALL}\n{validation_result}"
            )
    
    # Continue with normal processing
    try:
        if max_attempts > 1:
            foo = tenacity.retry(
                stop=tenacity.stop_after_attempt(max_attempts),
                wait=tenacity.wait_exponential(exp_base=2, multiplier=2),
                after=tenacity.after_log(logger=logger, log_level=1),
            )(self._async_get_response)
            response = await foo(
                message=prompt,
                temperature=temperature,
                max_tokens=max_tokens,
                model=model,
                returns_list=step.fanout,
            )
        else:
            response = await self._async_get_response(
                message=prompt,
                temperature=temperature,
                max_tokens=max_tokens,
                model=model,
                returns_list=step.fanout,
            )
    except Exception as e:
        error_msg = str(e).replace("\n", " ")
        logger.error(f"Error in _process_chain_step: {error_msg}")
        c_row[step.col] = f"[N/A] [ERROR: {error_msg}]"
    else:
        c_row[step.col] = response
```

### 4. Add Interactive Prompt Debugging

```python
def debug_prompt_interactive(
    template: str,
    row: Dict[str, Any],
    input_cols: List[str],
) -> None:
    """
    Interactive prompt debugging.
    
    Args:
        template: Template string
        row: DataFrame row with input values
        input_cols: List of column names to use for variable replacement
    """
    import tempfile
    import subprocess
    import platform
    
    # Create debug info
    debug_info = PromptDebugger.debug_template(
        template=template,
        row=row,
        input_cols=input_cols,
        validate=True,
    )
    
    # Save to temporary file
    with tempfile.NamedTemporaryFile(suffix=".md", delete=False) as f:
        f.write(debug_info.to_markdown().encode("utf-8"))
        temp_file = f.name
    
    # Open in default markdown viewer
    if platform.system() == "Darwin":  # macOS
        subprocess.run(["open", temp_file])
    elif platform.system() == "Windows":
        subprocess.run(["start", temp_file], shell=True)
    elif platform.system() == "Linux":
        subprocess.run(["xdg-open", temp_file])
    
    print(f"Prompt debug info saved to {temp_file}")
    print("Press Enter to continue...")
    input()
    
    # Clean up
    try:
        os.unlink(temp_file)
    except:
        pass

def parse_arguments():
    parser = argparse.ArgumentParser(description='Process Google Sheet data with LLM.')
    # ... existing arguments ...
    parser.add_argument('--debug-interactive', action='store_true',
                        help='Enable interactive prompt debugging')
    parser.add_argument('--debug-row', type=int, default=0,
                        help='Row index to debug (0-based)')
    parser.add_argument('--debug-column', type=str,
                        help='Column name to debug')
    return parser.parse_args()

def main():
    args = parse_arguments()
    
    # ... existing code ...
    
    # Handle interactive debugging
    if args.debug_interactive:
        if args.debug_column and args.debug_column in prompt_output_cols:
            col_idx = prompt_output_cols.index(args.debug_column)
            template = prompt_templates[col_idx]
            
            # Get row to debug
            if args.debug_row < len(prompt_inputs):
                row = prompt_inputs.iloc[args.debug_row].to_dict()
                
                # Get input columns for this output column
                current_input_cols = list(prompt_input_cols)
                
                # Add any output columns processed before this one as potential input variables
                for prev_idx in range(col_idx):
                    prev_col = prompt_output_cols[prev_idx]
                    if prev_col not in current_input_cols and prev_col in prompt_inputs.columns:
                        current_input_cols.append(prev_col)
                
                # Debug interactively
                debug_prompt_interactive(
                    template=template,
                    row=row,
                    input_cols=current_input_cols,
                )
                
                # Exit after debugging
                sys.exit(0)
            else:
                logger.error(f"{Fore.RED}Invalid row index: {args.debug_row}{Style.RESET_ALL}")
                sys.exit(1)
        else:
            logger.error(f"{Fore.RED}Invalid column name: {args.debug_column}{Style.RESET_ALL}")
            sys.exit(1)
    
    # ... continue with existing code ...
```

### 5. Add Prompt Visualization

```python
def visualize_prompt_template(template: str, output_file: str) -> None:
    """
    Visualize a prompt template.
    
    Args:
        template: Template string
        output_file: Output file path
    """
    # Find all template variables
    template_vars = re.findall(r"\{\{([^}]+)\}\}", template)
    
    # Create a graph of template variables
    parts = [
        "# Prompt Template Visualization",
        "",
        "```mermaid",
        "graph TD",
        "    Template[\"Template\"]",
    ]
    
    # Add nodes for each variable
    for i, var in enumerate(template_vars):
        parts.append(f"    Var{i}[\"{var}\"]")
    
    # Add edges from template to variables
    for i, _ in enumerate(template_vars):
        parts.append(f"    Template --> Var{i}")
    
    # Add dependencies between variables
    for i, var_i in enumerate(template_vars):
        base_var_i = var_i.split(".")[0]
        for j, var_j in enumerate(template_vars):
            if i != j:
                base_var_j = var_j.split(".")[0]
                if base_var_i == base_var_j:
                    parts.append(f"    Var{i} --- Var{j}")
    
    parts.extend([
        "```",
        "",
        "## Template",
        "```",
        template,
        "```",
        "",
        "## Variables",
    ])
    
    for var in template_vars:
        parts.append(f"- `{var}`")
    
    # Write to file
    with open(output_file, "w") as f:
        f.write("\n".join(parts))

def parse_arguments():
    parser = argparse.ArgumentParser(description='Process Google Sheet data with LLM.')
    # ... existing arguments ...
    parser.add_argument('--visualize-templates', action='store_true',
                        help='Visualize prompt templates')
    parser.add_argument('--visualization-dir', type=str, default='visualizations',
                        help='Directory for template visualizations')
    return parser.parse_args()

def main():
    args = parse_arguments()
    
    # ... existing code ...
    
    # Visualize templates if requested
    if args.visualize_templates:
        os.makedirs(args.visualization_dir, exist_ok=True)
        
        for i, (template, output_col) in enumerate(zip(prompt_templates, prompt_output_cols)):
            visualization_file = os.path.join(
                args.visualization_dir,
                f"{output_col}_template.md",
            )
            
            visualize_prompt_template(template, visualization_file)
            
            logger.info(
                f"{Fore.CYAN}Saved template visualization to {visualization_file}{Style.RESET_ALL}"
            )
    
    # ... continue with existing code ...
```

## Architecture Diagram

```mermaid
flowchart TD
    A[Start] --> B[Parse Arguments]
    B --> C{Debug Interactive?}
    
    C -->|Yes| D[Debug Prompt Interactively]
    C -->|No| E{Visualize Templates?}
    
    E -->|Yes| F[Visualize Prompt Templates]
    E -->|No| G[Process Columns]
    
    G --> H{For Each Column}
    H --> I{Debug Prompts?}
    
    I -->|Yes| J[Fill Templates with Validation]
    I -->|No| K[Fill Templates Normally]
    
    J --> L[Save Debug Info]
    L --> M[Process with LLM]
    K --> M
    
    M --> N[Update Sheet]
    N --> H
    
    D --> O[Exit]
    F --> G
    H --> P[End]
    
    subgraph Prompt Debugging
        D
        J
        L
    end
    
    subgraph Prompt Visualization
        F
    end
```

## Example Usage

### 1. Interactive Debugging

```bash
python main2.py --debug-interactive --debug-column new_career --debug-row 0
```

### 2. Batch Debugging

```bash
python main2.py --debug-prompts --debug-output-dir debug_output
```

### 3. Template Visualization

```bash
python main2.py --visualize-templates --visualization-dir visualizations
```

### 4. Prompt Validation

```bash
python main2.py --validate-prompts
```

## Context and Considerations

1. **Interactive Debugging**: The implementation provides interactive debugging of prompts, making it easier to identify and fix issues.

2. **Batch Debugging**: The implementation supports batch debugging of prompts, generating detailed debug information for multiple prompts.

3. **Template Visualization**: The implementation includes visualization of prompt templates, helping to understand the structure and dependencies of templates.

4. **Integration with Validation**: The implementation integrates debugging with validation, providing comprehensive information about prompt issues.

5. **Markdown Output**: The implementation generates debug information in markdown format, making it easy to view and share.

6. **Command Line Interface**: The implementation provides a command line interface for debugging and visualization, making it easy to use.

7. **Future Enhancements**: 
   - Integration with a web-based debugging interface
   - Support for collaborative debugging
   - Machine learning-based prompt improvement suggestions
   - Integration with prompt libraries and best practices
