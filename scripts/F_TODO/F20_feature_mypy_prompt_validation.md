# MYPY: Prompt Template Validation

Original feature description from features_list.md:
```
MYPY:   HOW TO ADJUST PROMPT TEMPLATE SETUP FOR 
CATCHING COMPILING ERRORS -> e.g, prompt variables not going in, or accidental miss-construction. NEED to have unit-tests for this.
--> and need to have compiled prompt pushed somewhere in sheets, even if just a hidden sheet that can be used as a lookup.
```

## Feature Description

This feature focuses on implementing static type checking and validation for prompt templates in the gsheet-model-breaker-demo tool. Similar to how MyPy provides static type checking for Python code, this feature would validate prompt templates before execution to catch errors such as:

1. **Missing Variables**: Variables referenced in templates but not available in the data
2. **Type Mismatches**: Variables that exist but have incompatible types
3. **Syntax Errors**: Malformed template syntax
4. **Unused Variables**: Variables available in the data but not used in templates

Additionally, the feature would store "compiled" (fully validated) templates in a hidden sheet for reference and debugging.

## Current Implementation Status

Currently, the gsheet-model-breaker-demo has basic template filling functionality but lacks comprehensive validation:

<augment_code_snippet path="experiments/src/tools/gsheet-model-breaker-demo/scripts/main2.py" mode="EXCERPT">
````python
def fill_template(row, template, input_cols):
    """
    Fill template with values from a DataFrame row.

    Args:
        row: DataFrame row with input values
        template: String template with {variable} placeholders
        input_cols: List of column names to use for variable replacement

    Returns:
        Filled template with all variables replaced
    """
    for col in input_cols:
        if col in row:
            template = template.replace("{{" + col + "}}", str(row[col]))
        else:
            # Replace with empty string if column doesn't exist
            template = template.replace("{{" + col + "}}", "")

    # Find any remaining template variables and replace them with empty strings
    remaining_vars = re.findall(r"\{\{([^}]+)\}\}", template)
    for var in remaining_vars:
        template = template.replace("{{" + var + "}}", "")

    return template
````
</augment_code_snippet>

The current implementation silently replaces missing variables with empty strings, which can lead to subtle errors in the generated prompts.

## Proposed Implementation

### 1. Prompt Template Validator Class

Create a dedicated class for template validation:

```python
from typing import Dict, List, Set, Optional, Any, NamedTuple
import re
from enum import Enum

class TemplateErrorType(Enum):
    MISSING_VARIABLE = "missing_variable"
    UNUSED_VARIABLE = "unused_variable"
    SYNTAX_ERROR = "syntax_error"
    TYPE_MISMATCH = "type_mismatch"

class TemplateError(NamedTuple):
    error_type: TemplateErrorType
    message: str
    variable: Optional[str] = None
    location: Optional[str] = None

class PromptTemplateValidator:
    """Validates prompt templates for errors before execution."""
    
    def __init__(self, strict_mode: bool = False):
        """
        Initialize the validator.
        
        Args:
            strict_mode: If True, raise exceptions for validation errors.
                         If False, log warnings but allow execution.
        """
        self.strict_mode = strict_mode
        self.template_cache = {}  # Cache of validated templates
    
    def extract_variables(self, template: str) -> Set[str]:
        """Extract all variable names from a template."""
        return set(re.findall(r"\{\{([^}]+)\}\}", template))
    
    def validate_template(self, template: str, available_vars: List[str], 
                          col_name: str = None) -> List[TemplateError]:
        """
        Validate a template against available variables.
        
        Args:
            template: The template string to validate
            available_vars: List of variable names available for substitution
            col_name: Optional column name for error reporting
            
        Returns:
            List of TemplateError objects describing any validation issues
        """
        errors = []
        
        # Check for syntax errors (unbalanced braces)
        open_braces = template.count("{{")
        close_braces = template.count("}}")
        if open_braces != close_braces:
            errors.append(TemplateError(
                error_type=TemplateErrorType.SYNTAX_ERROR,
                message=f"Unbalanced braces: {open_braces} opening '{{{{' vs {close_braces} closing '}}}}'",
                location=col_name
            ))
        
        # Extract variables from the template
        template_vars = self.extract_variables(template)
        
        # Check for missing variables
        available_vars_set = set(available_vars)
        missing_vars = template_vars - available_vars_set
        for var in missing_vars:
            errors.append(TemplateError(
                error_type=TemplateErrorType.MISSING_VARIABLE,
                message=f"Variable '{var}' referenced in template but not available",
                variable=var,
                location=col_name
            ))
        
        # Check for unused variables (optional warning)
        unused_vars = available_vars_set - template_vars
        for var in unused_vars:
            errors.append(TemplateError(
                error_type=TemplateErrorType.UNUSED_VARIABLE,
                message=f"Variable '{var}' available but not used in template",
                variable=var,
                location=col_name
            ))
        
        return errors
    
    def validate_and_compile(self, template: str, available_vars: List[str], 
                            sample_data: Dict[str, Any] = None, 
                            col_name: str = None) -> Dict:
        """
        Validate a template and compile it with sample data if provided.
        
        Args:
            template: The template string to validate
            available_vars: List of variable names available for substitution
            sample_data: Optional dictionary of sample data for compilation
            col_name: Optional column name for error reporting
            
        Returns:
            Dictionary with validation results and compiled template
        """
        errors = self.validate_template(template, available_vars, col_name)
        
        # Compile the template with sample data if provided
        compiled_template = None
        if sample_data:
            try:
                compiled_template = template
                for var, value in sample_data.items():
                    if var in available_vars:
                        compiled_template = compiled_template.replace(f"{{{{{var}}}}}", str(value))
            except Exception as e:
                errors.append(TemplateError(
                    error_type=TemplateErrorType.TYPE_MISMATCH,
                    message=f"Error compiling template: {str(e)}",
                    location=col_name
                ))
        
        # Cache the validation result
        cache_key = f"{col_name}:{template}"
        self.template_cache[cache_key] = {
            "template": template,
            "variables": list(self.extract_variables(template)),
            "errors": [e._asdict() for e in errors],
            "compiled": compiled_template
        }
        
        # If in strict mode and there are errors, raise an exception
        if self.strict_mode and any(e.error_type != TemplateErrorType.UNUSED_VARIABLE for e in errors):
            error_msgs = "\n".join([f"- {e.error_type.value}: {e.message}" for e in errors 
                                   if e.error_type != TemplateErrorType.UNUSED_VARIABLE])
            raise ValueError(f"Template validation failed for {col_name}:\n{error_msgs}")
        
        return {
            "template": template,
            "variables": list(self.extract_variables(template)),
            "errors": errors,
            "compiled": compiled_template
        }
    
    def get_validation_summary(self) -> Dict:
        """Get a summary of all template validations."""
        return {
            "templates_validated": len(self.template_cache),
            "templates_with_errors": sum(1 for v in self.template_cache.values() 
                                        if any(e["error_type"] != TemplateErrorType.UNUSED_VARIABLE.value 
                                              for e in v["errors"])),
            "error_counts": self._count_errors_by_type(),
            "cache": self.template_cache
        }
    
    def _count_errors_by_type(self) -> Dict[str, int]:
        """Count errors by type across all validated templates."""
        counts = {e.value: 0 for e in TemplateErrorType}
        for v in self.template_cache.values():
            for e in v["errors"]:
                counts[e["error_type"]] += 1
        return counts
```

### 2. Integration with Main Processing Flow

Update the main2.py file to use the template validator:

```python
# Initialize the template validator
template_validator = PromptTemplateValidator(strict_mode=False)

# Validate templates before processing
validation_results = {}
for idx, (prompt_template, output_col) in enumerate(zip(prompt_templates, prompt_output_cols)):
    # Determine available variables for this template
    current_input_cols = prompt_input_cols.copy()
    
    # Add any output columns processed before this one as potential input variables
    for prev_idx in range(idx):
        prev_col = prompt_output_cols[prev_idx]
        if prev_col not in current_input_cols and prev_col in prompt_inputs.columns:
            current_input_cols.append(prev_col)
    
    # Get sample data from the first row (if available)
    sample_data = {}
    if len(prompt_inputs) > 0:
        for col in current_input_cols:
            if col in prompt_inputs.columns:
                sample_data[col] = prompt_inputs[col].iloc[0]
    
    # Validate the template
    validation_result = template_validator.validate_and_compile(
        prompt_template, 
        current_input_cols,
        sample_data,
        output_col
    )
    
    validation_results[output_col] = validation_result
    
    # Log validation results
    if validation_result["errors"]:
        error_count = sum(1 for e in validation_result["errors"] 
                         if e.error_type != TemplateErrorType.UNUSED_VARIABLE)
        warning_count = sum(1 for e in validation_result["errors"] 
                           if e.error_type == TemplateErrorType.UNUSED_VARIABLE)
        
        if error_count > 0:
            logger.warning(f"{Fore.YELLOW}Template validation for {output_col} found {error_count} errors:{Style.RESET_ALL}")
            for e in validation_result["errors"]:
                if e.error_type != TemplateErrorType.UNUSED_VARIABLE:
                    logger.warning(f"{Fore.YELLOW}- {e.error_type.value}: {e.message}{Style.RESET_ALL}")
        
        if warning_count > 0:
            logger.info(f"{Fore.CYAN}Template validation for {output_col} found {warning_count} warnings:{Style.RESET_ALL}")
            for e in validation_result["errors"]:
                if e.error_type == TemplateErrorType.UNUSED_VARIABLE:
                    logger.info(f"{Fore.CYAN}- {e.message}{Style.RESET_ALL}")
    else:
        logger.info(f"{Fore.GREEN}Template validation for {output_col} passed with no issues{Style.RESET_ALL}")
```

### 3. Store Compiled Templates in Hidden Sheet

Add functionality to store compiled templates in a hidden sheet:

```python
def store_compiled_templates(sheet_id, validation_results):
    """
    Store compiled templates in a hidden sheet for reference.
    
    Args:
        sheet_id: The ID of the Google Sheet
        validation_results: Dictionary of validation results by column
    """
    # Get credentials and build service
    creds = get_credentials()
    service = build('sheets', 'v4', credentials=creds)
    
    # Check if the hidden sheet exists, create it if not
    sheet_name = "_compiled_templates"
    sheet_exists = False
    
    try:
        # Get the spreadsheet to check for the hidden sheet
        spreadsheet = service.spreadsheets().get(spreadsheetId=sheet_id).execute()
        
        for sheet in spreadsheet['sheets']:
            if sheet['properties']['title'] == sheet_name:
                sheet_exists = True
                break
        
        if not sheet_exists:
            # Create the hidden sheet
            request_body = {
                'requests': [
                    {
                        'addSheet': {
                            'properties': {
                                'title': sheet_name,
                                'hidden': True
                            }
                        }
                    }
                ]
            }
            service.spreadsheets().batchUpdate(
                spreadsheetId=sheet_id,
                body=request_body
            ).execute()
    except Exception as e:
        logger.error(f"Error creating hidden sheet: {str(e)}")
        return
    
    # Prepare the data for the hidden sheet
    headers = ["Column", "Template", "Variables", "Errors", "Compiled Template", "Timestamp"]
    rows = [headers]
    
    for col_name, result in validation_results.items():
        variables_str = ", ".join(result["variables"])
        errors_str = "; ".join([f"{e.error_type.value}: {e.message}" for e in result["errors"]]) if result["errors"] else "None"
        timestamp = datetime.now().isoformat()
        
        rows.append([
            col_name,
            result["template"],
            variables_str,
            errors_str,
            result["compiled"] or "Not compiled",
            timestamp
        ])
    
    # Update the hidden sheet
    try:
        # Clear the existing content
        service.spreadsheets().values().clear(
            spreadsheetId=sheet_id,
            range=f"{sheet_name}!A:Z"
        ).execute()
        
        # Write the new content
        service.spreadsheets().values().update(
            spreadsheetId=sheet_id,
            range=f"{sheet_name}!A1",
            valueInputOption="RAW",
            body={"values": rows}
        ).execute()
        
        logger.info(f"{Fore.GREEN}Compiled templates stored in hidden sheet '{sheet_name}'{Style.RESET_ALL}")
    except Exception as e:
        logger.error(f"Error updating hidden sheet: {str(e)}")
```

### 4. Unit Tests for Template Validation

Create unit tests for the template validation functionality:

```python
# test_template_validation.py
import unittest
from template_validator import PromptTemplateValidator, TemplateErrorType

class TestTemplateValidator(unittest.TestCase):
    def setUp(self):
        self.validator = PromptTemplateValidator(strict_mode=False)
    
    def test_extract_variables(self):
        template = "Hello {{name}}, your age is {{age}}."
        variables = self.validator.extract_variables(template)
        self.assertEqual(variables, {"name", "age"})
    
    def test_missing_variable(self):
        template = "Hello {{name}}, your age is {{age}}."
        available_vars = ["name"]
        errors = self.validator.validate_template(template, available_vars)
        
        # Should find one missing variable error
        missing_errors = [e for e in errors if e.error_type == TemplateErrorType.MISSING_VARIABLE]
        self.assertEqual(len(missing_errors), 1)
        self.assertEqual(missing_errors[0].variable, "age")
    
    def test_unused_variable(self):
        template = "Hello {{name}}."
        available_vars = ["name", "age", "location"]
        errors = self.validator.validate_template(template, available_vars)
        
        # Should find two unused variable warnings
        unused_errors = [e for e in errors if e.error_type == TemplateErrorType.UNUSED_VARIABLE]
        self.assertEqual(len(unused_errors), 2)
        self.assertIn("age", [e.variable for e in unused_errors])
        self.assertIn("location", [e.variable for e in unused_errors])
    
    def test_syntax_error(self):
        template = "Hello {{name}, your age is {{age}}."  # Missing closing brace
        available_vars = ["name", "age"]
        errors = self.validator.validate_template(template, available_vars)
        
        # Should find one syntax error
        syntax_errors = [e for e in errors if e.error_type == TemplateErrorType.SYNTAX_ERROR]
        self.assertEqual(len(syntax_errors), 1)
    
    def test_compilation(self):
        template = "Hello {{name}}, your age is {{age}}."
        available_vars = ["name", "age"]
        sample_data = {"name": "John", "age": 30}
        
        result = self.validator.validate_and_compile(template, available_vars, sample_data)
        self.assertEqual(result["compiled"], "Hello John, your age is 30.")
    
    def test_strict_mode(self):
        strict_validator = PromptTemplateValidator(strict_mode=True)
        template = "Hello {{name}}, your age is {{age}}."
        available_vars = ["name"]
        
        # Should raise an exception in strict mode
        with self.assertRaises(ValueError):
            strict_validator.validate_and_compile(template, available_vars)

if __name__ == "__main__":
    unittest.main()
```

## Implementation Considerations

1. **Performance Impact**: Validation adds overhead to the processing pipeline, but the benefits in error prevention outweigh the cost.

2. **Strict vs. Lenient Mode**: Consider providing both strict mode (fails on errors) and lenient mode (warns but continues) to accommodate different use cases.

3. **Integration with UI**: Ideally, validation errors would be highlighted directly in the Google Sheet UI.

4. **Caching**: Cache validation results to avoid redundant validation of unchanged templates.

5. **Extensibility**: Design the validator to be extensible for future template syntax enhancements.

## Mermaid Diagram: Template Validation Flow

```mermaid
flowchart TD
    A[Extract Template<br>from Sheet] --> B[Determine Available<br>Variables]
    B --> C[Validate Template]
    C --> D{Validation<br>Successful?}
    
    D -->|Yes| E[Compile Template<br>with Sample Data]
    D -->|No, Strict Mode| F[Raise Exception<br>Halt Processing]
    D -->|No, Lenient Mode| G[Log Warnings<br>Continue Processing]
    
    E --> H[Store Compiled Template<br>in Hidden Sheet]
    G --> E
    
    H --> I[Proceed with<br>Normal Processing]
    
    subgraph "Validation Checks"
    C1[Check for<br>Syntax Errors]
    C2[Check for<br>Missing Variables]
    C3[Check for<br>Unused Variables]
    C4[Check for<br>Type Mismatches]
    end
    
    C --> C1
    C --> C2
    C --> C3
    C --> C4
```

## Example Validation Output

Here's an example of how validation errors might be reported:

```
Template validation for summary_col found 2 errors:
- missing_variable: Variable 'author' referenced in template but not available
- syntax_error: Unbalanced braces: 3 opening '{{' vs 2 closing '}}'

Template validation for analysis_col found 1 warning:
- unused_variable: Variable 'publication_date' available but not used in template
```

## Conclusion

Implementing MyPy-style validation for prompt templates would significantly improve the reliability and maintainability of the gsheet-model-breaker-demo tool. By catching template errors before execution, users can avoid wasted API calls and confusing outputs caused by malformed templates.

The proposed implementation provides comprehensive validation, including checks for missing variables, syntax errors, and unused variables. It also includes the ability to store compiled templates in a hidden sheet for reference and debugging.

This feature aligns with software engineering best practices by bringing static analysis techniques to prompt engineering, helping users catch errors early in the development process rather than during runtime.
