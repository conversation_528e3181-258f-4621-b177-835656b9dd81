# Iteration Mode

Original feature description from features_list.md:
```
Iteration mode -> duplicates sheet and updates rather than updating same sheet. Requires duplicating the formatting, groups, etc. Probalby just full duplicate the sheet first (may require waiting to complete...)
```

## Feature Description

The Iteration Mode feature allows users to run the model-breaker tool in a way that preserves the original sheet data by creating a duplicate sheet before making any updates. This is particularly useful for:

1. **Preserving Historical Data**: Keeping a record of previous runs for comparison
2. **Experimental Iterations**: Running multiple variations of prompts without overwriting previous results
3. **Audit Trail**: Maintaining a history of how outputs evolved over time
4. **Safety**: Preventing accidental loss of original data

The feature would duplicate the entire sheet, including all formatting, groupings, and data, then perform updates on the duplicate rather than the original.

## Current Implementation Status

Currently, the gsheet-model-breaker-demo does not implement an iteration mode. The tool updates the original sheet directly:

<augment_code_snippet path="experiments/src/tools/gsheet-model-breaker-demo/scripts/main2.py" mode="EXCERPT">
````python
# Directly update Google Sheets
update_to_sheet(update_df, prompt_output_cols, sheet_id, sheet_name)
````
</augment_code_snippet>

The `update_to_sheet` function in google_sheets_utils.py updates the original sheet without creating a duplicate:

<augment_code_snippet path="experiments/src/tools/gsheet-model-breaker-demo/llm_processor/google_sheets_utils.py" mode="EXCERPT">
````python
def update_to_sheet(df, cols, sheet_id, sheet_name):
    """
    Update a Google Sheet with data from a DataFrame.
    
    Args:
        df (pd.DataFrame): DataFrame containing the data to update.
        cols (list): List of column names to update.
        sheet_id (str): The ID of the Google Sheet.
        sheet_name (str): The name of the sheet to update.
    """
    # ... implementation details ...
````
</augment_code_snippet>

## Proposed Implementation

### 1. Sheet Duplication Function

First, we need to add a function to duplicate a sheet:

```python
def duplicate_sheet(sheet_id, source_sheet_name, new_sheet_name=None):
    """
    Duplicate a sheet within the same Google Sheets document.
    
    Args:
        sheet_id (str): The ID of the Google Sheet.
        source_sheet_name (str): The name of the sheet to duplicate.
        new_sheet_name (str, optional): The name for the new sheet. If None, a timestamp will be appended.
        
    Returns:
        str: The name of the newly created sheet.
    """
    # Get credentials and build service
    creds = get_credentials()
    service = build('sheets', 'v4', credentials=creds)
    
    # Get the spreadsheet to find the source sheet's ID
    spreadsheet = service.spreadsheets().get(spreadsheetId=sheet_id).execute()
    
    # Find the source sheet's ID
    source_sheet_id = None
    for sheet in spreadsheet['sheets']:
        if sheet['properties']['title'] == source_sheet_name:
            source_sheet_id = sheet['properties']['sheetId']
            break
    
    if source_sheet_id is None:
        raise ValueError(f"Sheet '{source_sheet_name}' not found")
    
    # Generate a new sheet name if not provided
    if new_sheet_name is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        new_sheet_name = f"{source_sheet_name}_iteration_{timestamp}"
    
    # Create duplicate sheet request
    request_body = {
        'requests': [
            {
                'duplicateSheet': {
                    'sourceSheetId': source_sheet_id,
                    'insertSheetIndex': len(spreadsheet['sheets']),
                    'newSheetName': new_sheet_name
                }
            }
        ]
    }
    
    # Execute the duplication request
    service.spreadsheets().batchUpdate(
        spreadsheetId=sheet_id,
        body=request_body
    ).execute()
    
    logger.info(f"Sheet '{source_sheet_name}' duplicated as '{new_sheet_name}'")
    return new_sheet_name
```

### 2. Command Line Argument for Iteration Mode

Add a command line argument to enable iteration mode:

```python
import argparse

def parse_arguments():
    parser = argparse.ArgumentParser(description='Run LLM processing on Google Sheets data')
    parser.add_argument('--iteration-mode', action='store_true', 
                        help='Enable iteration mode (duplicate sheet before updating)')
    parser.add_argument('--iteration-name', type=str, default=None,
                        help='Custom name for the iteration sheet (default: auto-generated with timestamp)')
    return parser.parse_args()

# In main function
def main():
    args = parse_arguments()
    
    # ... existing code ...
    
    # Define the sheet name to use
    sheet_name = "Sheet1"
    
    # If iteration mode is enabled, duplicate the sheet
    if args.iteration_mode:
        sheet_name = duplicate_sheet(sheet_id, sheet_name, args.iteration_name)
        logger.info(f"Iteration mode enabled. Working with duplicate sheet: {sheet_name}")
    
    # ... rest of the code using the potentially new sheet_name ...
```

### 3. Metadata Tracking for Iterations

Add metadata to track iterations:

```python
def add_iteration_metadata(sheet_id, sheet_name, metadata):
    """
    Add metadata about the iteration to the sheet.
    
    Args:
        sheet_id (str): The ID of the Google Sheet.
        sheet_name (str): The name of the sheet.
        metadata (dict): Dictionary containing metadata about the iteration.
    """
    creds = get_credentials()
    service = build('sheets', 'v4', credentials=creds)
    
    # Format metadata as a string
    metadata_str = "ITERATION METADATA:\n" + "\n".join([f"{k}: {v}" for k, v in metadata.items()])
    
    # Add metadata to cell A1 note
    request_body = {
        'requests': [
            {
                'updateCellMetadata': {
                    'fields': 'note',
                    'range': {
                        'sheetId': get_sheet_id_by_name(service, sheet_id, sheet_name),
                        'startRowIndex': 0,
                        'endRowIndex': 1,
                        'startColumnIndex': 0,
                        'endColumnIndex': 1
                    },
                    'cellMetadata': {
                        'note': metadata_str
                    }
                }
            }
        ]
    }
    
    service.spreadsheets().batchUpdate(
        spreadsheetId=sheet_id,
        body=request_body
    ).execute()
```

### 4. Integration with Main Processing Flow

Update the main function to use iteration mode:

```python
def main():
    args = parse_arguments()
    
    # ... existing code ...
    
    # Define the sheet name to use
    original_sheet_name = "Sheet1"
    sheet_name = original_sheet_name
    
    # If iteration mode is enabled, duplicate the sheet
    if args.iteration_mode:
        sheet_name = duplicate_sheet(sheet_id, original_sheet_name, args.iteration_name)
        logger.info(f"Iteration mode enabled. Working with duplicate sheet: {sheet_name}")
        
        # Add metadata about this iteration
        metadata = {
            "Original Sheet": original_sheet_name,
            "Timestamp": datetime.now().isoformat(),
            "Models Used": ", ".join(set(models)),
            "User": os.getenv("USER", "unknown")
        }
        add_iteration_metadata(sheet_id, sheet_name, metadata)
    
    # Get data from the appropriate sheet
    df = get_data_from_sheet(sheet_id, range_name=sheet_name)
    
    # ... rest of processing ...
    
    # Update the appropriate sheet
    update_to_sheet(update_df, prompt_output_cols, sheet_id, sheet_name)
    
    # If in iteration mode, provide a link to the new sheet
    if args.iteration_mode:
        sheet_url = f"https://docs.google.com/spreadsheets/d/{sheet_id}/edit#gid={get_sheet_id_by_name(None, sheet_id, sheet_name)}"
        logger.info(f"Iteration complete. Access the new sheet at: {sheet_url}")
```

### 5. Helper Function to Get Sheet ID by Name

```python
def get_sheet_id_by_name(service, spreadsheet_id, sheet_name):
    """Get the sheet ID for a given sheet name."""
    if service is None:
        creds = get_credentials()
        service = build('sheets', 'v4', credentials=creds)
        
    spreadsheet = service.spreadsheets().get(spreadsheetId=spreadsheet_id).execute()
    
    for sheet in spreadsheet['sheets']:
        if sheet['properties']['title'] == sheet_name:
            return sheet['properties']['sheetId']
    
    raise ValueError(f"Sheet '{sheet_name}' not found")
```

## Implementation Considerations

1. **Performance**: Sheet duplication can be slow for large sheets with complex formatting.

2. **API Quotas**: Be mindful of Google Sheets API quotas when performing multiple duplications.

3. **Naming Conventions**: Consider standardized naming conventions for iteration sheets to make them easily identifiable.

4. **Cleanup**: Provide options to clean up old iteration sheets to prevent spreadsheet clutter.

5. **Permissions**: Ensure the service account has sufficient permissions to create new sheets.

## Mermaid Diagram: Iteration Mode Flow

```mermaid
flowchart TD
    A[Start Script with<br>--iteration-mode] --> B[Parse Arguments]
    B --> C[Connect to Google Sheets]
    C --> D[Identify Source Sheet]
    D --> E[Generate New Sheet Name<br>with Timestamp]
    E --> F[Duplicate Sheet<br>with All Formatting]
    F --> G[Add Metadata to<br>New Sheet]
    G --> H[Process Data<br>as Normal]
    H --> I[Update Duplicate Sheet<br>Instead of Original]
    I --> J[Provide Link to<br>New Sheet]
    
    subgraph "Original Flow (No Iteration)"
    K[Start Script] --> L[Connect to Google Sheets]
    L --> M[Process Data]
    M --> N[Update Original Sheet]
    end
```

## Example Usage

```bash
# Run in normal mode (updates original sheet)
python main2.py

# Run in iteration mode (creates and updates a duplicate sheet)
python main2.py --iteration-mode

# Run in iteration mode with custom sheet name
python main2.py --iteration-mode --iteration-name "Experiment_A_v2"
```

## Conclusion

Implementing an Iteration Mode in the gsheet-model-breaker-demo would provide users with a valuable tool for experimentation and historical tracking. By duplicating sheets before making changes, users can preserve their original data while exploring different prompt configurations and model settings.

The proposed implementation leverages the Google Sheets API's sheet duplication capabilities and integrates seamlessly with the existing processing flow. With the addition of metadata tracking, users can easily identify and manage their iterations, creating a more robust workflow for LLM experimentation.
