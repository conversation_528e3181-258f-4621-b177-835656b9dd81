# Ensemble-vertical and ensemble-horizontal

Original feature description from features_list.md:
```
ensemble-vertical and ensemble-horizontal
```

## Feature Description

Ensemble methods in LLM applications involve combining multiple model outputs to produce more reliable, accurate, or diverse results. The gsheet-model-breaker-demo tool could implement two types of ensemble approaches:

1. **Ensemble-vertical**: Running the same prompt through multiple different models or configurations and combining their outputs.
2. **Ensemble-horizontal**: Running multiple variations of a prompt through the same model and combining the results.

These ensemble techniques can significantly improve the quality and reliability of LLM outputs by leveraging the strengths of different models or prompt strategies.

## Current Implementation Status

Currently, the gsheet-model-breaker-demo does not have a dedicated ensemble feature implementation. The code structure supports running individual prompts through specific models, but there's no built-in mechanism for running ensemble methods and aggregating results.

However, there are hints in the codebase that ensemble functionality was considered:

<augment_code_snippet path="experiments/src/tools/gsheet-model-breaker-demo/llm_processor/chain_step.py" mode="EXCERPT">
````python
# TODO: ENSEMBLE FLAG PARAMETER
"""
@dataclass
class EnsembleConfig:
    models: List[str] = field(default_factory=lambda: [LLM.gpt_4o_mini, LLM.claude_3_5_sonnet, LLM.gpt_3_5_turbo])
    temperatures: List[float] = field(default_factory=lambda: [0.2, 0.5, 0.8])
    scoring_model: str = LLM.gpt_4o_mini
    scoring_temperature: float = 0.1
    scoring_max_tokens: int = 10

We'd want a config class so we can use ensembling naively. Maybe a few core types set up.
Our chaining is essentially making a list of initialised ChainSteps, which we then execute in order.
Which col the ensembles cols were nested under would have to be specified in the col name. Maybe better way like a df that just describes the relationships betweeen cols.
Ideally, we would then have dependencies detected and parellisation optimized. Could potentially then even automate the ordering... just a set of steps with dependencies and it figures the rest out.
"""
````
</augment_code_snippet>

## Proposed Implementation

### 1. Ensemble Configuration

First, we need to define how ensemble configurations would be specified in the Google Sheet:

```
Row 1: Column references (e.g., "ensemble-v" or "ensemble-h" in addition to "py-llm")
Row 4: Prompt templates (same as current)
Row 5: Models (comma-separated list for vertical ensemble)
Row 6: Temperatures (comma-separated list for temperature variations)
Row 7: Max tokens (single value or comma-separated)
Row 8: Flags (including ensemble aggregation method: "best-of", "majority-vote", "average", etc.)
```

### 2. Ensemble-Vertical Implementation

Ensemble-vertical involves running the same prompt through multiple models or configurations:

```python
# New class for vertical ensemble
class VerticalEnsemble:
    def __init__(self, models, temperatures, max_tokens, aggregation_method="best-of"):
        self.models = models.split(",") if isinstance(models, str) else models
        self.temperatures = [float(t) for t in temperatures.split(",")] if isinstance(temperatures, str) else temperatures
        self.max_tokens = [int(t) for t in max_tokens.split(",")] if isinstance(max_tokens, str) else max_tokens
        self.aggregation_method = aggregation_method
        
        # Ensure all lists have the same length by repeating the last value
        max_len = max(len(self.models), len(self.temperatures), len(self.max_tokens))
        self.models = self._extend_list(self.models, max_len)
        self.temperatures = self._extend_list(self.temperatures, max_len)
        self.max_tokens = self._extend_list(self.max_tokens, max_len)
    
    def _extend_list(self, lst, target_len):
        if len(lst) < target_len:
            return lst + [lst[-1]] * (target_len - len(lst))
        return lst
    
    async def execute(self, prompt, llm_processor):
        """Run the prompt through multiple models and aggregate results"""
        results = []
        
        for i in range(len(self.models)):
            try:
                response = await llm_processor._async_get_response(
                    message=prompt,
                    temperature=self.temperatures[i],
                    max_tokens=self.max_tokens[i],
                    model=self.models[i]
                )
                results.append({
                    "model": self.models[i],
                    "temperature": self.temperatures[i],
                    "response": response,
                    "error": None
                })
            except Exception as e:
                results.append({
                    "model": self.models[i],
                    "temperature": self.temperatures[i],
                    "response": None,
                    "error": str(e)
                })
        
        # Aggregate results based on the specified method
        return self.aggregate_results(results)
    
    def aggregate_results(self, results):
        """Aggregate results using the specified method"""
        valid_results = [r["response"] for r in results if r["response"] is not None]
        
        if not valid_results:
            return "[ERROR] All ensemble attempts failed"
        
        if self.aggregation_method == "best-of":
            # Use a scoring model to rank responses
            # For now, just return the first valid result
            return valid_results[0]
        
        elif self.aggregation_method == "majority-vote":
            # Return the most common response
            from collections import Counter
            return Counter(valid_results).most_common(1)[0][0]
        
        elif self.aggregation_method == "all-responses":
            # Return all responses as a formatted string
            return "\n\n---\n\n".join([
                f"Model: {r['model']} (temp={r['temperature']})\n{r['response']}"
                for r in results if r["response"] is not None
            ])
        
        # Default to returning the first valid result
        return valid_results[0]
```

### 3. Ensemble-Horizontal Implementation

Ensemble-horizontal involves running multiple variations of a prompt through the same model:

```python
class HorizontalEnsemble:
    def __init__(self, prompt_variations, model, temperature, max_tokens, aggregation_method="best-of"):
        self.prompt_variations = prompt_variations
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.aggregation_method = aggregation_method
    
    async def execute(self, base_prompt, llm_processor):
        """Run multiple prompt variations through the same model"""
        results = []
        
        # Generate prompt variations if they're not explicitly provided
        if not self.prompt_variations:
            # Use an LLM to generate variations of the base prompt
            variations_prompt = f"""
            I need multiple variations of the following prompt to get diverse responses:
            
            {base_prompt}
            
            Generate 3 variations that ask for the same information but with different wording or emphasis.
            Format each variation as a numbered list item.
            """
            
            try:
                variations_text = await llm_processor._async_get_response(
                    message=variations_prompt,
                    temperature=0.7,
                    max_tokens=1000,
                    model=self.model
                )
                
                # Extract variations from the response
                import re
                variations = re.findall(r"\d+\.\s+(.*?)(?=\d+\.|$)", variations_text, re.DOTALL)
                variations = [v.strip() for v in variations]
                
                # Add the original prompt as the first variation
                self.prompt_variations = [base_prompt] + variations
            except Exception:
                # If generation fails, just use the original prompt
                self.prompt_variations = [base_prompt]
        
        # Execute each prompt variation
        for prompt in self.prompt_variations:
            try:
                response = await llm_processor._async_get_response(
                    message=prompt,
                    temperature=self.temperature,
                    max_tokens=self.max_tokens,
                    model=self.model
                )
                results.append({
                    "prompt": prompt,
                    "response": response,
                    "error": None
                })
            except Exception as e:
                results.append({
                    "prompt": prompt,
                    "response": None,
                    "error": str(e)
                })
        
        # Aggregate results
        return self.aggregate_results(results)
    
    def aggregate_results(self, results):
        """Aggregate results using the specified method"""
        valid_results = [r["response"] for r in results if r["response"] is not None]
        
        if not valid_results:
            return "[ERROR] All ensemble attempts failed"
        
        if self.aggregation_method == "best-of":
            # For now, just return the first valid result
            return valid_results[0]
        
        elif self.aggregation_method == "all-responses":
            # Return all responses with their prompts
            return "\n\n---\n\n".join([
                f"Prompt: {r['prompt'][:100]}...\n\nResponse: {r['response']}"
                for r in results if r["response"] is not None
            ])
        
        # Default to returning the first valid result
        return valid_results[0]
```

### 4. Integration with Main Processing Flow

To integrate these ensemble methods into the main processing flow:

```python
# In main2.py, modify the column processing loop

for idx, (prompt_template, model, temperature, max_tokens, flags, output_col) in enumerate(zip(
    prompt_templates, models, temperatures, max_output_tokens, flags, prompt_output_cols
)):
    # Check if this column uses ensemble methods
    is_vertical_ensemble = "ensemble-v" in flags.lower()
    is_horizontal_ensemble = "ensemble-h" in flags.lower()
    
    # Extract aggregation method if specified
    aggregation_method = "best-of"  # default
    for flag in flags.lower().split():
        if flag.startswith("agg="):
            aggregation_method = flag.split("=")[1]
    
    # Create filled_template column
    template_col = f"filled_template_{output_col}"
    current_input_cols = prompt_input_cols.copy()
    
    # Add previously processed output columns as potential input variables
    for prev_idx in range(idx):
        prev_col = prompt_output_cols[prev_idx]
        if prev_col not in current_input_cols and prev_col in prompt_inputs.columns:
            current_input_cols.append(prev_col)
    
    # Fill templates with available variables
    prompt_inputs[template_col] = prompt_inputs.apply(
        lambda row: fill_template(row, prompt_template, current_input_cols),
        axis=1,
    )
    
    # Find rows with "<generate>" tag
    generate_mask = (prompt_inputs[f"original_{output_col}"] == "<generate>") | (prompt_inputs[f"original_{output_col}"] == "//")
    
    if not generate_mask.any():
        logger.info(f"No rows with '<generate>' tag found for {output_col}. Skipping.")
        continue
    
    generate_indices = generate_mask[generate_mask].index.tolist()
    chain_input = prompt_inputs.loc[generate_indices].copy()
    
    if is_vertical_ensemble:
        # Process with vertical ensemble
        ensemble = VerticalEnsemble(model, temperature, max_tokens, aggregation_method)
        
        # Process each row with the ensemble
        for idx_in_result, row in chain_input.iterrows():
            prompt = row[template_col]
            result = await ensemble.execute(prompt, llm_proc)
            
            # Store the result
            chain_input.at[idx_in_result, output_col] = result
            
    elif is_horizontal_ensemble:
        # Process with horizontal ensemble
        ensemble = HorizontalEnsemble([], model, temperature, max_tokens, aggregation_method)
        
        # Process each row with the ensemble
        for idx_in_result, row in chain_input.iterrows():
            prompt = row[template_col]
            result = await ensemble.execute(prompt, llm_proc)
            
            # Store the result
            chain_input.at[idx_in_result, output_col] = result
            
    else:
        # Process normally with ChainStep
        # (existing code)
```

### 5. Advanced Aggregation Methods

For more sophisticated ensemble aggregation, we could implement:

```python
async def best_of_aggregation(responses, llm_processor):
    """Use an LLM to select the best response from multiple options"""
    if not responses:
        return "[ERROR] No valid responses to aggregate"
    
    scoring_prompt = f"""
    I have received multiple responses to a prompt. Please analyze them and select the best one.
    
    {"\n\n".join([f"RESPONSE {i+1}:\n{r}" for i, r in enumerate(responses)])}
    
    Analyze each response for accuracy, completeness, and quality. Then select the best response by indicating its number (e.g., "The best response is Response 2").
    """
    
    try:
        scoring_result = await llm_processor._async_get_response(
            message=scoring_prompt,
            temperature=0.1,
            max_tokens=500,
            model="gpt-4o-mini"  # Use a reliable model for scoring
        )
        
        # Extract the chosen response number
        import re
        match = re.search(r"Response\s+(\d+)", scoring_result)
        if match:
            chosen_idx = int(match.group(1)) - 1
            if 0 <= chosen_idx < len(responses):
                return responses[chosen_idx]
        
        # If extraction fails, return the scoring result itself
        return scoring_result
    except Exception:
        # If scoring fails, return the first response
        return responses[0]

async def disagreement_analysis(responses, llm_processor):
    """Analyze areas of agreement and disagreement between responses"""
    if len(responses) < 2:
        return responses[0] if responses else "[ERROR] No valid responses to analyze"
    
    analysis_prompt = f"""
    I have received multiple responses to the same prompt. Please analyze the areas of agreement and disagreement.
    
    {"\n\n".join([f"RESPONSE {i+1}:\n{r}" for i, r in enumerate(responses)])}
    
    Provide a summary that:
    1. Identifies key points where all responses agree
    2. Highlights significant areas of disagreement
    3. Synthesizes a final response that incorporates the most reliable information
    """
    
    try:
        return await llm_processor._async_get_response(
            message=analysis_prompt,
            temperature=0.2,
            max_tokens=1000,
            model="gpt-4o"  # Use a capable model for complex analysis
        )
    except Exception:
        # If analysis fails, concatenate the responses
        return "\n\n---\n\n".join(responses)
```

## Implementation Considerations

1. **Performance Impact**: Ensemble methods significantly increase the number of API calls, which affects both cost and processing time.

2. **Configuration Complexity**: The sheet configuration needs to be intuitive while supporting complex ensemble setups.

3. **Error Handling**: Need robust handling for cases where some ensemble members fail.

4. **Result Formatting**: The aggregated results should be clearly formatted, especially for methods that return multiple responses.

5. **Dependency Management**: Ensemble processing should respect the dependency chain between columns.

## Mermaid Diagram: Ensemble Processing Flow

```mermaid
flowchart TD
    A[Input Row] --> B{Ensemble Type?}
    B -->|Vertical| C[Split into Multiple Models]
    B -->|Horizontal| D[Generate Prompt Variations]
    B -->|None| E[Standard Processing]
    
    C --> F[Execute Same Prompt<br>with Different Models]
    D --> G[Execute Different Prompts<br>with Same Model]
    
    F --> H{Aggregation Method}
    G --> H
    
    H -->|Best-of-N| I[LLM Scoring<br>to Select Best]
    H -->|Majority Vote| J[Select Most<br>Common Response]
    H -->|All Responses| K[Format All<br>Responses Together]
    H -->|Disagreement Analysis| L[Analyze Agreement<br>and Disagreement]
    
    I --> M[Final Result]
    J --> M
    K --> M
    L --> M
    E --> M
```

## Example Sheet Configuration

Here's how the sheet configuration might look for ensemble methods:

| Column A | Column B | Column C | Column D |
|----------|----------|----------|----------|
| col_ref | inputs | py-llm<br>ensemble-v | py-llm<br>ensemble-h |
| ... | ... | ... | ... |
| Prompt Template | Input data | Generate a summary | Analyze the data |
| Model | N/A | gpt-4o-mini,claude-3-sonnet,gpt-3.5-turbo | gpt-4o |
| Temperature | N/A | 0.2,0.5,0.7 | 0.3 |
| Max Tokens | N/A | 500 | 800 |
| Flags | N/A | agg=best-of | agg=disagreement-analysis |

## Conclusion

Implementing ensemble methods in the gsheet-model-breaker-demo would significantly enhance its capabilities for producing high-quality, reliable outputs. The vertical ensemble approach leverages the strengths of different models, while the horizontal ensemble approach explores different prompt formulations to get more comprehensive results.

The proposed implementation provides a flexible framework that can be extended with additional aggregation methods and ensemble strategies. By integrating these features into the existing processing flow, users would gain powerful new tools for improving LLM outputs while maintaining the simplicity of the spreadsheet interface.
