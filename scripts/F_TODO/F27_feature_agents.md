# Agents

Original feature description from features_list.md:
```
Agents
```

## Feature Description

The "Agents" feature would extend the gsheet-model-breaker-demo tool to support LLM agents - autonomous or semi-autonomous systems that can perform complex tasks by breaking them down into steps, using tools, and making decisions based on intermediate results. Unlike simple prompt-response patterns, agents can:

1. **Execute Multi-Step Reasoning**: Break down complex tasks into logical steps
2. **Use Tools**: Access external tools and APIs to gather information or perform actions
3. **Maintain State**: Remember previous steps and use that information in subsequent steps
4. **Make Decisions**: Choose different paths based on intermediate results
5. **Self-Correct**: Identify and fix errors in their own reasoning

Implementing agents in the gsheet-model-breaker-demo would allow users to create more sophisticated workflows directly from their spreadsheets.

## Current Implementation Status

Currently, the gsheet-model-breaker-demo does not support agent functionality. The existing implementation focuses on direct prompt-response patterns:

<augment_code_snippet path="experiments/src/tools/gsheet-model-breaker-demo/scripts/main2.py" mode="EXCERPT">
````python
# Process data
llm_proc = ParallelLLMDataFrameProcessor(
    def_model=model,
    def_temperature=temperature,
    def_max_tokens=max_tokens,
    def_async_rate_limit=10,
    def_thread_rate_limit=5,
)

# Set the template column as the source for the prompt
chain = [
    ChainStep(
        pt=template_col,  # Use the template column as source
        model=model,
        temperature=temperature,
        max_tokens=max_tokens,
        col=output_col,
    ),
]

start_time = time.time()
result_df = llm_proc.execute_chain(
    chain_input,
    chain,
    max_attempts=3,
)
````
</augment_code_snippet>

The current ChainStep class is designed for simple prompt-response patterns and doesn't support the more complex interactions required for agents:

<augment_code_snippet path="experiments/src/tools/gsheet-model-breaker-demo/llm_processor/chain_step.py" mode="EXCERPT">
````python
class ChainStep(NamedTuple):
    """
    Represents a single step in an LLM processing chain.

    This class defines the structure and parameters for each step in the chain,
    including the prompt template, model settings, and output specifications.

    Attributes:
        pt (str): The prompt template for this step.
        mapping (Optional[Dict[str, str]]): A mapping of prompt keywords to column names.
        temperature (Optional[float]): The temperature setting for the LLM model.
        max_tokens (Optional[int]): The maximum number of tokens for the LLM response.
        model (Optional[str]): The name of the LLM model to use.
        col (str): The name of the column to store the response.
        fanout (bool): Whether to fan out the response as a list.
        overwrite (bool): Whether to overwrite existing data in the response column.
        validators (List[LLMValidator]): A list of LLM validators to apply to the response.
    """

    pt: str  # prompt template
    mapping: Optional[Dict[str, str]] = None  # prompt keyword mapping
    temperature: Optional[float] = None  # model temperature
    max_tokens: Optional[int] = None  # model max_tokens
    model: Optional[str] = None  # model name
    col: str = "response"  # response column name
    fanout: bool = False  # fanout response column as a list TODO: ensure all cols's new rows are dropped down. TODO: how to un-fan?
    unfan_col: Optional[str] = (
        None  # if fanout, specify the column to un-fan #TODO - THIS IS NOT FUNCTIONAL, SEE NOTE BELOW
    )
    overwrite: bool = False  # overwrite existing response column
    validators: List = None  # LLM validators to apply to the response
````
</augment_code_snippet>

## Proposed Implementation

### 1. Agent Configuration in Sheets

Define how agents would be configured in the Google Sheet:

```
Row 1: Column references (e.g., "py-agent" instead of "py-llm")
Row 2: Agent goal/objective
Row 3: Available tools (comma-separated list)
Row 4: System prompt / agent instructions
Row 5: Models (same as current)
Row 6: Temperatures (same as current)
Row 7: Max tokens (same as current)
Row 8: Flags (including agent-specific flags like "max_iterations=5")
```

### 2. Agent Class Definition

Create a new class to represent an agent:

```python
from typing import Dict, List, Optional, Any, Callable
import json
import re
import asyncio

class Tool:
    """Represents a tool that an agent can use."""
    
    def __init__(self, name: str, description: str, function: Callable):
        """
        Initialize a tool.
        
        Args:
            name: The name of the tool
            description: A description of what the tool does
            function: The function to call when the tool is used
        """
        self.name = name
        self.description = description
        self.function = function
    
    async def execute(self, **kwargs) -> str:
        """Execute the tool with the given arguments."""
        try:
            result = self.function(**kwargs)
            if asyncio.iscoroutine(result):
                result = await result
            return str(result)
        except Exception as e:
            return f"Error executing tool {self.name}: {str(e)}"

class AgentStep(NamedTuple):
    """Represents a single step in an agent's execution."""
    
    thought: str
    tool: Optional[str] = None
    tool_input: Optional[Dict[str, Any]] = None
    observation: Optional[str] = None
    
    def to_string(self) -> str:
        """Convert the step to a string representation."""
        result = f"Thought: {self.thought}\n"
        if self.tool:
            result += f"Tool: {self.tool}\n"
            result += f"Tool Input: {json.dumps(self.tool_input)}\n"
        if self.observation:
            result += f"Observation: {self.observation}\n"
        return result

class Agent:
    """Represents an LLM agent that can use tools to accomplish tasks."""
    
    def __init__(self, 
                 system_prompt: str,
                 goal: str,
                 tools: List[Tool],
                 model: str = "gpt-4o",
                 temperature: float = 0.2,
                 max_tokens: int = 1000,
                 max_iterations: int = 5):
        """
        Initialize an agent.
        
        Args:
            system_prompt: The system prompt for the agent
            goal: The goal or objective for the agent
            tools: List of tools available to the agent
            model: The LLM model to use
            temperature: The temperature setting
            max_tokens: The maximum tokens for responses
            max_iterations: The maximum number of iterations
        """
        self.system_prompt = system_prompt
        self.goal = goal
        self.tools = {tool.name: tool for tool in tools}
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.max_iterations = max_iterations
        self.steps = []
    
    def _build_prompt(self, user_input: str) -> List[Dict[str, str]]:
        """Build the prompt for the agent."""
        # Create the system prompt with tool descriptions
        tool_descriptions = "\n".join([
            f"- {name}: {tool.description}" 
            for name, tool in self.tools.items()
        ])
        
        full_system_prompt = f"""
        {self.system_prompt}
        
        Your goal is: {self.goal}
        
        You have access to the following tools:
        {tool_descriptions}
        
        To use a tool, respond with:
        Thought: <your reasoning>
        Tool: <tool_name>
        Tool Input: <tool_input_json>
        
        After receiving the tool's response, you will get an observation.
        You can then continue with another thought, tool use, or provide your final answer.
        
        If you have the information to directly answer without using tools, or after you've gathered enough information, respond with:
        Thought: <your reasoning>
        Answer: <your final answer>
        """
        
        # Build the conversation history
        messages = [
            {"role": "system", "content": full_system_prompt}
        ]
        
        # Add the initial user input
        messages.append({"role": "user", "content": user_input})
        
        # Add previous steps if any
        for i, step in enumerate(self.steps):
            messages.append({"role": "assistant", "content": f"Thought: {step.thought}\nTool: {step.tool}\nTool Input: {json.dumps(step.tool_input)}"})
            if step.observation:
                messages.append({"role": "user", "content": f"Observation: {step.observation}"})
        
        return messages
    
    def _parse_response(self, response: str) -> Dict:
        """Parse the LLM response into structured components."""
        thought_match = re.search(r"Thought:\s*(.*?)(?=Tool:|Answer:|$)", response, re.DOTALL)
        tool_match = re.search(r"Tool:\s*(.*?)(?=Tool Input:|$)", response, re.DOTALL)
        tool_input_match = re.search(r"Tool Input:\s*(.*?)(?=Observation:|$)", response, re.DOTALL)
        answer_match = re.search(r"Answer:\s*(.*?)$", response, re.DOTALL)
        
        result = {
            "thought": thought_match.group(1).strip() if thought_match else "",
            "tool": tool_match.group(1).strip() if tool_match else None,
            "tool_input": None,
            "answer": answer_match.group(1).strip() if answer_match else None
        }
        
        # Parse tool input as JSON if present
        if tool_input_match and result["tool"]:
            try:
                tool_input_str = tool_input_match.group(1).strip()
                result["tool_input"] = json.loads(tool_input_str)
            except json.JSONDecodeError:
                result["tool_input"] = {"text": tool_input_str}
        
        return result
    
    async def execute(self, user_input: str) -> str:
        """
        Execute the agent on the given input.
        
        Args:
            user_input: The user's input to the agent
            
        Returns:
            The agent's final answer
        """
        self.steps = []
        iteration = 0
        
        while iteration < self.max_iterations:
            # Build the prompt
            messages = self._build_prompt(user_input)
            
            # Get response from LLM
            try:
                response = await litellm.acompletion(
                    model=self.model,
                    messages=messages,
                    temperature=self.temperature,
                    max_tokens=self.max_tokens
                )
                response_text = response["choices"][0]["message"]["content"]
            except Exception as e:
                return f"Error calling LLM: {str(e)}"
            
            # Parse the response
            parsed = self._parse_response(response_text)
            
            # If we have a final answer, return it
            if parsed["answer"]:
                self.steps.append(AgentStep(thought=parsed["thought"]))
                return parsed["answer"]
            
            # If we need to use a tool
            if parsed["tool"] and parsed["tool"] in self.tools:
                # Create a step without the observation yet
                step = AgentStep(
                    thought=parsed["thought"],
                    tool=parsed["tool"],
                    tool_input=parsed["tool_input"]
                )
                
                # Execute the tool
                try:
                    tool = self.tools[parsed["tool"]]
                    observation = await tool.execute(**(parsed["tool_input"] or {}))
                    
                    # Update the step with the observation
                    step = step._replace(observation=observation)
                    self.steps.append(step)
                except Exception as e:
                    observation = f"Error executing tool: {str(e)}"
                    step = step._replace(observation=observation)
                    self.steps.append(step)
            else:
                # Invalid tool or no tool specified
                observation = "No valid tool specified. Please use one of the available tools or provide a final answer."
                step = AgentStep(
                    thought=parsed["thought"],
                    tool=parsed["tool"],
                    tool_input=parsed["tool_input"],
                    observation=observation
                )
                self.steps.append(step)
            
            iteration += 1
        
        # If we've reached the maximum iterations, return a timeout message
        return f"Agent reached maximum iterations ({self.max_iterations}) without finding an answer. Last thought: {self.steps[-1].thought}"
    
    def get_execution_trace(self) -> str:
        """Get a formatted trace of the agent's execution."""
        trace = f"Goal: {self.goal}\n\n"
        for i, step in enumerate(self.steps):
            trace += f"Step {i+1}:\n{step.to_string()}\n"
        return trace
```

### 3. Tool Implementations

Implement a set of basic tools for agents to use:

```python
# Basic tool implementations
async def search_web(query: str) -> str:
    """Search the web for information."""
    # This would integrate with a search API
    return f"Results for query: {query}\n1. Sample result 1\n2. Sample result 2"

async def calculate(expression: str) -> str:
    """Evaluate a mathematical expression."""
    try:
        # Use a safe evaluation method
        from sympy import sympify
        result = sympify(expression)
        return f"Result: {result}"
    except Exception as e:
        return f"Error calculating: {str(e)}"

async def get_current_date() -> str:
    """Get the current date and time."""
    from datetime import datetime
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

async def fetch_spreadsheet_data(sheet_name: str, range: str) -> str:
    """Fetch data from another sheet in the spreadsheet."""
    # This would integrate with the Google Sheets API
    return f"Data from {sheet_name} range {range}:\nSample data 1\nSample data 2"

# Create tool instances
default_tools = [
    Tool("search", "Search the web for information", search_web),
    Tool("calculate", "Evaluate a mathematical expression", calculate),
    Tool("date", "Get the current date and time", get_current_date),
    Tool("fetch_sheet", "Fetch data from another sheet", fetch_spreadsheet_data)
]
```

### 4. Integration with Main Processing Flow

Update the main2.py file to support agent execution:

```python
# In main2.py, modify the column processing loop

for idx, (prompt_template, model, temperature, max_tokens, flags, output_col) in enumerate(zip(
    prompt_templates, models, temperatures, max_output_tokens, flags, prompt_output_cols
)):
    # Check if this column uses agent mode
    is_agent = "py-agent" in df.iloc[0][output_col].lower()
    
    if is_agent:
        # Extract agent configuration
        agent_goal = df.iloc[1][output_col]
        agent_tools_str = df.iloc[2][output_col]
        agent_system_prompt = prompt_template  # Row 4 contains the system prompt
        
        # Parse agent-specific flags
        max_iterations = 5  # Default
        for flag in flags.lower().split():
            if flag.startswith("max_iterations="):
                try:
                    max_iterations = int(flag.split("=")[1])
                except ValueError:
                    pass
        
        # Parse tool list
        requested_tools = [t.strip() for t in agent_tools_str.split(",")]
        tools = [tool for tool in default_tools if tool.name in requested_tools]
        
        # Create the agent
        agent = Agent(
            system_prompt=agent_system_prompt,
            goal=agent_goal,
            tools=tools,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            max_iterations=max_iterations
        )
        
        # Find rows with "<generate>" tag
        generate_mask = (prompt_inputs[f"original_{output_col}"] == "<generate>") | (prompt_inputs[f"original_{output_col}"] == "//")
        
        if not generate_mask.any():
            logger.info(f"No rows with '<generate>' tag found for {output_col}. Skipping.")
            continue
        
        generate_indices = generate_mask[generate_mask].index.tolist()
        chain_input = prompt_inputs.loc[generate_indices].copy()
        
        # Process each row with the agent
        for idx_in_result, row in chain_input.iterrows():
            # Prepare user input by combining all input columns
            user_input = "\n".join([f"{col}: {row[col]}" for col in prompt_input_cols if col in row and pd.notna(row[col])])
            
            # Execute the agent
            try:
                result = await agent.execute(user_input)
                
                # Store the result
                chain_input.at[idx_in_result, output_col] = result
                
                # Also store the execution trace in a separate column if requested
                if "store_trace" in flags.lower():
                    trace_col = f"{output_col}_trace"
                    if trace_col not in chain_input.columns:
                        chain_input[trace_col] = ""
                    chain_input.at[idx_in_result, trace_col] = agent.get_execution_trace()
            except Exception as e:
                logger.error(f"Error executing agent for row {idx_in_result}: {str(e)}")
                chain_input.at[idx_in_result, output_col] = f"[ERROR] Agent execution failed: {str(e)}"
        
        # Update the original DataFrame with the results
        for idx_in_result in chain_input.index:
            prompt_inputs.at[idx_in_result, output_col] = chain_input.at[idx_in_result, output_col]
            if "store_trace" in flags.lower() and f"{output_col}_trace" in chain_input.columns:
                if f"{output_col}_trace" not in prompt_inputs.columns:
                    prompt_inputs[f"{output_col}_trace"] = ""
                prompt_inputs.at[idx_in_result, f"{output_col}_trace"] = chain_input.at[idx_in_result, f"{output_col}_trace"]
    else:
        # Process normally with ChainStep
        # (existing code)
```

### 5. Custom Tool Definition in Sheets

Allow users to define custom tools directly in the sheet:

```python
# Add a new hidden sheet for tool definitions
def get_custom_tools(sheet_id):
    """Get custom tool definitions from the _tools sheet."""
    try:
        tools_df = get_data_from_sheet(sheet_id, range_name="_tools")
        custom_tools = []
        
        for _, row in tools_df.iterrows():
            if pd.isna(row.get("name")) or pd.isna(row.get("description")) or pd.isna(row.get("code")):
                continue
                
            tool_name = row["name"]
            tool_description = row["description"]
            tool_code = row["code"]
            
            # Create a function from the code
            try:
                # Create a safe namespace
                namespace = {"asyncio": asyncio, "json": json, "re": re}
                
                # Execute the code in the namespace
                exec(tool_code, namespace)
                
                # Get the function
                if "tool_function" in namespace:
                    custom_tools.append(Tool(
                        name=tool_name,
                        description=tool_description,
                        function=namespace["tool_function"]
                    ))
            except Exception as e:
                logger.error(f"Error creating custom tool {tool_name}: {str(e)}")
        
        return custom_tools
    except Exception:
        # If the _tools sheet doesn't exist or can't be read
        return []
```

## Implementation Considerations

1. **Security**: Executing custom tool code requires careful security considerations to prevent malicious code execution.

2. **Performance**: Agent execution involves multiple LLM calls, which can be time-consuming and expensive.

3. **Error Handling**: Robust error handling is essential for agents, as they involve more complex interactions.

4. **Tool Limitations**: Consider what tools should be available and how to restrict access to sensitive operations.

5. **Debugging**: Provide detailed execution traces to help users debug agent behavior.

## Mermaid Diagram: Agent Execution Flow

```mermaid
flowchart TD
    A[User Input] --> B[Initialize Agent]
    B --> C[Build Prompt]
    C --> D[Call LLM]
    D --> E[Parse Response]
    
    E --> F{Final Answer?}
    F -->|Yes| G[Return Answer]
    
    F -->|No| H{Valid Tool?}
    H -->|Yes| I[Execute Tool]
    H -->|No| J[Error Message]
    
    I --> K[Record Observation]
    J --> K
    
    K --> L{Max Iterations<br>Reached?}
    L -->|No| C
    L -->|Yes| M[Return Timeout<br>Message]
    
    subgraph "Agent Configuration"
    N[System Prompt]
    O[Goal]
    P[Available Tools]
    Q[Model Settings]
    end
    
    N --> B
    O --> B
    P --> B
    Q --> B
```

## Example Sheet Configuration for Agents

Here's how the sheet configuration might look for an agent:

| Column A | Column B | Column C |
|----------|----------|----------|
| col_ref | inputs | py-agent |
| | | Research and summarize information about climate change |
| | | search,date,calculate |
| Prompt Template | Input data | You are a research assistant that helps find and summarize information. |
| Model | N/A | gpt-4o |
| Temperature | N/A | 0.2 |
| Max Tokens | N/A | 1000 |
| Flags | N/A | max_iterations=8 store_trace |

## Example Custom Tool Definition

In the `_tools` sheet:

| name | description | code |
|------|-------------|------|
| translate | Translate text to another language | ```python\nasync def tool_function(text, target_language):\n    # This would integrate with a translation API\n    return f"Translated '{text}' to {target_language}: [Sample translation]"\n``` |

## Conclusion

Implementing agent functionality in the gsheet-model-breaker-demo would significantly expand its capabilities, allowing users to create more sophisticated and autonomous workflows directly from their spreadsheets. By leveraging the power of LLM agents with access to tools, users could automate complex tasks that require multi-step reasoning and external information.

The proposed implementation provides a flexible framework for defining and executing agents, with support for both built-in and custom tools. It integrates seamlessly with the existing sheet structure while adding the necessary configuration options for agent-specific settings.

This feature would position the gsheet-model-breaker-demo as a powerful platform for not just simple prompt-response interactions, but for complex, tool-using agent workflows that can solve real-world problems.
