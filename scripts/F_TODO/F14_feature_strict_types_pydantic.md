# Feature: Strict Types with Pydantic

Original requirement:
> StrictTypes -> how to handle pydantic definitions.

## Implementation Status: Not Implemented

The current implementation in `main2.py` and related files does not include support for strict type validation using Pydantic. The code treats all LLM outputs as strings without validating them against defined schemas or types.

## Feature Description

This feature would add support for strict type validation of LLM outputs using Pydantic models. Pydantic is a data validation and settings management library that uses Python type annotations to validate data structures. This would ensure that LLM outputs conform to expected schemas and types, making it easier to work with structured data and catch errors early.

Key aspects of this feature include:
1. Defining Pydantic models for expected output structures
2. Validating LLM responses against these models
3. Handling validation errors gracefully
4. Providing access to both raw and validated data

## Implementation Recommendation

### 1. Add Pydantic Dependency

First, ensure Pydantic is installed:

```python
# Add to requirements.txt or setup.py
# pydantic>=2.0.0
```

### 2. Create Base Pydantic Model Handler

```python
import json
from typing import Any, Dict, List, Optional, Type, TypeVar, Generic, Union
from pydantic import BaseModel, ValidationError

T = TypeVar('T', bound=BaseModel)

class PydanticHandler(Generic[T]):
    """
    Handler for validating and managing data with Pydantic models.
    """
    
    def __init__(self, model_class: Type[T]):
        """
        Initialize with a Pydantic model class.
        
        Args:
            model_class: Pydantic model class to use for validation
        """
        self.model_class = model_class
    
    def validate(self, data: Union[str, Dict, List]) -> tuple[Optional[T], Optional[str]]:
        """
        Validate data against the Pydantic model.
        
        Args:
            data: Data to validate (string, dict, or list)
            
        Returns:
            Tuple of (validated_model, error_message)
        """
        # If data is a string, try to parse it as JSON
        if isinstance(data, str):
            try:
                data = json.loads(data)
            except json.JSONDecodeError as e:
                return None, f"JSON parsing error: {str(e)}"
        
        # Validate against Pydantic model
        try:
            model = self.model_class.model_validate(data)
            return model, None
        except ValidationError as e:
            return None, f"Validation error: {str(e)}"
    
    def get_model_schema(self) -> Dict[str, Any]:
        """
        Get the JSON schema for the Pydantic model.
        
        Returns:
            JSON schema as a dictionary
        """
        return self.model_class.model_json_schema()
    
    def get_model_schema_json(self) -> str:
        """
        Get the JSON schema for the Pydantic model as a JSON string.
        
        Returns:
            JSON schema as a string
        """
        return json.dumps(self.get_model_schema(), indent=2)
    
    def get_example_instance(self) -> Dict[str, Any]:
        """
        Generate an example instance of the model with default values.
        
        Returns:
            Example instance as a dictionary
        """
        # Create an instance with default values where possible
        try:
            instance = self.model_class()
            return instance.model_dump()
        except Exception:
            # If we can't create an instance with defaults, return an empty dict
            return {}
```

### 3. Add Pydantic Model Registry

```python
class PydanticModelRegistry:
    """
    Registry for Pydantic models used in the application.
    """
    
    _models: Dict[str, Type[BaseModel]] = {}
    _handlers: Dict[str, PydanticHandler] = {}
    
    @classmethod
    def register_model(cls, name: str, model_class: Type[BaseModel]) -> None:
        """
        Register a Pydantic model with a name.
        
        Args:
            name: Name to register the model under
            model_class: Pydantic model class
        """
        cls._models[name] = model_class
        cls._handlers[name] = PydanticHandler(model_class)
    
    @classmethod
    def get_model_class(cls, name: str) -> Optional[Type[BaseModel]]:
        """
        Get a registered model class by name.
        
        Args:
            name: Name of the registered model
            
        Returns:
            Pydantic model class or None if not found
        """
        return cls._models.get(name)
    
    @classmethod
    def get_handler(cls, name: str) -> Optional[PydanticHandler]:
        """
        Get a handler for a registered model by name.
        
        Args:
            name: Name of the registered model
            
        Returns:
            PydanticHandler or None if not found
        """
        return cls._handlers.get(name)
    
    @classmethod
    def list_models(cls) -> List[str]:
        """
        List all registered model names.
        
        Returns:
            List of model names
        """
        return list(cls._models.keys())
    
    @classmethod
    def get_schema(cls, name: str) -> Optional[Dict[str, Any]]:
        """
        Get the JSON schema for a registered model.
        
        Args:
            name: Name of the registered model
            
        Returns:
            JSON schema as a dictionary or None if not found
        """
        handler = cls.get_handler(name)
        if handler:
            return handler.get_model_schema()
        return None
```

### 4. Define Example Pydantic Models

```python
from pydantic import BaseModel, Field
from typing import List, Optional

class Person(BaseModel):
    """Model for a person."""
    name: str = Field(..., description="The person's name")
    age: int = Field(..., description="The person's age")
    email: Optional[str] = Field(None, description="The person's email address")
    
class Address(BaseModel):
    """Model for an address."""
    street: str = Field(..., description="Street address")
    city: str = Field(..., description="City")
    state: str = Field(..., description="State or province")
    zip_code: str = Field(..., description="ZIP or postal code")
    
class Customer(BaseModel):
    """Model for a customer."""
    id: str = Field(..., description="Customer ID")
    person: Person = Field(..., description="Personal information")
    addresses: List[Address] = Field(default_factory=list, description="List of addresses")
    notes: Optional[str] = Field(None, description="Additional notes")

# Register models
PydanticModelRegistry.register_model("Person", Person)
PydanticModelRegistry.register_model("Address", Address)
PydanticModelRegistry.register_model("Customer", Customer)
```

### 5. Add Pydantic Validation to ChainStep

Update the `ChainStep` class to include Pydantic model validation:

```python
class ChainStep(NamedTuple):
    """
    Represents a single step in an LLM processing chain.
    """
    pt: str  # prompt template
    mapping: Optional[Dict[str, str]] = None  # prompt keyword mapping
    temperature: Optional[float] = None  # model temperature
    max_tokens: Optional[int] = None  # model max_tokens
    model: Optional[str] = None  # model name
    col: str = "response"  # response column name
    fanout: bool = False  # fanout response column as a list
    unfan_col: Optional[str] = None  # if fanout, specify the column to un-fan
    overwrite: bool = False  # overwrite existing response column
    validators: List = None  # LLM validators to apply to the response
    pydantic_model: Optional[str] = None  # Name of Pydantic model for validation
    strict_validation: bool = False  # Whether to fail on validation errors
```

### 6. Modify LLM Processor to Use Pydantic Validation

Update the `_process_chain_step` method in `processor.py` to use Pydantic validation:

```python
async def _process_chain_step(
    self,
    c_row: pd.Series,
    step: ChainStep,
    mapping: dict,
    max_attempts: int,
) -> None:
    """
    Process a single chain step for a given row.
    
    Args:
        c_row (pd.Series): The current row of the DataFrame.
        step (ChainStep): The chain step to process.
        mapping (dict): The mapping of prompt keywords to column names.
        max_attempts (int): The maximum number of retry attempts for failed API calls.
    """
    prompt = c_row.get(step.pt)
    if c_row.get(step.col) and not step.overwrite:
        logger.debug(f"{step.col} already exists (skip)")
        return
    for k, v in mapping.items():
        prompt = prompt.replace(f"{{{k}}}", str(c_row.get(v, "N/A")))

    temperature = (
        c_row.get("__temperature")
        or step.temperature
        or self.def_temperature
    )
    max_tokens = (
        c_row.get("__max_tokens") or step.max_tokens or self.def_max_tokens
    )
    model = c_row.get("__model") or step.model or self.def_model

    # Add Pydantic model information to the prompt if specified
    if step.pydantic_model:
        handler = PydanticModelRegistry.get_handler(step.pydantic_model)
        if handler:
            # Add schema information to the prompt
            schema_json = handler.get_model_schema_json()
            example = json.dumps(handler.get_example_instance(), indent=2)
            
            schema_prompt = f"""
Your response must be a valid JSON object that conforms to the following schema:

{schema_json}

Here's an example of a valid response:

{example}

Please ensure your response is a valid JSON object and nothing else.
"""
            prompt = f"{prompt}\n\n{schema_prompt}"

    try:
        if max_attempts > 1:
            foo = tenacity.retry(
                stop=tenacity.stop_after_attempt(max_attempts),
                wait=tenacity.wait_exponential(exp_base=2, multiplier=2),
                after=tenacity.after_log(logger=logger, log_level=1),
            )(self._async_get_response)
            response = await foo(
                message=prompt,
                temperature=temperature,
                max_tokens=max_tokens,
                model=model,
                returns_list=step.fanout,
            )
        else:
            response = await self._async_get_response(
                message=prompt,
                temperature=temperature,
                max_tokens=max_tokens,
                model=model,
                returns_list=step.fanout,
            )
    except Exception as e:
        error_msg = str(e).replace("\n", " ")
        logger.error(f"Error in _process_chain_step: {error_msg}")
        c_row[step.col] = f"[N/A] [ERROR: {error_msg}]"
    else:
        # Validate with Pydantic if model is specified
        if step.pydantic_model and not step.fanout:
            handler = PydanticModelRegistry.get_handler(step.pydantic_model)
            if handler:
                validated_model, error = handler.validate(response)
                
                if validated_model:
                    # Store both raw response and validated model
                    c_row[step.col] = response
                    c_row[f"{step.col}_validated"] = validated_model.model_dump()
                    c_row[f"{step.col}_valid"] = True
                else:
                    # Handle validation error
                    logger.warning(f"Validation error for {step.col}: {error}")
                    c_row[step.col] = response
                    c_row[f"{step.col}_valid"] = False
                    c_row[f"{step.col}_error"] = error
                    
                    # If strict validation is enabled, treat this as an error
                    if step.strict_validation:
                        c_row[step.col] = f"[N/A] [VALIDATION ERROR: {error}]"
            else:
                # Model not found
                logger.warning(f"Pydantic model '{step.pydantic_model}' not found")
                c_row[step.col] = response
        else:
            # No validation or fanout is enabled
            c_row[step.col] = response
```

### 7. Add Configuration for Pydantic Models in the Sheet

Add a new configuration row in the sheet for Pydantic model specifications:

```
Row A: "PYDANTIC_MODEL"
Row B-Z: Pydantic model name for each output column
```

And another for validation strictness:

```
Row A: "STRICT_VALIDATION"
Row B-Z: "TRUE" or "FALSE" for each output column
```

### 8. Update Main Function to Extract Pydantic Configuration

```python
def main():
    # ... existing code ...
    
    # Extract Pydantic model configuration
    pydantic_models = get_config_by_name(
        df, "PYDANTIC_MODEL", prompt_output_cols, default_value=""
    )
    
    # Extract strict validation configuration
    strict_validation_values = get_config_by_name(
        df, "STRICT_VALIDATION", prompt_output_cols, default_value="FALSE"
    )
    strict_validation = [
        str(val).strip().upper() == "TRUE" for val in strict_validation_values
    ]
    
    # ... existing code ...
    
    # Process columns in sequence to handle dependencies between columns
    for idx, (
        prompt_template,
        model,
        temperature,
        max_tokens,
        flags,
        output_col,
        pydantic_model,
        strict_val,
    ) in enumerate(
        zip(
            prompt_templates,
            models,
            temperatures,
            max_output_tokens,
            flags,
            prompt_output_cols,
            pydantic_models,
            strict_validation,
        )
    ):
        # ... existing code ...
        
        # Set the template column as the source for the prompt
        chain = [
            ChainStep(
                pt=template_col,  # Use the template column as source
                model=model,
                temperature=temperature,
                max_tokens=max_tokens,
                col=output_col,
                pydantic_model=pydantic_model if pydantic_model else None,
                strict_validation=strict_val,
            ),
        ]
        
        # ... continue with existing code ...
```

### 9. Add Command Line Interface for Pydantic Model Management

```python
def parse_arguments():
    parser = argparse.ArgumentParser(description='Process Google Sheet data with LLM.')
    # ... existing arguments ...
    
    # Add Pydantic model management subcommands
    subparsers = parser.add_subparsers(dest='command', help='Commands')
    
    # List models command
    list_parser = subparsers.add_parser('list-models', help='List registered Pydantic models')
    
    # Show model schema command
    schema_parser = subparsers.add_parser('show-schema', help='Show schema for a Pydantic model')
    schema_parser.add_argument('model_name', help='Name of the Pydantic model')
    
    # Generate example command
    example_parser = subparsers.add_parser('generate-example', help='Generate example instance of a Pydantic model')
    example_parser.add_argument('model_name', help='Name of the Pydantic model')
    
    return parser.parse_args()

def main():
    args = parse_arguments()
    
    # Handle Pydantic model management commands
    if args.command == 'list-models':
        models = PydanticModelRegistry.list_models()
        print("Registered Pydantic models:")
        for model in models:
            print(f"- {model}")
        return
    
    elif args.command == 'show-schema':
        schema = PydanticModelRegistry.get_schema(args.model_name)
        if schema:
            print(json.dumps(schema, indent=2))
        else:
            print(f"Model '{args.model_name}' not found")
        return
    
    elif args.command == 'generate-example':
        handler = PydanticModelRegistry.get_handler(args.model_name)
        if handler:
            example = handler.get_example_instance()
            print(json.dumps(example, indent=2))
        else:
            print(f"Model '{args.model_name}' not found")
        return
    
    # ... continue with existing code ...
```

## Architecture Diagram

```mermaid
classDiagram
    class BaseModel {
        +model_validate(data)
        +model_dump()
        +model_json_schema()
    }
    
    class PydanticHandler {
        +Type[BaseModel] model_class
        +validate(data)
        +get_model_schema()
        +get_model_schema_json()
        +get_example_instance()
    }
    
    class PydanticModelRegistry {
        +Dict[str, Type[BaseModel]] _models
        +Dict[str, PydanticHandler] _handlers
        +register_model(name, model_class)
        +get_model_class(name)
        +get_handler(name)
        +list_models()
        +get_schema(name)
    }
    
    class ChainStep {
        +str pt
        +Dict mapping
        +float temperature
        +int max_tokens
        +str model
        +str col
        +bool fanout
        +str unfan_col
        +bool overwrite
        +List validators
        +str pydantic_model
        +bool strict_validation
    }
    
    class LLMProcessor {
        +_process_chain_step()
        +execute_chain()
    }
    
    BaseModel <|-- CustomModels : extends
    PydanticHandler --> BaseModel : uses
    PydanticModelRegistry --> PydanticHandler : manages
    ChainStep --> PydanticModelRegistry : references
    LLMProcessor --> ChainStep : uses
    LLMProcessor --> PydanticModelRegistry : uses
```

## Process Flow

```mermaid
flowchart TD
    A[Start] --> B[Load Configuration]
    B --> C[Extract Pydantic Model Config]
    C --> D[Process Each Column]
    
    D --> E{Pydantic Model Specified?}
    E -->|Yes| F[Add Schema to Prompt]
    E -->|No| G[Process Normally]
    
    F --> H[Process with LLM]
    G --> H
    
    H --> I{Validate Response?}
    I -->|Yes| J[Validate with Pydantic]
    I -->|No| K[Store Raw Response]
    
    J --> L{Validation Successful?}
    L -->|Yes| M[Store Validated Model]
    L -->|No| N{Strict Validation?}
    
    N -->|Yes| O[Store Error]
    N -->|No| P[Store Raw Response + Error]
    
    M --> Q[Update Sheet]
    O --> Q
    P --> Q
    K --> Q
```

## Example Usage

### 1. Define Pydantic Models

```python
from pydantic import BaseModel, Field

class ProductReview(BaseModel):
    """Model for a product review."""
    rating: int = Field(..., ge=1, le=5, description="Rating from 1 to 5")
    title: str = Field(..., min_length=3, max_length=100, description="Review title")
    content: str = Field(..., min_length=10, description="Review content")
    pros: list[str] = Field(default_factory=list, description="List of pros")
    cons: list[str] = Field(default_factory=list, description="List of cons")

# Register model
PydanticModelRegistry.register_model("ProductReview", ProductReview)
```

### 2. Configure in Sheet

```
Row A: "PYDANTIC_MODEL"
Row B (output column): "ProductReview"

Row A: "STRICT_VALIDATION"
Row B (output column): "TRUE"
```

### 3. Use in Template

```
Please write a review for the product: {{product_name}}

Include a rating, title, content, pros, and cons.
```

## Context and Considerations

1. **Type Safety**: Pydantic provides strong type checking and validation, ensuring that LLM outputs conform to expected structures.
2. **Error Handling**: The implementation includes comprehensive error handling for validation failures, with options for strict or lenient validation.
3. **Documentation**: Pydantic models provide self-documenting schemas that can be used to guide LLM responses.
4. **Integration with LLMs**: Adding schema information to prompts helps LLMs generate correctly structured responses.
5. **Flexibility**: The implementation allows for different validation settings for each column, providing flexibility in how strictly to enforce schemas.
6. **Performance**: Validation adds some overhead, so it should be optional and configurable.
7. **Future Enhancements**: 
   - Support for more complex validation rules
   - Integration with OpenAPI for API documentation
   - Custom validators for domain-specific validation
   - Schema evolution and versioning