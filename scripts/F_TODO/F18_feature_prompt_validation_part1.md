# Feature: Prompt Validation and Debugging (Part 1)

Original requirement:
> Prompt validation and debugging -> how to debug prompts? Validate that all vars are filled in, etc.

## Implementation Status: Not Implemented

The current implementation in `main2.py` and related files does not include comprehensive prompt validation and debugging features. While there is basic template filling and some error handling, there's no systematic validation of prompts before sending them to LLMs or tools to help debug prompt issues.

## Feature Description

This feature would add prompt validation and debugging capabilities to help identify and fix issues with prompts before they're sent to LLMs. In Part 1, we'll focus on:

1. Validating that all template variables are filled in
2. Checking for common prompt issues
3. Providing detailed validation reports

This would help prevent errors, improve prompt quality, and make it easier to debug issues when they occur.

## Implementation Recommendation: Prompt Validation

### 1. Define Prompt Validation Classes

```python
from enum import Enum
from typing import List, Dict, Any, Optional, Set, Tuple
import re

class ValidationLevel(Enum):
    """Levels of validation severity."""
    ERROR = "error"        # Critical issue that should be fixed
    WARNING = "warning"    # Potential issue that might need attention
    INFO = "info"          # Informational message

class ValidationIssue:
    """
    Represents an issue found during prompt validation.
    """
    
    def __init__(
        self,
        level: ValidationLevel,
        message: str,
        location: Optional[str] = None,
        suggestion: Optional[str] = None,
    ):
        """
        Initialize a validation issue.
        
        Args:
            level: Severity level
            message: Description of the issue
            location: Where in the prompt the issue was found
            suggestion: Suggested fix
        """
        self.level = level
        self.message = message
        self.location = location
        self.suggestion = suggestion
    
    def __str__(self) -> str:
        """String representation of the issue."""
        parts = [f"[{self.level.value.upper()}] {self.message}"]
        
        if self.location:
            parts.append(f"Location: {self.location}")
        
        if self.suggestion:
            parts.append(f"Suggestion: {self.suggestion}")
        
        return " | ".join(parts)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage or display."""
        return {
            "level": self.level.value,
            "message": self.message,
            "location": self.location,
            "suggestion": self.suggestion,
        }

class PromptValidationResult:
    """
    Result of prompt validation.
    """
    
    def __init__(self):
        """Initialize an empty validation result."""
        self.issues: List[ValidationIssue] = []
    
    def add_issue(
        self,
        level: ValidationLevel,
        message: str,
        location: Optional[str] = None,
        suggestion: Optional[str] = None,
    ) -> None:
        """
        Add an issue to the validation result.
        
        Args:
            level: Severity level
            message: Description of the issue
            location: Where in the prompt the issue was found
            suggestion: Suggested fix
        """
        self.issues.append(
            ValidationIssue(
                level=level,
                message=message,
                location=location,
                suggestion=suggestion,
            )
        )
    
    def has_errors(self) -> bool:
        """Check if there are any error-level issues."""
        return any(issue.level == ValidationLevel.ERROR for issue in self.issues)
    
    def has_warnings(self) -> bool:
        """Check if there are any warning-level issues."""
        return any(issue.level == ValidationLevel.WARNING for issue in self.issues)
    
    def has_issues(self) -> bool:
        """Check if there are any issues."""
        return len(self.issues) > 0
    
    def get_issues_by_level(self, level: ValidationLevel) -> List[ValidationIssue]:
        """Get all issues of a specific level."""
        return [issue for issue in self.issues if issue.level == level]
    
    def __str__(self) -> str:
        """String representation of the validation result."""
        if not self.has_issues():
            return "No validation issues found."
        
        parts = []
        
        errors = self.get_issues_by_level(ValidationLevel.ERROR)
        if errors:
            parts.append(f"Errors ({len(errors)}):")
            for i, error in enumerate(errors, 1):
                parts.append(f"  {i}. {error}")
        
        warnings = self.get_issues_by_level(ValidationLevel.WARNING)
        if warnings:
            parts.append(f"Warnings ({len(warnings)}):")
            for i, warning in enumerate(warnings, 1):
                parts.append(f"  {i}. {warning}")
        
        infos = self.get_issues_by_level(ValidationLevel.INFO)
        if infos:
            parts.append(f"Info ({len(infos)}):")
            for i, info in enumerate(infos, 1):
                parts.append(f"  {i}. {info}")
        
        return "\n".join(parts)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage or display."""
        return {
            "has_errors": self.has_errors(),
            "has_warnings": self.has_warnings(),
            "error_count": len(self.get_issues_by_level(ValidationLevel.ERROR)),
            "warning_count": len(self.get_issues_by_level(ValidationLevel.WARNING)),
            "info_count": len(self.get_issues_by_level(ValidationLevel.INFO)),
            "issues": [issue.to_dict() for issue in self.issues],
        }
```

### 2. Implement Prompt Validators

```python
class PromptValidator:
    """
    Base class for prompt validators.
    """
    
    def validate(self, prompt: str, context: Dict[str, Any] = None) -> PromptValidationResult:
        """
        Validate a prompt.
        
        Args:
            prompt: Prompt to validate
            context: Additional context for validation
            
        Returns:
            Validation result
        """
        raise NotImplementedError("Subclasses must implement validate()")

class TemplateVariableValidator(PromptValidator):
    """
    Validator for template variables.
    """
    
    def validate(self, prompt: str, context: Dict[str, Any] = None) -> PromptValidationResult:
        """
        Validate that all template variables are filled in.
        
        Args:
            prompt: Prompt to validate
            context: Additional context for validation
            
        Returns:
            Validation result
        """
        result = PromptValidationResult()
        context = context or {}
        
        # Find all template variables
        template_vars = re.findall(r"\{\{([^}]+)\}\}", prompt)
        
        if not template_vars:
            # No template variables found
            return result
        
        # Check if any template variables remain unfilled
        for var in template_vars:
            result.add_issue(
                level=ValidationLevel.ERROR,
                message=f"Unfilled template variable: {{{{{{var}}}}}}",
                location=f"Template variable {{{{{{var}}}}}}",
                suggestion=f"Ensure that the variable '{var}' is available in the input data.",
            )
        
        return result

class PromptLengthValidator(PromptValidator):
    """
    Validator for prompt length.
    """
    
    def __init__(self, max_length: int = 8000, warning_threshold: float = 0.8):
        """
        Initialize the prompt length validator.
        
        Args:
            max_length: Maximum allowed prompt length in characters
            warning_threshold: Threshold (as fraction of max_length) for warnings
        """
        self.max_length = max_length
        self.warning_threshold = warning_threshold
    
    def validate(self, prompt: str, context: Dict[str, Any] = None) -> PromptValidationResult:
        """
        Validate that the prompt is not too long.
        
        Args:
            prompt: Prompt to validate
            context: Additional context for validation
            
        Returns:
            Validation result
        """
        result = PromptValidationResult()
        context = context or {}
        
        # Get prompt length
        length = len(prompt)
        
        # Check if prompt is too long
        if length > self.max_length:
            result.add_issue(
                level=ValidationLevel.ERROR,
                message=f"Prompt is too long: {length} characters (max {self.max_length})",
                suggestion=f"Reduce prompt length to less than {self.max_length} characters.",
            )
        elif length > self.max_length * self.warning_threshold:
            result.add_issue(
                level=ValidationLevel.WARNING,
                message=f"Prompt is approaching maximum length: {length} characters ({length / self.max_length:.1%} of max)",
                suggestion=f"Consider reducing prompt length to improve reliability.",
            )
        
        return result

class CommonIssuesValidator(PromptValidator):
    """
    Validator for common prompt issues.
    """
    
    def validate(self, prompt: str, context: Dict[str, Any] = None) -> PromptValidationResult:
        """
        Validate that the prompt doesn't have common issues.
        
        Args:
            prompt: Prompt to validate
            context: Additional context for validation
            
        Returns:
            Validation result
        """
        result = PromptValidationResult()
        context = context or {}
        
        # Check for repeated instructions
        repeated_phrases = self._find_repeated_phrases(prompt)
        for phrase, count in repeated_phrases.items():
            if count > 1 and len(phrase) > 20:  # Only flag substantial phrases
                result.add_issue(
                    level=ValidationLevel.WARNING,
                    message=f"Repeated phrase found {count} times: '{phrase[:30]}...'",
                    suggestion="Remove redundant instructions to improve clarity.",
                )
        
        # Check for conflicting instructions
        conflicts = self._find_conflicting_instructions(prompt)
        for conflict in conflicts:
            result.add_issue(
                level=ValidationLevel.WARNING,
                message=f"Potentially conflicting instructions: '{conflict}'",
                suggestion="Clarify or remove conflicting instructions.",
            )
        
        # Check for vague instructions
        vague_terms = self._find_vague_terms(prompt)
        if vague_terms:
            result.add_issue(
                level=ValidationLevel.INFO,
                message=f"Vague terms found: {', '.join(vague_terms)}",
                suggestion="Consider using more specific language for clearer instructions.",
            )
        
        return result
    
    def _find_repeated_phrases(self, text: str) -> Dict[str, int]:
        """Find repeated phrases in text."""
        # This is a simplified implementation
        # A more sophisticated approach would use NLP techniques
        words = text.split()
        phrases = {}
        
        # Check for phrases of 3-6 words
        for phrase_length in range(3, 7):
            for i in range(len(words) - phrase_length + 1):
                phrase = " ".join(words[i:i+phrase_length])
                phrases[phrase] = phrases.get(phrase, 0) + 1
        
        # Filter to only include repeated phrases
        return {phrase: count for phrase, count in phrases.items() if count > 1}
    
    def _find_conflicting_instructions(self, text: str) -> List[str]:
        """Find potentially conflicting instructions."""
        # This is a simplified implementation
        # A more sophisticated approach would use NLP techniques
        conflicts = []
        
        # Check for common conflict patterns
        patterns = [
            (r"be (brief|concise|short)", r"be (detailed|comprehensive|thorough)"),
            (r"(don't|do not) use", r"(use|include)"),
            (r"(focus on|emphasize)", r"(ignore|exclude)"),
        ]
        
        for pattern1, pattern2 in patterns:
            if re.search(pattern1, text, re.IGNORECASE) and re.search(pattern2, text, re.IGNORECASE):
                match1 = re.search(pattern1, text, re.IGNORECASE).group(0)
                match2 = re.search(pattern2, text, re.IGNORECASE).group(0)
                conflicts.append(f"'{match1}' vs '{match2}'")
        
        return conflicts
    
    def _find_vague_terms(self, text: str) -> List[str]:
        """Find vague terms in text."""
        vague_terms = [
            "good", "better", "best", "nice", "great", "awesome",
            "appropriate", "suitable", "reasonable", "adequate",
            "etc", "and so on", "and more", "some", "many", "few",
            "several", "various", "different", "other",
        ]
        
        found_terms = []
        for term in vague_terms:
            pattern = r"\b" + re.escape(term) + r"\b"
            if re.search(pattern, text, re.IGNORECASE):
                found_terms.append(term)
        
        return found_terms
```

### 3. Create Composite Validator

```python
class CompositeValidator(PromptValidator):
    """
    Validator that combines multiple validators.
    """
    
    def __init__(self, validators: List[PromptValidator] = None):
        """
        Initialize the composite validator.
        
        Args:
            validators: List of validators to use
        """
        self.validators = validators or []
    
    def add_validator(self, validator: PromptValidator) -> None:
        """
        Add a validator to the composite.
        
        Args:
            validator: Validator to add
        """
        self.validators.append(validator)
    
    def validate(self, prompt: str, context: Dict[str, Any] = None) -> PromptValidationResult:
        """
        Validate a prompt using all validators.
        
        Args:
            prompt: Prompt to validate
            context: Additional context for validation
            
        Returns:
            Combined validation result
        """
        result = PromptValidationResult()
        context = context or {}
        
        # Run all validators
        for validator in self.validators:
            validator_result = validator.validate(prompt, context)
            
            # Add issues from this validator to the combined result
            for issue in validator_result.issues:
                result.add_issue(
                    level=issue.level,
                    message=issue.message,
                    location=issue.location,
                    suggestion=issue.suggestion,
                )
        
        return result
```

### 4. Create Default Validator

```python
def create_default_validator() -> CompositeValidator:
    """
    Create a default validator with common validation rules.
    
    Returns:
        CompositeValidator instance
    """
    validator = CompositeValidator()
    
    # Add standard validators
    validator.add_validator(TemplateVariableValidator())
    validator.add_validator(PromptLengthValidator())
    validator.add_validator(CommonIssuesValidator())
    
    return validator
```

### 5. Integrate Validation into Template Filling

```python
def fill_template_with_validation(
    row: Dict[str, Any],
    template: str,
    input_cols: List[str],
    validate: bool = True,
) -> Tuple[str, Optional[PromptValidationResult]]:
    """
    Fill template with values from a DataFrame row and validate the result.
    
    Args:
        row: DataFrame row with input values
        template: String template with {{variable}} placeholders
        input_cols: List of column names to use for variable replacement
        validate: Whether to validate the filled template
        
    Returns:
        Tuple of (filled_template, validation_result)
    """
    # Track which variables were used
    used_vars = set()
    available_vars = set(input_cols)
    
    # Find all template variables
    template_vars = re.findall(r"\{\{([^}]+)\}\}", template)
    
    # Fill template variables
    filled_template = template
    for var in template_vars:
        base_var = var.split(".")[0]  # Handle dot notation
        
        if base_var in row and base_var in input_cols:
            value = row[base_var]
            filled_template = filled_template.replace("{{" + var + "}}", str(value))
            used_vars.add(base_var)
        else:
            # Leave the variable unfilled for validation
            pass
    
    # Validate if requested
    if validate:
        validator = create_default_validator()
        validation_result = validator.validate(filled_template, {
            "used_vars": used_vars,
            "available_vars": available_vars,
            "template_vars": template_vars,
        })
        
        # Add warnings for unused available variables
        unused_vars = available_vars - used_vars
        if unused_vars:
            validation_result.add_issue(
                level=ValidationLevel.INFO,
                message=f"Unused available variables: {', '.join(unused_vars)}",
                suggestion="Consider using these variables in your template if relevant.",
            )
        
        return filled_template, validation_result
    
    return filled_template, None
```

## Architecture Diagram

```mermaid
classDiagram
    class ValidationLevel {
        <<enumeration>>
        ERROR
        WARNING
        INFO
    }
    
    class ValidationIssue {
        +ValidationLevel level
        +str message
        +str location
        +str suggestion
        +__str__()
        +to_dict()
    }
    
    class PromptValidationResult {
        +List[ValidationIssue] issues
        +add_issue(level, message, location, suggestion)
        +has_errors()
        +has_warnings()
        +has_issues()
        +get_issues_by_level(level)
        +__str__()
        +to_dict()
    }
    
    class PromptValidator {
        <<interface>>
        +validate(prompt, context)
    }
    
    class TemplateVariableValidator {
        +validate(prompt, context)
    }
    
    class PromptLengthValidator {
        +int max_length
        +float warning_threshold
        +validate(prompt, context)
    }
    
    class CommonIssuesValidator {
        +validate(prompt, context)
        -_find_repeated_phrases(text)
        -_find_conflicting_instructions(text)
        -_find_vague_terms(text)
    }
    
    class CompositeValidator {
        +List[PromptValidator] validators
        +add_validator(validator)
        +validate(prompt, context)
    }
    
    ValidationLevel <-- ValidationIssue : uses
    ValidationIssue <-- PromptValidationResult : contains
    PromptValidator <|-- TemplateVariableValidator : implements
    PromptValidator <|-- PromptLengthValidator : implements
    PromptValidator <|-- CommonIssuesValidator : implements
    PromptValidator <|-- CompositeValidator : implements
    PromptValidator <-- CompositeValidator : contains
```

## Context and Considerations

1. **Template Variable Validation**: The implementation checks that all template variables are filled in, helping to prevent errors from missing data.

2. **Prompt Length Validation**: The implementation checks that prompts aren't too long, which can help prevent token limit issues with LLMs.

3. **Common Issues Detection**: The implementation checks for common prompt issues like repeated or conflicting instructions, helping to improve prompt quality.

4. **Extensibility**: The validator architecture is extensible, making it easy to add new validation rules as needed.

5. **Integration with Template Filling**: The implementation integrates validation with template filling, making it easy to validate prompts as they're created.

6. **Detailed Reporting**: The implementation provides detailed validation reports with severity levels, locations, and suggestions for fixing issues.

7. **Future Enhancements**: 
   - More sophisticated NLP-based validation
   - Model-specific validation rules
   - Integration with prompt libraries and best practices
   - Learning from successful and failed prompts to improve validation
