# Delegate Columns

Original feature description:

```text
Having some cols be 'delegates', i.e., they just run a pre-made python function. We might want this for e.g., complex multi-output regex's or NLP or who knows what. Should be flexible to work for ANY python function so handling different numbers of params etc - cover the bases.
```

## Feature Description

The "Delegate Columns" feature allows users to specify columns that execute custom Python functions instead of LLM calls. This provides a powerful way to extend the gsheet-model-breaker-demo tool with arbitrary Python code for tasks such as:

1. **Complex Text Processing**: Regular expressions, NLP operations, text extraction
2. **Data Transformation**: Formatting, normalization, type conversion
3. **External API Calls**: Integrating with third-party services
4. **Multi-Output Processing**: Generating multiple outputs from a single input
5. **Specialized Algorithms**: Implementing domain-specific algorithms

Delegate columns would be marked in the sheet with a special identifier (e.g., "py-delegate" instead of "py-llm") and would reference Python functions that are either pre-defined in a module or dynamically defined in the sheet itself.

## Current Implementation Status

Currently, the gsheet-model-breaker-demo does not support delegate columns. The existing implementation focuses exclusively on LLM processing through the ChainStep class:

<augment_code_snippet path="experiments/src/tools/gsheet-model-breaker-demo/scripts/main2.py" mode="EXCERPT">

````python
# Process data
llm_proc = ParallelLLMDataFrameProcessor(
    def_model=model,
    def_temperature=temperature,
    def_max_tokens=max_tokens,
    def_async_rate_limit=10,
    def_thread_rate_limit=5,
)

# Set the template column as the source for the prompt
chain = [
    ChainStep(
        pt=template_col,  # Use the template column as source
        model=model,
        temperature=temperature,
        max_tokens=max_tokens,
        col=output_col,
    ),
]

start_time = time.time()
result_df = llm_proc.execute_chain(
    chain_input,
    chain,
    max_attempts=3,
)
````

</augment_code_snippet>

## Proposed Implementation

### 1. Create a DelegateStep Class

First, we need to create a new class to represent delegate function steps:

```python
from typing import Callable, Dict, List, Optional, Any, Union
import inspect
import json
import ast

class DelegateStep:
    """
    Represents a step that executes a custom Python function.

    This class defines the structure and parameters for a delegate step,
    including the function to execute, input mapping, and output specifications.

    Attributes:
        func (Callable): The Python function to execute.
        input_mapping (Dict[str, str]): Mapping of function parameter names to column names.
        col (str): The name of the column to store the primary result.
        additional_output_cols (Dict[str, str]): Mapping of additional output keys to column names.
        overwrite (bool): Whether to overwrite existing data in the output columns.
    """

    def __init__(
        self,
        func: Callable,
        input_mapping: Optional[Dict[str, str]] = None,
        col: str = "response",
        additional_output_cols: Optional[Dict[str, str]] = None,
        overwrite: bool = False
    ):
        """
        Initialize a DelegateStep.

        Args:
            func: The Python function to execute.
            input_mapping: Mapping of function parameter names to column names.
                If None, parameter names are assumed to match column names.
            col: The name of the column to store the primary result.
            additional_output_cols: Mapping of additional output keys to column names
                for functions that return dictionaries with multiple outputs.
            overwrite: Whether to overwrite existing data in the output columns.
        """
        self.func = func
        self.input_mapping = input_mapping
        self.col = col
        self.additional_output_cols = additional_output_cols or {}
        self.overwrite = overwrite

        # Extract function signature for validation
        self.signature = inspect.signature(func)
        self.param_names = list(self.signature.parameters.keys())

        # Check if the function is async
        self.is_async = inspect.iscoroutinefunction(func)

    def get_all_output_cols(self) -> List[str]:
        """Get all output column names for this step."""
        return [self.col] + list(self.additional_output_cols.values())

    def get_required_input_cols(self) -> List[str]:
        """Get all required input column names for this step."""
        if self.input_mapping:
            return list(self.input_mapping.values())
        else:
            return self.param_names
```

### 2. Add Delegate Processing to the Processor Class

Extend the ParallelLLMDataFrameProcessor class to handle delegate steps:

```python
async def _process_delegate_step(
    self,
    c_row: pd.Series,
    step: DelegateStep
) -> None:
    """
    Process a single delegate step for a given row.

    Args:
        c_row: The current row of the DataFrame.
        step: The delegate step to process.
    """
    # Skip if output already exists and overwrite is False
    if c_row.get(step.col) and not step.overwrite:
        logger.debug(f"{step.col} already exists (skip)")
        return

    # Prepare function arguments
    kwargs = {}
    missing_params = []

    for param_name in step.param_names:
        if step.input_mapping and param_name in step.input_mapping:
            col_name = step.input_mapping[param_name]
            if col_name in c_row:
                kwargs[param_name] = c_row[col_name]
            else:
                missing_params.append(col_name)
        elif param_name in c_row:
            kwargs[param_name] = c_row[param_name]
        else:
            missing_params.append(param_name)

    # Log warning for missing parameters
    if missing_params:
        logger.warning(f"Missing parameters for delegate function: {missing_params}")

    try:
        # Execute the function
        if step.is_async:
            result = await step.func(**kwargs)
        else:
            result = step.func(**kwargs)

        # Handle different result types
        if result is None:
            c_row[step.col] = ""
        elif isinstance(result, dict) and step.additional_output_cols:
            # Store primary result if specified in the dictionary
            if "result" in result:
                c_row[step.col] = str(result["result"])
            else:
                # If no "result" key, store the whole dict as JSON in the primary column
                c_row[step.col] = json.dumps(result)

            # Store additional outputs in their respective columns
            for key, col_name in step.additional_output_cols.items():
                if key in result:
                    c_row[col_name] = str(result[key])
        else:
            # For simple return values, just store in the primary column
            c_row[step.col] = str(result)

    except Exception as e:
        error_msg = str(e).replace("\n", " ")
        logger.error(f"Error in _process_delegate_step: {error_msg}")
        c_row[step.col] = f"[N/A] [ERROR: {error_msg}]"
```

### 3. Modify the Chain Processing to Handle Delegate Steps

Update the _process_chain method to handle both ChainStep and DelegateStep:

```python
async def _process_chain(self, input_row: pd.Series, chain: List[Union[ChainStep, DelegateStep]], max_attempts: int) -> List[pd.Series]:
    """
    Process a chain of steps for a given row.

    Args:
        input_row: The input row of the DataFrame.
        chain: The list of steps to process (can be ChainStep or DelegateStep).
        max_attempts: The maximum number of retry attempts for failed API calls.

    Returns:
        List of processed rows.
    """
    assert max_attempts >= 1
    c_rows = [input_row.copy()]

    for step in chain:
        if isinstance(step, ChainStep):
            # Process LLM step (existing code)
            if step.mapping is None:
                keywords = self.extract_fstring_keywords(step.pt)
                mapping = {k: k for k in keywords}
            else:
                mapping = step.mapping

            tasks = [
                self._process_chain_step(c_row, step, mapping, max_attempts)
                for c_row in c_rows
            ]
            await asyncio.gather(*tasks)

            if step.fanout:
                df = pd.DataFrame(c_rows)
                df = df.explode(column=step.col)
                c_rows = [r for _, r in df.iterrows()]

        elif isinstance(step, DelegateStep):
            # Process delegate step
            tasks = [
                self._process_delegate_step(c_row, step)
                for c_row in c_rows
            ]
            await asyncio.gather(*tasks)

    return c_rows
```

### 4. Function Registry for Delegate Functions

Create a registry to manage available delegate functions:

```python
class DelegateFunctionRegistry:
    """Registry for delegate functions that can be used in delegate steps."""

    def __init__(self):
        """Initialize the registry."""
        self.functions = {}

    def register(self, name: str, func: Callable) -> None:
        """
        Register a function with the registry.

        Args:
            name: The name to register the function under.
            func: The function to register.
        """
        self.functions[name] = func

    def get(self, name: str) -> Optional[Callable]:
        """
        Get a function by name.

        Args:
            name: The name of the function to get.

        Returns:
            The function if found, None otherwise.
        """
        return self.functions.get(name)

    def list_functions(self) -> Dict[str, str]:
        """
        List all registered functions with their signatures.

        Returns:
            Dictionary mapping function names to their signatures.
        """
        result = {}
        for name, func in self.functions.items():
            sig = inspect.signature(func)
            result[name] = f"{name}{sig}"
        return result

    def register_from_module(self, module) -> None:
        """
        Register all functions from a module.

        Args:
            module: The module to register functions from.
        """
        for name, obj in inspect.getmembers(module, inspect.isfunction):
            self.register(name, obj)

    def register_from_code(self, name: str, code: str) -> None:
        """
        Register a function from its source code.

        Args:
            name: The name to register the function under.
            code: The source code of the function.

        Raises:
            SyntaxError: If the code is not valid Python.
            ValueError: If the code does not define a function.
        """
        try:
            # Parse the code to check for syntax errors
            tree = ast.parse(code)

            # Check if the code defines a function
            if not any(isinstance(node, ast.FunctionDef) for node in tree.body):
                raise ValueError("Code does not define a function")

            # Create a namespace for the function
            namespace = {}

            # Execute the code in the namespace
            exec(code, namespace)

            # Find the function in the namespace
            for obj_name, obj in namespace.items():
                if inspect.isfunction(obj):
                    self.register(name, obj)
                    return

            raise ValueError(f"No function found in code")

        except SyntaxError as e:
            raise SyntaxError(f"Invalid Python syntax: {str(e)}")
```

### 5. Integration with Main Processing Flow

Update the main2.py file to support delegate columns:

```python
# Import the new classes
from llm_processor.delegate_step import DelegateStep, DelegateFunctionRegistry

# Initialize the function registry
delegate_registry = DelegateFunctionRegistry()

# Register built-in functions
def extract_entities(text):
    """Extract entities from text using simple regex patterns."""
    import re

    entities = {
        "emails": re.findall(r'[\w\.-]+@[\w\.-]+', text),
        "phone_numbers": re.findall(r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b', text),
        "urls": re.findall(r'https?://(?:[-\w.]|(?:%[\da-fA-F]{2}))+', text),
        "dates": re.findall(r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b', text)
    }

    return {
        "result": f"Found {sum(len(v) for v in entities.values())} entities",
        **entities
    }

delegate_registry.register("extract_entities", extract_entities)

# In main function, modify the column processing loop
for idx, (prompt_template, model, temperature, max_tokens, flags, output_col) in enumerate(zip(
    prompt_templates, models, temperatures, max_output_tokens, flags, prompt_output_cols
)):
    # Check if this column is a delegate column
    is_delegate = "py-delegate" in df.iloc[0][output_col].lower()

    if is_delegate:
        # Extract delegate function name from the prompt template
        function_name = prompt_template.strip()

        # Get the function from the registry
        delegate_func = delegate_registry.get(function_name)

        if delegate_func is None:
            logger.error(f"Delegate function '{function_name}' not found in registry")
            continue

        # Create filled_template column for debugging
        template_col = f"filled_template_{output_col}"
        current_input_cols = prompt_input_cols.copy()

        # Add previously processed output columns as potential input variables
        for prev_idx in range(idx):
            prev_col = prompt_output_cols[prev_idx]
            if prev_col not in current_input_cols and prev_col in prompt_inputs.columns:
                current_input_cols.append(prev_col)

        # Find rows with "<generate>" tag
        generate_mask = (prompt_inputs[f"original_{output_col}"] == "<generate>") | (prompt_inputs[f"original_{output_col}"] == "//")

        if not generate_mask.any():
            logger.info(f"No rows with '<generate>' tag found for {output_col}. Skipping.")
            continue

        generate_indices = generate_mask[generate_mask].index.tolist()
        chain_input = prompt_inputs.loc[generate_indices].copy()

        # Parse additional output columns from flags
        additional_output_cols = {}
        for flag in flags.split():
            if flag.startswith("output:"):
                parts = flag.split(":", 2)
                if len(parts) == 3:
                    output_key, output_col_name = parts[1], parts[2]
                    additional_output_cols[output_key] = output_col_name

        # Create a delegate step
        delegate_step = DelegateStep(
            func=delegate_func,
            col=output_col,
            additional_output_cols=additional_output_cols
        )

        # Process the delegate step
        llm_proc = ParallelLLMDataFrameProcessor()  # No need for LLM parameters

        # Create a chain with just the delegate step
        chain = [delegate_step]

        start_time = time.time()
        result_df = llm_proc.execute_chain(
            chain_input,
            chain,
            max_attempts=1  # No retries for delegate functions
        )
        logger.info(f"Processing time: {time.time() - start_time:.2f} seconds")

        # Rest of the processing is the same as for LLM columns
    else:
        # Process normally with ChainStep (existing code)
```

### 6. Support for Dynamic Function Definition in Sheets

Add support for defining functions directly in the sheet:

```python
# Add a hidden sheet for function definitions
def load_delegate_functions_from_sheet(sheet_id):
    """
    Load delegate functions defined in the _functions sheet.

    Args:
        sheet_id: The ID of the Google Sheet.

    Returns:
        The DelegateFunctionRegistry with loaded functions.
    """
    registry = DelegateFunctionRegistry()

    # Register built-in functions
    registry.register("extract_entities", extract_entities)
    # Add more built-in functions here

    try:
        # Try to load functions from the _functions sheet
        functions_df = get_data_from_sheet(sheet_id, range_name="_functions")

        for _, row in functions_df.iterrows():
            if pd.isna(row.get("name")) or pd.isna(row.get("code")):
                continue

            function_name = row["name"]
            function_code = row["code"]

            try:
                registry.register_from_code(function_name, function_code)
                logger.info(f"Registered delegate function '{function_name}' from sheet")
            except Exception as e:
                logger.error(f"Error registering function '{function_name}': {str(e)}")

    except Exception as e:
        # If the _functions sheet doesn't exist or can't be read
        logger.warning(f"Could not load functions from sheet: {str(e)}")

    return registry
```

## Implementation Considerations

### 1. Security Concerns

Executing arbitrary Python code from a spreadsheet poses significant security risks:

```python
# Security measures for delegate functions
def validate_function_code(code: str) -> bool:
    """
    Validate function code for security concerns.

    Args:
        code: The function code to validate.

    Returns:
        True if the code passes security checks, False otherwise.
    """
    # Parse the code into an AST
    try:
        tree = ast.parse(code)
    except SyntaxError:
        return False

    # Check for potentially dangerous operations
    for node in ast.walk(tree):
        # Check for imports of potentially dangerous modules
        if isinstance(node, ast.Import):
            for name in node.names:
                if name.name in ["os", "subprocess", "sys", "shutil"]:
                    return False

        # Check for exec or eval calls
        if isinstance(node, ast.Call) and isinstance(node.func, ast.Name):
            if node.func.id in ["exec", "eval", "compile"]:
                return False

    return True
```

### 2. Parameter Handling

Delegate functions need to handle various parameter scenarios:

1. **Required vs. Optional Parameters**: Functions may have both required and optional parameters
2. **Type Conversion**: Input data from sheets may need conversion to the expected types
3. **Default Values**: Functions may define default values for parameters
4. **Variable Arguments**: Functions may use *args and **kwargs

```python
def prepare_function_args(func, row_data, input_mapping=None):
    """
    Prepare arguments for a function based on row data and parameter specifications.

    Args:
        func: The function to prepare arguments for.
        row_data: The row data containing potential argument values.
        input_mapping: Optional mapping of parameter names to column names.

    Returns:
        Dictionary of arguments to pass to the function.
    """
    signature = inspect.signature(func)
    kwargs = {}

    for param_name, param in signature.parameters.items():
        # Skip *args and **kwargs
        if param.kind in (param.VAR_POSITIONAL, param.VAR_KEYWORD):
            continue

        # Get the column name from the mapping or use the parameter name
        col_name = input_mapping.get(param_name, param_name) if input_mapping else param_name

        # Check if the column exists in the row data
        if col_name in row_data:
            value = row_data[col_name]

            # Handle type conversion based on parameter annotation
            if param.annotation != inspect.Parameter.empty:
                try:
                    # Convert to the annotated type
                    if param.annotation == bool and isinstance(value, str):
                        value = value.lower() in ("true", "yes", "1", "t", "y")
                    elif param.annotation == int and isinstance(value, str):
                        value = int(value)
                    elif param.annotation == float and isinstance(value, str):
                        value = float(value)
                    elif param.annotation == list and isinstance(value, str):
                        value = json.loads(value) if value.startswith("[") else value.split(",")
                    elif param.annotation == dict and isinstance(value, str):
                        value = json.loads(value) if value.startswith("{") else {}
                except (ValueError, json.JSONDecodeError):
                    # If conversion fails, use the original value
                    pass

            kwargs[param_name] = value
        elif param.default != inspect.Parameter.empty:
            # Use default value if column doesn't exist
            pass  # The function will use its default
        else:
            # Missing required parameter
            logger.warning(f"Missing required parameter '{param_name}' for function")

    return kwargs
```

### 3. Error Handling

Robust error handling is essential for delegate functions:

```python
try:
    # Execute the function with prepared arguments
    result = func(**kwargs)
    return result
except Exception as e:
    # Capture the full exception information
    import traceback
    error_info = {
        "error": str(e),
        "type": type(e).__name__,
        "traceback": traceback.format_exc()
    }
    logger.error(f"Error executing delegate function: {error_info['error']}")
    return {
        "result": f"[ERROR: {error_info['type']}] {error_info['error']}",
        "error_details": error_info
    }
```

### 4. Handling Multiple Outputs

Delegate functions may produce multiple outputs that need to be mapped to different columns:

```python
# Example sheet configuration for multiple outputs
# Flags: output:emails:extracted_emails output:phone_numbers:extracted_phones

# In the processing code
if isinstance(result, dict):
    # Store the primary result
    c_row[step.col] = str(result.get("result", ""))

    # Store additional outputs in their respective columns
    for key, col_name in step.additional_output_cols.items():
        if key in result:
            # Create the column if it doesn't exist
            if col_name not in c_row.index:
                c_row[col_name] = ""

            # Store the value
            value = result[key]
            if isinstance(value, (list, dict)):
                c_row[col_name] = json.dumps(value)
            else:
                c_row[col_name] = str(value)
```

### 5. Asynchronous Functions

Support for both synchronous and asynchronous delegate functions:

```python
# Check if the function is async
is_async = inspect.iscoroutinefunction(func)

# Execute accordingly
if is_async:
    result = await func(**kwargs)
else:
    result = func(**kwargs)
```

## Mermaid Diagram: Delegate Column Processing Flow

```mermaid
flowchart TD
    A[Sheet with<br>Delegate Column] --> B[Extract Function<br>Name from Template]
    B --> C[Look up Function<br>in Registry]
    C --> D{Function<br>Found?}

    D -->|No| E[Log Error<br>Skip Column]
    D -->|Yes| F[Find Rows with<br>Generate Tag]

    F --> G[Create DelegateStep<br>with Function]
    G --> H[Process Each Row<br>with Function]

    H --> I{Function<br>Returns Dict?}
    I -->|Yes| J[Store Multiple<br>Outputs in Columns]
    I -->|No| K[Store Result in<br>Primary Column]

    J --> L[Update Sheet<br>with Results]
    K --> L

    subgraph "Function Registry"
    M[Built-in<br>Functions]
    N[Functions from<br>_functions Sheet]
    O[Dynamically<br>Registered Functions]
    end

    M --> C
    N --> C
    O --> C
```

## Example Sheet Configuration

Here's how the sheet configuration might look for delegate columns:

| Column A | Column B | Column C | Column D |
|----------|----------|----------|----------|
| col_ref | inputs | py-llm | py-delegate |
| | | | |
| | | | |
| Prompt Template | Input data | Generate a summary | extract_entities |
| Model | N/A | gpt-4o | N/A |
| Temperature | N/A | 0.3 | N/A |
| Max Tokens | N/A | 500 | N/A |
| Flags | N/A | | output:emails:extracted_emails output:urls:extracted_urls |

## Example Function Definition Sheet

In the `_functions` sheet:

### Example Function Definitions

**Sentiment Analysis Function:**

```python
def sentiment_analysis(text):
    """Analyze sentiment of text."""
    # Simple rule-based sentiment analysis
    positive_words = ["good", "great", "excellent", "positive", "happy"]
    negative_words = ["bad", "terrible", "negative", "sad", "poor"]

    text = text.lower()
    positive_count = sum(text.count(word) for word in positive_words)
    negative_count = sum(text.count(word) for word in negative_words)

    score = (positive_count - negative_count) / (positive_count + negative_count + 0.001)

    sentiment = "positive" if score > 0.1 else "negative" if score < -0.1 else "neutral"

    return {
        "result": f"Sentiment: {sentiment} (score: {score:.2f})",
        "sentiment": sentiment,
        "score": score,
        "positive_count": positive_count,
        "negative_count": negative_count
    }
```

**JSON Extraction Function:**

```python
def extract_json(text):
    """Extract JSON objects from text."""
    import re
    import json

    # Find potential JSON objects
    json_pattern = r'\{(?:[^{}]|(?:\{(?:[^{}]|(?:\{[^{}]*\}))*\}))*\}'
    matches = re.findall(json_pattern, text)

    valid_jsons = []
    for match in matches:
        try:
            parsed = json.loads(match)
            valid_jsons.append(parsed)
        except json.JSONDecodeError:
            pass

    return {
        "result": f"Found {len(valid_jsons)} JSON objects",
        "json_objects": valid_jsons
    }
```

## Edge Cases and Considerations

1. **Type Mismatches**: When sheet data types don't match function parameter types
   - Solution: Implement automatic type conversion based on parameter annotations

2. **Missing Parameters**: When required function parameters aren't available in the row
   - Solution: Log warnings and provide clear error messages

3. **Function Errors**: When delegate functions raise exceptions
   - Solution: Capture and format errors consistently, with detailed logging

4. **Large Return Values**: When functions return large data structures
   - Solution: Implement truncation or compression for large values

5. **Column Creation**: When functions need to create new columns dynamically
   - Solution: Support dynamic column creation with proper validation

6. **Dependency Management**: When delegate functions have external dependencies
   - Solution: Provide a mechanism to specify and install dependencies

7. **State Management**: When functions need to maintain state between calls
   - Solution: Implement a state management system for stateful functions

8. **Performance Concerns**: When functions are computationally expensive
   - Solution: Implement caching and parallel execution strategies

9. **Debugging Support**: When functions need debugging
   - Solution: Provide detailed logging and debugging information

10. **Version Control**: When function definitions change over time
    - Solution: Implement versioning for function definitions

## Conclusion

Adding delegate columns to the gsheet-model-breaker-demo would significantly expand its capabilities beyond LLM processing. By allowing users to execute custom Python functions directly from the spreadsheet, the tool would become much more versatile and powerful for complex data processing tasks.

The proposed implementation provides a flexible framework for defining, registering, and executing delegate functions, with support for various parameter types, multiple outputs, and error handling. It integrates seamlessly with the existing sheet structure while adding the necessary configuration options for delegate-specific settings.

This feature would position the gsheet-model-breaker-demo as a comprehensive platform for both LLM-based and traditional algorithmic data processing, all within the familiar interface of a Google Sheet.

## Regression Tests

1. **DO**: Delegate columns should be identified by the "py-delegate" marker in the column reference row, distinguishing them from "py-llm" columns.

2. **DO**: The function name specified in the prompt template row should be correctly loaded from the function registry.

3. **DO**: Delegate functions should receive the correct parameters from the row data, with appropriate type conversion based on parameter annotations.

4. **DO**: When a delegate function returns a dictionary, the values should be correctly mapped to their respective output columns as specified in the flags.

5. **DO**: Delegate functions should be able to access values from previously processed columns, including both input columns and output columns from earlier steps.

6. **DO**: Errors in delegate functions should be properly caught, logged, and displayed in the output cell with a clear error message.

7. **DON'T**: Delegate functions should NOT have access to modify the sheet directly - they should only return values that are then written back to the sheet by the main process.

8. **DON'T**: The implementation should NOT break the existing functionality of LLM columns - both delegate and LLM columns should work side by side in the same sheet.

9. **DO**: Custom functions defined in the _functions sheet should be correctly loaded and executed, with proper validation and error handling.

10. **DON'T**: Potentially dangerous operations (like file system access, network calls, or code execution) should NOT be allowed in custom functions defined in the sheet, unless explicitly permitted through a secure sandbox.
