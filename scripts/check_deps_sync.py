#!/usr/bin/env python3
"""
Dependency Sync Checker

This script checks if dependencies in pyproject.toml and requirements.txt are in sync.
It reports any discrepancies and suggests fixes.

Usage:
    uv run scripts/check_deps_sync.py [--fix]

Options:
    --fix    Automatically fix discrepancies by updating requirements.txt
"""

import argparse
import os
import re
import sys
from dataclasses import dataclass
from typing import List, Tuple

# Use tomllib (built-in for Python 3.11+) or fall back to tomli
try:
    import tomllib
except ImportError:
    try:
        import tomli as tomllib
    except ImportError:
        print("Error: tomli package is required for Python < 3.11")
        print("Install it with: uv pip install tomli")
        sys.exit(1)


@dataclass
class Dependency:
    """Represents a dependency with name and version specification."""
    name: str
    version_spec: str

    def __str__(self) -> str:
        return f"{self.name}{self.version_spec}"

    def __eq__(self, other: object) -> bool:
        if not isinstance(other, Dependency):
            return False
        return self.name == other.name and self.version_spec == other.version_spec


def parse_pyproject_toml(file_path: str) -> Tuple[List[Dependency], List[Dependency]]:
    """
    Parse pyproject.toml and extract production and development dependencies.

    Returns:
        Tuple containing:
        - List of production dependencies
        - List of development dependencies
    """
    try:
        with open(file_path, "rb") as f:
            pyproject_data = tomllib.load(f)

        prod_dependencies = []
        dev_dependencies = []

        # Get production dependencies from [project] section
        if "project" in pyproject_data and "dependencies" in pyproject_data["project"]:
            for dep in pyproject_data["project"]["dependencies"]:
                # Handle comments in dependency lines
                dep = dep.split("#")[0].strip()
                if not dep:
                    continue

                # Parse dependency name and version
                match = re.match(r"([a-zA-Z0-9_\-\.]+)([<>=~^]+[^#\s]+)?", dep)
                if match:
                    name, version_spec = match.groups()
                    prod_dependencies.append(Dependency(name, version_spec or ""))

        # Get development dependencies from [tool.uv] section
        if "tool" in pyproject_data and "uv" in pyproject_data["tool"] and "dev-dependencies" in pyproject_data["tool"]["uv"]:
            for dep in pyproject_data["tool"]["uv"]["dev-dependencies"]:
                # Handle comments in dependency lines
                dep = dep.split("#")[0].strip()
                if not dep:
                    continue

                # Parse dependency name and version
                match = re.match(r"([a-zA-Z0-9_\-\.]+)([<>=~^]+[^#\s]+)?", dep)
                if match:
                    name, version_spec = match.groups()
                    dev_dependencies.append(Dependency(name, version_spec or ""))

        # Print debug info
        print(f"Found {len(prod_dependencies)} production dependencies in pyproject.toml")
        for dep in prod_dependencies:
            print(f"  - {dep}")

        print(f"Found {len(dev_dependencies)} development dependencies in pyproject.toml")
        for dep in dev_dependencies:
            print(f"  - {dep}")

        return prod_dependencies, dev_dependencies
    except Exception as e:
        print(f"Error parsing pyproject.toml: {e}")
        return [], []


def parse_requirements_txt(file_path: str) -> List[Dependency]:
    """Parse requirements.txt and extract dependencies."""
    try:
        dependencies = []

        with open(file_path, "r") as f:
            for line in f:
                # Skip comments and empty lines
                line = line.split("#")[0].strip()
                if not line:
                    continue

                # Parse dependency name and version
                match = re.match(r"([a-zA-Z0-9_\-\.]+)([<>=~^]+[^#\s]+)?", line)
                if match:
                    name, version_spec = match.groups()
                    dependencies.append(Dependency(name, version_spec or ""))

        # Print debug info
        print(f"Found {len(dependencies)} dependencies in requirements.txt")
        for dep in dependencies:
            print(f"  - {dep}")

        return dependencies
    except Exception as e:
        print(f"Error parsing requirements.txt: {e}")
        return []


def check_version_spec_format(deps: List[Dependency], exceptions: List[str] = None) -> List[Dependency]:
    """
    Check if dependencies use >= instead of == version specifier.

    Args:
        deps: List of dependencies to check
        exceptions: List of dependency names that are allowed to use == specifier

    Returns:
        List of dependencies that need to be updated to use >= specifier
    """
    if exceptions is None:
        exceptions = []

    needs_update = []
    for dep in deps:
        if dep.name.lower() not in [e.lower() for e in exceptions] and dep.version_spec.startswith("=="):
            # Create a new dependency with >= instead of ==
            new_version_spec = ">=" + dep.version_spec[2:]
            needs_update.append(Dependency(dep.name, new_version_spec))

    return needs_update


def compare_dependencies(
    pyproject_deps: List[Dependency],
    requirements_deps: List[Dependency]
) -> Tuple[List[Dependency], List[Dependency], List[Tuple[Dependency, Dependency]]]:
    """
    Compare dependencies from pyproject.toml and requirements.txt.

    Returns:
        Tuple containing:
        - Dependencies in pyproject.toml but not in requirements.txt
        - Dependencies in requirements.txt but not in pyproject.toml
        - Dependencies with version mismatches (pyproject_dep, requirements_dep)
    """
    pyproject_deps_dict = {dep.name.lower(): dep for dep in pyproject_deps}
    requirements_deps_dict = {dep.name.lower(): dep for dep in requirements_deps}

    # Find dependencies in pyproject.toml but not in requirements.txt
    missing_in_requirements = [
        dep for name, dep in pyproject_deps_dict.items()
        if name not in requirements_deps_dict
    ]

    # Find dependencies in requirements.txt but not in pyproject.toml
    missing_in_pyproject = [
        dep for name, dep in requirements_deps_dict.items()
        if name not in pyproject_deps_dict
    ]

    # Find version mismatches
    version_mismatches = []
    for name, pyproject_dep in pyproject_deps_dict.items():
        if name in requirements_deps_dict:
            requirements_dep = requirements_deps_dict[name]
            if pyproject_dep.version_spec != requirements_dep.version_spec:
                version_mismatches.append((pyproject_dep, requirements_dep))

    return missing_in_requirements, missing_in_pyproject, version_mismatches


def update_requirements_txt(
    file_path: str,
    missing_deps: List[Dependency],
    version_mismatches: List[Tuple[Dependency, Dependency]],
    remove_deps: List[Dependency] = None
) -> bool:
    """
    Update requirements.txt with missing dependencies, fix version mismatches, and remove dependencies.

    Args:
        file_path: Path to requirements.txt
        missing_deps: Dependencies to add
        version_mismatches: Dependencies with version mismatches to fix
        remove_deps: Dependencies to remove

    Returns:
        True if file was updated, False otherwise
    """
    try:
        # Read current content
        with open(file_path, "r") as f:
            lines = f.readlines()

        # Fix version mismatches
        for pyproject_dep, req_dep in version_mismatches:
            for i, line in enumerate(lines):
                if line.strip().startswith(req_dep.name):
                    lines[i] = f"{pyproject_dep}\n"
                    break

        # Remove dependencies if specified
        if remove_deps:
            remove_dep_names = [dep.name for dep in remove_deps]
            lines = [line for line in lines if not any(line.strip().startswith(name) for name in remove_dep_names)]

        # Add missing dependencies
        for dep in missing_deps:
            lines.append(f"{dep}\n")

        # Write updated content
        with open(file_path, "w") as f:
            f.writelines(lines)

        return True
    except Exception as e:
        print(f"Error updating requirements.txt: {e}")
        return False


def check_dev_requirements_file(project_root: str) -> str:
    """
    Check if dev-requirements.txt exists, and create it if it doesn't.

    Returns:
        Path to dev-requirements.txt
    """
    dev_requirements_path = os.path.join(project_root, "dev-requirements.txt")

    if not os.path.exists(dev_requirements_path):
        print(f"Creating {dev_requirements_path} as it doesn't exist...")
        with open(dev_requirements_path, "w") as f:
            f.write("# Development dependencies\n")

    return dev_requirements_path


def main() -> int:
    """Main function."""
    parser = argparse.ArgumentParser(description="Check if dependencies in pyproject.toml and requirements.txt are in sync")
    parser.add_argument("--fix", action="store_true", help="Automatically fix discrepancies")
    parser.add_argument("--dev", action="store_true", help="Also check development dependencies")
    parser.add_argument("--check-format", action="store_true", help="Check if dependencies use >= instead of == version specifier")
    parser.add_argument("--exceptions", nargs="+", help="List of dependencies that are allowed to use == specifier")
    args = parser.parse_args()

    # Get project root directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(script_dir)

    # Parse dependency files
    pyproject_path = os.path.join(project_root, "pyproject.toml")
    requirements_path = os.path.join(project_root, "requirements.txt")

    # Parse pyproject.toml for both prod and dev dependencies
    pyproject_prod_deps, pyproject_dev_deps = parse_pyproject_toml(pyproject_path)

    # Parse requirements.txt for prod dependencies
    requirements_deps = parse_requirements_txt(requirements_path)

    # Compare production dependencies
    missing_in_requirements, missing_in_pyproject, version_mismatches = compare_dependencies(
        pyproject_prod_deps, requirements_deps
    )

    # Check version specifier format if requested
    format_issues_prod = []
    format_issues_dev = []
    if args.check_format:
        exceptions = args.exceptions or []
        format_issues_prod = check_version_spec_format(pyproject_prod_deps, exceptions)
        if args.dev:
            format_issues_dev = check_version_spec_format(pyproject_dev_deps, exceptions)

    # Report results for production dependencies
    has_prod_issues = False

    if missing_in_requirements:
        has_prod_issues = True
        print("\n🔍 Production dependencies in pyproject.toml but missing in requirements.txt:")
        for dep in missing_in_requirements:
            print(f"  - {dep}")

    if missing_in_pyproject:
        has_prod_issues = True
        print("\n🔍 Dependencies in requirements.txt but missing in pyproject.toml:")
        for dep in missing_in_pyproject:
            print(f"  - {dep}")

    if version_mismatches:
        has_prod_issues = True
        print("\n🔍 Production dependencies with version mismatches:")
        for pyproject_dep, req_dep in version_mismatches:
            print(f"  - {pyproject_dep.name}: pyproject.toml={pyproject_dep.version_spec}, requirements.txt={req_dep.version_spec}")

    if format_issues_prod:
        has_prod_issues = True
        print("\n🔍 Production dependencies using == instead of >= version specifier:")
        for dep in format_issues_prod:
            print(f"  - {dep.name}: should be {dep.version_spec} instead of =={dep.version_spec[2:]}")

    # Check development dependencies if requested
    has_dev_issues = False
    if args.dev and pyproject_dev_deps:
        dev_requirements_path = check_dev_requirements_file(project_root)
        dev_requirements_deps = parse_requirements_txt(dev_requirements_path)

        # Compare development dependencies
        missing_in_dev_requirements, missing_in_dev_pyproject, dev_version_mismatches = compare_dependencies(
            pyproject_dev_deps, dev_requirements_deps
        )

        if missing_in_dev_requirements:
            has_dev_issues = True
            print("\n🔍 Development dependencies in pyproject.toml but missing in dev-requirements.txt:")
            for dep in missing_in_dev_requirements:
                print(f"  - {dep}")

        if missing_in_dev_pyproject:
            has_dev_issues = True
            print("\n🔍 Dependencies in dev-requirements.txt but missing in pyproject.toml dev-dependencies:")
            for dep in missing_in_dev_pyproject:
                print(f"  - {dep}")

        if dev_version_mismatches:
            has_dev_issues = True
            print("\n🔍 Development dependencies with version mismatches:")
            for pyproject_dep, req_dep in dev_version_mismatches:
                print(f"  - {pyproject_dep.name}: pyproject.toml={pyproject_dep.version_spec}, dev-requirements.txt={req_dep.version_spec}")

        if format_issues_dev:
            has_dev_issues = True
            print("\n🔍 Development dependencies using == instead of >= version specifier:")
            for dep in format_issues_dev:
                print(f"  - {dep.name}: should be {dep.version_spec} instead of =={dep.version_spec[2:]}")

        # Fix development dependency issues if requested
        if has_dev_issues and args.fix:
            print("\n🔧 Fixing discrepancies in dev-requirements.txt...")

            # Update pyproject.toml if there are format issues
            if format_issues_dev and args.check_format:
                print("🔧 Updating format in pyproject.toml for development dependencies...")
                # This would require updating the pyproject.toml file
                # For now, we'll just print a message
                print("⚠️ Automatic format fixing in pyproject.toml is not implemented yet.")
                print("⚠️ Please manually update the following dependencies to use >= instead of ==:")
                for dep in format_issues_dev:
                    print(f"  - {dep.name}: change to {dep.version_spec}")

            if update_requirements_txt(dev_requirements_path, missing_in_dev_requirements, dev_version_mismatches, missing_in_dev_pyproject):
                print("✅ dev-requirements.txt updated successfully!")
            else:
                print("❌ Failed to update dev-requirements.txt")
                return 1

    # Fix production dependency issues if requested
    if has_prod_issues:
        if args.fix:
            print("\n🔧 Fixing discrepancies in requirements.txt...")

            # Update pyproject.toml if there are format issues
            if format_issues_prod and args.check_format:
                print("🔧 Updating format in pyproject.toml for production dependencies...")
                # This would require updating the pyproject.toml file
                # For now, we'll just print a message
                print("⚠️ Automatic format fixing in pyproject.toml is not implemented yet.")
                print("⚠️ Please manually update the following dependencies to use >= instead of ==:")
                for dep in format_issues_prod:
                    print(f"  - {dep.name}: change to {dep.version_spec}")

            if update_requirements_txt(requirements_path, missing_in_requirements, version_mismatches, missing_in_pyproject):
                print("✅ requirements.txt updated successfully!")
            else:
                print("❌ Failed to update requirements.txt")
                return 1
        else:
            print("\n💡 Run with --fix to automatically update requirements.txt")
            return 1

    # Overall status
    has_issues = has_prod_issues or has_dev_issues
    if has_issues:
        if not args.fix:
            print("\n💡 Run with --fix to automatically update requirement files")
            if args.check_format and (format_issues_prod or format_issues_dev):
                print("⚠️ Note: Automatic format fixing in pyproject.toml is not implemented yet.")
                print("⚠️ You will need to manually update dependencies to use >= instead of ==.")
            return 1
    else:
        if args.check_format:
            if not format_issues_prod and (not format_issues_dev or not args.dev):
                print("\n✅ All dependencies are in sync and using the correct version specifier format!")
            else:
                print("\n✅ All dependencies are in sync!")
                print("⚠️ But some dependencies are using == instead of >= version specifier.")
                print("⚠️ Run with --fix to see which ones need to be updated.")
                return 1
        else:
            print("\n✅ All dependencies are in sync!")

    return 0


if __name__ == "__main__":
    sys.exit(main())
