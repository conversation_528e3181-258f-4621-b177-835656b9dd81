"""
Main entry point for running the LLMProcessor with various configurations.
"""

import sys
import os
import time
import pandas as pd
from dotenv import load_dotenv
from loguru import logger
from colorama import Fore, Style, init
import multiprocessing as mp
import re

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from llm_processor.processor import ParallelLLMDataFrameProcessor
from llm_processor.chain_step import ChainStep
from llm_processor.google_sheets_utils import (
    get_data_from_sheet,
    get_sheet_id_from_url,
    update_to_sheet,
)

# Initialize colorama
init(autoreset=True)
load_dotenv()
logger.remove()
logger.add(
    sys.stderr,
    format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <dim><normal>{name}</normal>:<normal>{function}</normal>:<normal>{line}</normal></dim>: \n ----> <level>{message}</level>",
    level="INFO",
)

URL_PATH = "https://docs.google.com/spreadsheets/d/1qtbhjCczRQsKywxQfHwhJzeTd3D5702j6an80kPIGyI/edit?gid=0#gid=0"
max_workers = mp.cpu_count() - 1


def fill_template(row, template, input_cols):
    """
    Fill template with values from a DataFrame row.

    Args:
        row: DataFrame row with input values
        template: String template with {variable} placeholders
        input_cols: List of column names to use for variable replacement

    Returns:
        Filled template with all variables replaced
    """
    for col in input_cols:
        if col in row:
            template = template.replace("{{" + col + "}}", str(row[col]))
        else:
            # Replace with empty string if column doesn't exist
            template = template.replace("{{" + col + "}}", "")

    # Find any remaining template variables and replace them with empty strings
    remaining_vars = re.findall(r"\{\{([^}]+)\}\}", template)
    for var in remaining_vars:
        template = template.replace("{{" + var + "}}", "")

    return template


def main():
    """Main execution function with enhanced quality checks."""
    sheet_id = get_sheet_id_from_url(URL_PATH)
    logger.info(f"{Fore.GREEN}Sheet ID: {sheet_id}{Style.RESET_ALL}")

    # Define the sheet name to use
    sheet_name = "Sheet1"

    # Get data from sheets
    df = get_data_from_sheet(sheet_id, range_name=sheet_name)
    if len(df) == 0:
        logger.info("No prompts to generate")
        sys.exit(0)

    logger.info(
        f"{Fore.CYAN}Debug: DataFrame loaded with shape {df.shape}{Style.RESET_ALL}"
    )

    # Print the first few rows to check structure
    logger.info(
        f"{Fore.CYAN}Debug: First few rows of df:\n{df.head()}{Style.RESET_ALL}"
    )

    df_col_ref = df.iloc[[0]]
    # find the column reference for the prompt input
    prompt_input_cols = [
        key
        for key, value in df_col_ref.to_dict("list").items()
        if "inputs" in value
    ]
    # find the column reference for the prompt output
    prompt_output_cols = [
        key
        for key, value in df_col_ref.to_dict("list").items()
        if "py-llm" in value
    ]

    logger.info(
        f"{Fore.CYAN}Debug: Input columns: {prompt_input_cols}{Style.RESET_ALL}"
    )
    logger.info(
        f"{Fore.CYAN}Debug: Output columns: {prompt_output_cols}{Style.RESET_ALL}"
    )

    prompt_templates = list(
        df.iloc[[3]][prompt_output_cols].to_dict("records")[0].values()
    )
    models = list(
        df.iloc[[4]][prompt_output_cols].to_dict("records")[0].values()
    )
    temperatures = list(
        map(
            float,
            df.iloc[[5]][prompt_output_cols].to_dict("records")[0].values(),
        )
    )
    max_output_tokens = list(
        map(
            int,
            df.iloc[[6]][prompt_output_cols].to_dict("records")[0].values(),
        )
    )
    flags = list(
        df.iloc[[7]][prompt_output_cols].to_dict("records")[0].values()
    )

    prompt_inputs = (
        df[prompt_input_cols]
        .iloc[19:]
        .replace("", pd.NA)
        .dropna(how="all", ignore_index=True)
    )
    prompt_inputs["col_ref"] = (
        df["col_ref"]
        .iloc[19:]
        .reset_index(drop=True)
        .iloc[: len(prompt_inputs)]
    )

    # Check that col_ref was properly extracted
    logger.info(
        f"{Fore.CYAN}Debug: First few col_ref values: {prompt_inputs['col_ref'].head()}{Style.RESET_ALL}"
    )

    # Create a direct mapping to store LLM results for Google Sheets update
    direct_update_map = {}  # {(row_idx, col_name): (col_ref, generated_value)}

    # Get current values for prompt_output_cols to identify rows with "<generate>" tag
    # Also prepopulate output columns with existing values for chain dependency
    for output_col in prompt_output_cols:
        col_data = (
            df[output_col]
            .iloc[19:]
            .reset_index(drop=True)
            .iloc[: len(prompt_inputs)]
        )
        prompt_inputs[f"original_{output_col}"] = col_data

        # Check for presence of "<generate>" tags
        generate_count = (col_data == "<generate>").sum()
        logger.info(
            f"{Fore.CYAN}Debug: Found {generate_count} rows with '<generate>' for column {output_col}{Style.RESET_ALL}"
        )

        # Log the exact values from a sample of rows
        sample_rows = col_data[col_data == "<generate>"].head()
        if not sample_rows.empty:
            logger.info(
                f"{Fore.CYAN}Debug: Sample rows with '<generate>' for {output_col}: {sample_rows.index.tolist()}{Style.RESET_ALL}"
            )

        # Initialize output column with existing values (excluding "<generate>" tags)
        prompt_inputs[output_col] = col_data.apply(
            lambda x: "" if x == "<generate>" else x
        )

    # Dictionary to track which rows need updates for which columns
    rows_to_update = {col: [] for col in prompt_output_cols}

    # Process columns in sequence to handle dependencies between columns
    for idx, (
        prompt_template,
        model,
        temperature,
        max_tokens,
        flags,
        output_col,
    ) in enumerate(
        zip(
            prompt_templates,
            models,
            temperatures,
            max_output_tokens,
            flags,
            prompt_output_cols,
        )
    ):
        # Create filled_template column for this prompt template
        template_col = f"filled_template_{output_col}"

        # Apply the fill_template function to create a new column with filled templates
        # Include all previously processed output columns as potential input variables
        current_input_cols = prompt_input_cols.copy()

        # Add any output columns processed before this one as potential input variables
        for prev_idx in range(idx):
            prev_col = prompt_output_cols[prev_idx]
            if (
                prev_col not in current_input_cols
                and prev_col in prompt_inputs.columns
            ):
                current_input_cols.append(prev_col)

        # Fill templates with available variables
        prompt_inputs[template_col] = prompt_inputs.apply(
            lambda row: fill_template(
                row, prompt_template, current_input_cols
            ),
            axis=1,
        )
        logger.info(
            f"{Fore.CYAN}Created template column: {template_col}{Style.RESET_ALL}"
        )

        # Find rows that have "<generate>" tag for this output column
        generate_mask = prompt_inputs[f"original_{output_col}"] == "<generate>"

        # Debug the mask to see if it's finding the rows
        logger.info(
            f"{Fore.CYAN}Debug: Generate mask for {output_col} has {generate_mask.sum()} matches{Style.RESET_ALL}"
        )

        # Double-check by directly searching the column values
        direct_check = prompt_inputs[f"original_{output_col}"].apply(
            lambda x: str(x).strip() == "<generate>"
        )
        logger.info(
            f"{Fore.CYAN}Debug: Direct check for {output_col} has {direct_check.sum()} matches{Style.RESET_ALL}"
        )

        # If there's a discrepancy, log some values for debugging
        if generate_mask.sum() != direct_check.sum():
            logger.warning(
                f"{Fore.YELLOW}Discrepancy in '<generate>' detection for {output_col}. Checking values...{Style.RESET_ALL}"
            )
            for i, val in enumerate(
                prompt_inputs[f"original_{output_col}"].head(10)
            ):
                logger.info(
                    f"{Fore.CYAN}Row {i}, value: '{val}', len: {len(str(val))}, is '<generate>'?: {str(val).strip() == '<generate>'}{Style.RESET_ALL}"
                )

            # Use the more permissive check
            generate_mask = direct_check

        if not generate_mask.any():
            logger.info(
                f"{Fore.YELLOW}No rows with '<generate>' tag found for {output_col}. Skipping.{Style.RESET_ALL}"
            )
            continue

        generate_indices = generate_mask[generate_mask].index.tolist()
        rows_to_update[output_col] = generate_indices

        logger.info(
            f"{Fore.CYAN}Debug: Generate indices for {output_col}: {generate_indices}{Style.RESET_ALL}"
        )

        # Filter to only process rows with "<generate>" tag
        chain_input = prompt_inputs.loc[generate_indices].copy()

        if len(chain_input) == 0:
            logger.info(
                f"{Fore.YELLOW}No rows to process for {output_col} after filtering.{Style.RESET_ALL}"
            )
            continue

        logger.info(
            f"{Fore.GREEN}Processing {len(chain_input)} rows for {output_col} with '<generate>' tag.{Style.RESET_ALL}"
        )

        # Debug some of the inputs
        if not chain_input.empty:
            logger.info(
                f"{Fore.CYAN}Debug: First row template for {output_col}: {chain_input[template_col].iloc[0]}{Style.RESET_ALL}"
            )

        # Process data
        llm_proc = ParallelLLMDataFrameProcessor(
            def_model=model,
            def_temperature=temperature,
            def_max_tokens=max_tokens,
            def_async_rate_limit=10,
            def_thread_rate_limit=5,
        )

        # Set the template column as the source for the prompt
        chain = [
            ChainStep(
                pt=template_col,  # Use the template column as source
                model=model,
                temperature=temperature,
                max_tokens=max_tokens,
                col=output_col,
            ),
        ]

        start_time = time.time()
        result_df = llm_proc.execute_chain(
            chain_input,
            chain,
            max_attempts=3,
        )
        logger.info(f"Processing time: {time.time() - start_time:.2f} seconds")

        # Debug the results
        logger.info(
            f"{Fore.CYAN}Debug: Result DataFrame has {len(result_df)} rows{Style.RESET_ALL}"
        )
        if not result_df.empty:
            logger.info(
                f"{Fore.CYAN}Debug: First result: {result_df[output_col].iloc[0]}{Style.RESET_ALL}"
            )

        # DIRECTLY STORE results in our update map
        for idx_in_result in range(len(result_df)):
            try:
                result_row_idx = result_df.index[idx_in_result]
                result_value = result_df.iloc[idx_in_result][output_col]

                # Get the col_ref value for this row
                col_ref_value = None

                # Try multiple ways to get the col_ref value
                try:
                    # Method 1: Try direct .loc access
                    col_ref_value = chain_input.loc[result_row_idx, "col_ref"]
                except Exception as e:
                    logger.warning(
                        f"{Fore.YELLOW}Error accessing col_ref via .loc: {str(e)}{Style.RESET_ALL}"
                    )
                    try:
                        # Method 2: Try .iloc if we have numeric indices
                        if isinstance(result_row_idx, int):
                            col_ref_value = chain_input.iloc[result_row_idx][
                                "col_ref"
                            ]
                    except Exception as e:
                        logger.warning(
                            f"{Fore.YELLOW}Error accessing col_ref via .iloc: {str(e)}{Style.RESET_ALL}"
                        )
                        try:
                            # Method 3: Try direct dictionary-style access
                            col_ref_value = chain_input.at[
                                result_row_idx, "col_ref"
                            ]
                        except Exception as e:
                            logger.warning(
                                f"{Fore.YELLOW}Error accessing col_ref via .at: {str(e)}{Style.RESET_ALL}"
                            )

                # If we still don't have a col_ref, look it up in the original data
                if col_ref_value is None:
                    try:
                        # Find the same index in the original prompt_inputs
                        if result_row_idx in prompt_inputs.index:
                            col_ref_value = prompt_inputs.loc[
                                result_row_idx, "col_ref"
                            ]
                            logger.info(
                                f"{Fore.CYAN}Found col_ref {col_ref_value} from prompt_inputs{Style.RESET_ALL}"
                            )
                    except Exception as e:
                        logger.warning(
                            f"{Fore.YELLOW}Error getting col_ref from prompt_inputs: {str(e)}{Style.RESET_ALL}"
                        )

                # As a last resort, if we still don't have a col_ref, use the row index + 1
                # This is risky but better than losing the result
                if col_ref_value is None:
                    col_ref_value = (
                        int(result_row_idx) + 1
                    )  # Add 1 since col_ref is 1-based
                    logger.warning(
                        f"{Fore.YELLOW}Using fallback col_ref value: {col_ref_value} (row index + 1){Style.RESET_ALL}"
                    )

                # Only store non-empty values
                if pd.notna(result_value) and str(result_value).strip() != "":
                    logger.info(
                        f"{Fore.GREEN}Direct storage: row {result_row_idx}, col_ref {col_ref_value}, col {output_col}: '{result_value}'{Style.RESET_ALL}"
                    )

                    # Store in direct map for Google Sheets update
                    # Use a string representation of row_idx to avoid any issues with indexes
                    map_key = (str(result_row_idx), output_col)
                    direct_update_map[map_key] = (
                        col_ref_value,
                        str(result_value),
                    )

                    # Also try to store in DataFrames for template use
                    try:
                        prompt_inputs.at[result_row_idx, output_col] = str(
                            result_value
                        )
                    except:
                        pass
                else:
                    logger.warning(
                        f"{Fore.YELLOW}Empty result for row {result_row_idx}, not storing{Style.RESET_ALL}"
                    )
            except Exception as e:
                logger.error(
                    f"{Fore.RED}Error storing result at index {idx_in_result}: {str(e)}{Style.RESET_ALL}"
                )
                import traceback

                logger.error(
                    f"{Fore.RED}Traceback: {traceback.format_exc()}{Style.RESET_ALL}"
                )

        if output_col not in prompt_input_cols:
            prompt_input_cols.append(output_col)

    # After all processing, prepare the update dataframe directly from our map
    if direct_update_map:
        logger.info(
            f"{Fore.GREEN}Found {len(direct_update_map)} values to update in Google Sheets{Style.RESET_ALL}"
        )

        # Create a clean DataFrame for updates
        update_rows = []
        for (row_idx, col_name), (col_ref, value) in direct_update_map.items():
            logger.info(
                f"{Fore.CYAN}Adding to update: row {row_idx}, col_ref {col_ref}, col {col_name}, value: '{value}'{Style.RESET_ALL}"
            )
            update_rows.append({"col_ref": col_ref, col_name: value})

        # Create DataFrame and make sure every row has entries for all columns
        update_df = pd.DataFrame(update_rows)

        # Ensure all prompt_output_cols exist in the DataFrame
        for col in prompt_output_cols:
            if col not in update_df.columns:
                update_df[col] = ""

        logger.info(
            f"{Fore.GREEN}Update DataFrame created with {len(update_df)} rows{Style.RESET_ALL}"
        )

        # Group by col_ref to consolidate updates for the same row
        if len(update_df) > 1:
            update_df = update_df.groupby("col_ref").first().reset_index()
            logger.info(
                f"{Fore.CYAN}After grouping by col_ref, update DataFrame has {len(update_df)} rows{Style.RESET_ALL}"
            )

        # Print what we're updating
        for idx, row in update_df.iterrows():
            cols_with_values = [
                col
                for col in prompt_output_cols
                if col in row
                and pd.notna(row[col])
                and str(row[col]).strip() != ""
            ]
            if cols_with_values:
                logger.info(
                    f"{Fore.CYAN}Row with col_ref={row['col_ref']}: Updating columns {cols_with_values}{Style.RESET_ALL}"
                )
                for col in cols_with_values:
                    logger.info(
                        f"{Fore.CYAN}  Value for {col}: '{row[col]}'{Style.RESET_ALL}"
                    )

        # Directly update Google Sheets
        update_to_sheet(update_df, prompt_output_cols, sheet_id, sheet_name)
    else:
        logger.warning(
            f"{Fore.YELLOW}No valid LLM results found to update in Google Sheets.{Style.RESET_ALL}"
        )


if __name__ == "__main__":
    main()
