#!/usr/bin/env python3
"""
Script to run tests with coverage and open the HTML report.
"""

import os
import sys
import subprocess
import webbrowser
import argparse
from pathlib import Path

# Add the parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def main():
    """Run tests with coverage and open the HTML report."""
    # Parse arguments
    parser = argparse.ArgumentParser(description="Run tests with coverage and open HTML report")
    parser.add_argument("--erase", action="store_true", help="Erase previous coverage data before running")
    args = parser.parse_args()

    # Get project root directory
    project_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

    if args.erase:
        print("\n🧹 Erasing previous coverage data...\n")
        subprocess.run([sys.executable, "-m", "coverage", "erase"], cwd=project_dir)

    print("\n🔍 Running tests with coverage...\n")

    # Run the tests with coverage
    run_script = Path(project_dir) / "demo" / "tests" / "run_tests.py"
    cmd = [sys.executable, str(run_script), "--coverage"]

    result = subprocess.run(cmd, cwd=project_dir)

    if result.returncode != 0:
        print("\n❌ Tests failed! Fix issues before reviewing coverage.")
        return 1

    # Path to the coverage HTML report
    report_path = Path(project_dir) / "demo" / "tests" / "reports" / "htmlcov" / "index.html"

    if report_path.exists():
        print(f"\n✅ Opening coverage report at: {report_path}")
        webbrowser.open(f"file://{report_path.absolute()}")
    else:
        print(f"\n❌ Coverage report not found at: {report_path}")
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(main())
