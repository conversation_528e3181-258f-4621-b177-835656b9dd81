{"rules": {"142ef710-4c95-4e78-8d2e-3005bd4548d5": {"name": "Parallel Data Processing Insights  \n", "rule": {"type": "regex", "regex": "\\basyncExecution\\b", "ignoreCase": false}, "actions": {"ide": {"doc_id": "swm/run-pipeline-overview", "message": "When a dev is working on parallel processing in `demo.main`, I want them to find this document because it outlines the key functions for processing data in parallel."}}}}, "view_order": ["142ef710-4c95-4e78-8d2e-3005bd4548d5"]}