# main_config.py
from typing import Dict, List, Optional

# TODO: logger

# NOTE: Opted against putting convig in .env to avoid it being in two places.


class CFG:
    """Central configuration class for the LLM Agent Excel application.
    This class contains all configuration values grouped by purpose.
    Frequently changed values are at the top for easy access.
    """

    # ---------------------- FREQUENTLY CHANGED VALUES ----------------------
    # Source configuration
    # SHEET_NAME = "AF Earnings-Smrzr1"
    SHEET_NAME = "AF_Quant_Probes"
    GSHEET_URL = "https://docs.google.com/spreadsheets/d/1stpE5iThDtF8hCz_S6HlGHRHDKfkPKHwKfQnOA9qARg"
    EXCEL_PATH = "/Users/<USER>/Documents/gsheet-model-breaker-demo-may-6/data/1stpE5iThDtF8hCz_S6HlGHRHDKfkPKHwKfQnOA9qARg_Sheet1.xlsx"
    DEBUG = False

    # Set the source type to use (gsheet or excel)
    PROC_GSHEET = True  # if false, will not process gsheet. Will use GSHEET_URL and SHEET_NAME. If both True will process both.
    PROC_EXCEL = False  # if false, will not process excel file. Will use EXCEL_PATH and SHEET_NAME. If both True will process both.
    # ---------------------- LLM MODEL CONFIGURATION ----------------------
    # Default model configuration
    DEFAULT_MODEL: str = "gpt-4o"
    DEFAULT_TEMPERATURE: float = 0.7
    DEFAULT_MAX_OUTPUT_TOKENS: int = 1000
    DEFAULT_FLAGS: str = ""

    # ---------------------- MODEL SPECIFIC CONFIGURATION ----------------------
    # Maps model names to their specific parameter names for max tokens
    MAX_TOKENS_PARAM_MAP: Dict[str, str] = {
        "default": "max_tokens",  # Default parameter name
        "o1": "max_completion_tokens",  # Specific parameter for 'o1' model
        "o3": "max_completion_tokens",  # Specific parameter for 'o3' model
        # Add other models with different parameter names here if needed
    }

    # Maps model names to their specific parameter names for temperature
    # None means the model doesn't support temperature
    TEMPERATURE_PARAM_MAP: Dict[str, Optional[str]] = {
        "default": "temperature",  # Default parameter name
        "o1": None,  # 'o1' model doesn't support temperature
        "o3": None,  # 'o3' model doesn't support temperature
        # Add other models with different parameter names here if needed
    }

    # Maps model names to their specific parameter names for reasoning effort
    REASONING_EFFORT_PARAM_MAP: Dict[str, Optional[str]] = {
        "default": None,  # Default parameter name (not used for most models)
        "o1": "reasoning_effort",  # Specific parameter for 'o1' model
        # Add other models with different parameter names here if needed
    }

    # Default reasoning effort value (only used for models that support it)
    DEFAULT_REASONING_EFFORT: str = "medium"  # Options: "low", "medium", "high"

    # ---------------------- SHEET STRUCTURE CONFIGURATION ----------------------
    # Sheet structure constants - TODO: Move to name-based config instead of row numbers - SHOULD BE DONE, CHECK.
    DEFAULT_CONFIG_ROW: int = 3  # WARNING: THIS SHOULD NOT BEING USED. IF IT IS, ERROR.
    DEFAULT_PROMPT_ROW: int = 4  # WARNING: THIS SHOULD NOT BEING USED. IF IT IS, ERROR.
    DEFAULT_DATA_START_ROW: int = 20  # This value is very important. It splits the sheet between config rows and data rows.
    
    # Error handling configuration
    FAIL_ON_NO_GENERATION_TAGS: bool = False  # If True, exit with error when no generation tags found. If False, show warning and continue.

    # ---------------------- GENERATION MARKERS ----------------------
    # Tags that trigger generation in output columns  # NOTE These are the tags that trigger that cell to be processed / regnerated by LLM/Delegate.
    GENERATION_TAGS: List[str] = ["<generate>", "//", "<g>", "generate"]

    # ---------------------- CONFIGURATION ROW DETECTION ----------------------
    # Configuration row names in column A
    CONFIG_NAMES: Dict[str, str] = {
        "TEMPLATE": "TEMPLATE",
        "MODEL": "MODEL",
        "TEMPERATURE": "TEMPERATURE",
        "MAX_TOKENS": "MAX_OUTPUT_TOKENS",
        "FLAGS": "FLAGS",
        "REASONING_EFFORT": "REASONING_EFFORT",
    }
    # Map of common configuration row names (alternative spellings that may be used for the same config row name).
    # Should be Case Insensitive.
    CONFIG_ROW_MAPPING: Dict[str, List[str]] = {
        "template": ["prompt_template"],  # , "template", "templates"],
        "model": ["model", "models", "llm_model"],
        "temperature": ["temperature", "temp", "temperatures"],
        "max_output_tokens": ["max_tokens", "max_token", "maxtokens", "tokens", "max_output_tokens"],
        "flags": ["flags", "flag", "options"],
        "reasoning_effort": ["reasoning_effort", "reasoning", "effort", "thinking"],
    }

    # ---------------------- HELPER METHODS ----------------------
    @classmethod
    def get_source_path(cls) -> str:
        """Return the path/URL to the data source based on configuration."""
        if cls.PROC_GSHEET:
            return cls.GSHEET_URL
        else:
            return cls.EXCEL_PATH

    @classmethod
    def get_source_type(cls) -> str:
        """Return the type of data source."""
        return "gsheet" if cls.PROC_GSHEET else "excel"

    @classmethod
    def get_gsheet_config(cls):
        """Return a dictionary with Google Sheet configuration."""
        return {
            "gsheet_url": cls.GSHEET_URL,
            "sheet_name": cls.SHEET_NAME,
        }

    @classmethod
    def get_excel_config(cls):
        """Return a dictionary with Excel configuration."""
        return {
            "excel_path": cls.EXCEL_PATH,
            "sheet_name": cls.SHEET_NAME,
        }
