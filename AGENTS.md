# Usage instructions for Codex

## Dependency installation
Run `scripts/codex_setup.sh` to install dependencies from the pre-downloaded
wheelhouse. The setup script fails if `wheelhouse/` is missing.

## Linting
Use `make lint` before committing changes. This calls `uv run ruff check .`.

## Testing
After any code modifications, run `make test` (or `python demo/tests/run_tests.py`).
All tests must pass.

## Misc
Avoid creating temporary files unless necessary—prefix them with `.temp_` if
needed. New tests should be placed in `demo/tests/`.
