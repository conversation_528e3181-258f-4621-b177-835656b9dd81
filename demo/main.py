"""
Main entry point for running the LLMProcessor with various configurations.

This script supports:
1. LLM processing with prompt templates (py-llm columns)
2. Python function execution via LLM (py-func columns)
"""

import os
import sys
import time
import pandas as pd
from dotenv import load_dotenv
from pathlib import Path
import multiprocessing as mp
import traceback
from main_config import CFG

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
# Import from original llm_processor
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Import processors and utilities
from demo.utils.config import logger, DATA_DIR
from demo.utils.file_utils import is_google_sheets_url
from demo.utils.arg_parser import parse_arguments
from demo.utils.display import (
    display_panel,
    display_skipped_columns,
    display_column_scanning,
    display_processing_status,
    display_llm_processing_info,
    display_llm_processing_complete,
    display_processing_summary,
)
from demo.utils.sheet_utils import extract_sheet_id
from demo.utils.data_loader import load_data
from demo.utils.update_utils import process_updates
from demo.utils.column_matcher import (
    analyze_and_suggest_matches,
)

# Use configuration directly from CFG
DEFAULT_MODEL = CFG.DEFAULT_MODEL
DEFAULT_TEMPERATURE = CFG.DEFAULT_TEMPERATURE
DEFAULT_MAX_OUTPUT_TOKENS = CFG.DEFAULT_MAX_OUTPUT_TOKENS
DEFAULT_FLAGS = CFG.DEFAULT_FLAGS
DEFAULT_CONFIG_ROW = CFG.DEFAULT_CONFIG_ROW
DEFAULT_PROMPT_ROW = CFG.DEFAULT_PROMPT_ROW
DEFAULT_DATA_START_ROW = CFG.DEFAULT_DATA_START_ROW
GENERATION_TAGS = CFG.GENERATION_TAGS

from demo.processors.template_processor import fill_template
from demo.processors.py_func_processor import (
    extract_function_params,
)
from demo.processors.llm_processor import is_generation_tag, process_llm_column
from demo.processors.column_config_processor import (
    extract_column_configurations,
    extract_data_rows,
)

from llm_processor.processor import ParallelLLMDataFrameProcessor
from llm_processor.chain_step import ChainStep


# Load environment variables
load_dotenv()

# Create data directory if it doesn't exist
os.makedirs(DATA_DIR, exist_ok=True)


def process_python_function_column(
    prompt_inputs,
    output_col,
    function_template,
    model,
    temperature,
    max_tokens,
    generate_indices,
    direct_update_map,
    available_input_cols,
):
    """
    Process a Python function column.

    Args:
        prompt_inputs: DataFrame with input data
        output_col: Column name to process
        function_template: Python function template
        model: LLM model to use
        temperature: Temperature setting for the model
        max_tokens: Maximum tokens for generation
        generate_indices: Indices of rows to process
        direct_update_map: Dictionary to store results
        available_input_cols: List of available input columns

    Returns:
        tuple: (Updated direct_update_map, updated available_input_cols)
    """
    # Import re module at the function level to ensure it's available
    import re

    # Process all rows in parallel
    try:
        # Filter to just the rows we need to process
        rows_to_process = prompt_inputs.loc[generate_indices].copy()

        # Create a DataFrame to store prompts for all rows
        prompts_df = pd.DataFrame(index=rows_to_process.index)
        prompts_df["prompt"] = ""  # Initialize empty prompt column

        # Track errors for reporting
        param_errors = []

        # Generate prompts for all rows
        for idx in generate_indices:
            row = prompt_inputs.loc[idx]

            # Extract parameters for this row
            params = extract_function_params(function_template, row, available_input_cols)

            if not params:
                error_msg = f"No parameters could be extracted for row {idx}, function execution may fail"
                logger.warning(error_msg)
                param_errors.append((idx, error_msg))

            # Format parameters as string
            params_str = "\n".join([f"{k}={repr(v)}" for k, v in params.items()])

            # Create LLM prompt for executing the function
            prompt = f"""
            You are a Python function execution engine. Execute the following Python function with the provided inputs.
            Return ONLY the result of the function execution without explanation, commentary, or code.

            Python function:
            ```python
            {function_template}
            ```

            Input parameters:
            ```python
            {params_str}
            ```

            Execute the function with these inputs and return ONLY the result.
            """

            # Store in the prompts DataFrame
            prompts_df.at[idx, "prompt"] = prompt

            # Also store the col_ref for later mapping back to results
            prompts_df.at[idx, "col_ref"] = row["col_ref"]

        # If there were parameter extraction errors, show a summary
        if param_errors:
            error_summary = "\n".join([f"Row {idx}: {msg}" for idx, msg in param_errors])
            display_panel(
                f"[yellow]Warning:[/] Parameter extraction issues detected:\n{error_summary}",
                title="Parameter Extraction Issues",
                style="yellow",
            )

            # For each error, provide suggestions for possible column matches
            suggestions_provided = []

            for idx, _ in param_errors:
                row = prompt_inputs.loc[idx]

                # Extract parameter names from the function template
                func_signature_match = re.search(
                    r"def\s+([a-zA-Z0-9_]+)\s*\((.*?)\)",
                    function_template,
                    re.DOTALL,
                )

                if func_signature_match and func_signature_match.lastindex >= 2:
                    param_string = func_signature_match.group(2)
                    param_names = [p.strip().split("=")[0].strip() for p in param_string.split(",") if p.strip()]

                    for param in param_names:
                        if param not in available_input_cols:
                            # Analyze possible matches
                            matches = analyze_and_suggest_matches(param, available_input_cols, top_n=3)
                            if matches:
                                match_text = ", ".join([f"'{m[0]}' ({m[1]:.2f})" for m in matches])
                                suggestions_provided.append(f"Parameter '{param}' → Suggestions: {match_text}")

            if suggestions_provided:
                display_panel(
                    "[blue]Column matching suggestions:[/]\n" + "\n".join(suggestions_provided),
                    title="Matching Suggestions",
                    style="blue",
                )

        display_panel(
            f"Executing Python functions via LLM in parallel\n"
            f"Model: [blue]{model}[/]\n"
            f"Temperature: [yellow]{temperature}[/]\n"
            f"Max tokens: [magenta]{max_tokens}[/]\n"
            f"Function: [green]{output_col}[/]\n"
            f"Rows to process: [yellow]{len(generate_indices)}[/]",
            title="Parallel Python Function Execution",
            style="blue",
        )

        # Set up the parallel processor
        llm_proc = ParallelLLMDataFrameProcessor(
            def_model=model,
            def_temperature=temperature,
            def_max_tokens=max_tokens,
            def_async_rate_limit=5,  # Process up to 5 requests in parallel
            def_thread_rate_limit=5,
        )

        # Set up the processing chain
        chain = [
            ChainStep(
                pt="prompt",  # Source column with prompts
                model=model,
                temperature=temperature,
                max_tokens=max_tokens,
                col="result",  # Target column for results
            ),
        ]

        # Process all rows in parallel
        start_time = time.time()
        result_df = llm_proc.execute_chain(prompts_df, chain)
        processing_time = time.time() - start_time

        # Update the original DataFrame and direct_update_map with results
        rows_processed = len(generate_indices)
        rows_successful = 0

        # Process results
        for idx in result_df.index:
            try:
                result = result_df.loc[idx, "result"]

                # Clean up the result - remove any code blocks, backticks, etc.
                result = re.sub(r"```.*?```", "", result, flags=re.DOTALL)
                result = re.sub(r"`(.*?)`", r"\1", result)
                result = result.strip()

                # Store the result in prompt_inputs so it can be used by subsequent columns
                prompt_inputs.at[idx, output_col] = result

                # Get col_ref for this row
                col_ref = result_df.loc[idx, "col_ref"]

                # Add to direct update map for later saving
                direct_update_map[(str(idx), output_col)] = (
                    col_ref,
                    str(result),
                )
                rows_successful += 1

                logger.debug(f"Python function result for row {idx}: {result[:50]}{'...' if len(result) > 50 else ''}")
            except Exception as e:
                logger.error(f"Error processing result for row {idx}: {str(e)}")

        # Show summary
        display_panel(
            f"Python function processing complete\n"
            f"Processed: [green]{rows_successful}[/] of [blue]{rows_processed}[/] rows\n"
            f"Success rate: [yellow]{(rows_successful / max(1, rows_processed)) * 100:.1f}%[/]\n"
            f"Processing time: [magenta]{processing_time:.2f}[/] seconds",
            title="Processing Complete",
            style="green",
        )

        # After processing this column, add it to input_columns so it can be used in templates
        # for subsequent columns
        if output_col not in available_input_cols:
            available_input_cols.append(output_col)
            logger.info(f"Added {output_col} to available input columns for subsequent processing")

    except Exception as e:
        logger.error(f"Error batch processing Python functions: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        display_panel(
            f"Error executing Python functions in parallel\nError: [red]{str(e)}[/]",
            title="Execution Error",
            style="red",
        )

    return direct_update_map, available_input_cols


def process_llm_generation_column(
    prompt_inputs,
    output_col,
    template_col,
    model,
    temperature,
    max_tokens,
    generate_indices,
    direct_update_map,
    available_input_cols,
):
    """
    Process an LLM generation column.

    Args:
        prompt_inputs: DataFrame with input data
        output_col: Column name to process
        template_col: Column name containing filled templates
        model: LLM model to use
        temperature: Temperature for generation
        max_tokens: Maximum tokens for generation
        generate_indices: Indices of rows to process
        direct_update_map: Dictionary to store results
        available_input_cols: List of available input columns

    Returns:
        tuple: (Updated direct_update_map, updated available_input_cols)
    """
    try:
        logger.info(f"Processing LLM column: {output_col}")
        logger.info(f"Model: {model}, Temperature: {temperature}, Max tokens: {max_tokens}")

        # Create a Rich panel to show processing details
        display_llm_processing_info(output_col, model, temperature, max_tokens, len(generate_indices))

        # Call process_llm_column with error handling
        start_time = time.time()
        direct_update_map = process_llm_column(
            prompt_inputs=prompt_inputs,
            output_col=output_col,
            template_col=template_col,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            generate_indices=generate_indices,
            direct_update_map=direct_update_map,
        )
        processing_time = time.time() - start_time

        # Create a success panel with Rich
        processed_rows = sum(1 for k, _ in direct_update_map.items() if k[1] == output_col)
        display_llm_processing_complete(output_col, processed_rows, len(generate_indices), processing_time)

        # After processing this column, add it to available_input_cols so it can be used in templates
        # for subsequent columns
        if output_col not in available_input_cols:
            available_input_cols.append(output_col)
            logger.debug(f"Adding {output_col} to available input columns for subsequent processing")

    except Exception as e:
        logger.error(f"Error processing LLM column {output_col}: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        display_panel(
            f"Error processing column {output_col}\n{str(e)}",
            title="LLM Processing Error",
            style="red",
        )

    return direct_update_map, available_input_cols


def process_data(df, file_path, sheet_name, regenerate_all, model_config=None):
    """
    Process the data from a DataFrame with LLM.

    Args:
        df: DataFrame with data to process
        file_path: Path to input file
        sheet_name: Sheet name
        regenerate_all: Whether to regenerate all values
        model_config: Dictionary containing model configuration
    """
    # Create data directory if it doesn't exist
    os.makedirs(DATA_DIR, exist_ok=True)

    # Track skipped columns for summary
    skipped_columns = []

    if regenerate_all:
        logger.info("Regenerate all flag is set. Will regenerate all values regardless of generation tags.")

    # Create a Rich panel to display supported generation tags
    display_panel(
        "Supported generation tags: [green]<generate>[/], [green]//[/], [green]<g>[/], [green]generate[/] (exact matches only)",
        title="Configuration",
        style="blue",
    )

    # Determine if the file path is a Google Sheets URL
    is_gsheet = is_google_sheets_url(file_path)

    # Set local_file_path based on source
    if is_gsheet:
        sheet_id = extract_sheet_id(file_path)
        local_file_path = DATA_DIR / f"{sheet_id}_{sheet_name}.xlsx"
    else:
        local_file_path = Path(file_path)

    logger.debug(f"DataFrame loaded with shape {df.shape}")

    # Validate sheet structure and required columns
    required_columns = ["col_ref"]
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        logger.error(f"Required columns missing from sheet: {missing_columns}")
        display_panel(
            f"Required columns missing from sheet: {', '.join(missing_columns)}",
            title="Sheet Structure Error",
            style="red",
        )
        sys.exit(1)

    # Print the first few rows to check structure
    logger.debug(f"First few rows of df:\n{df.head()}")

    # Extract column configurations
    config = extract_column_configurations(df)

    # Display info about column processing order
    display_panel(
        f"Processing [bold]{len(config['all_output_cols'])}[/] columns in left-to-right order\n"
        f"Columns: [green]{', '.join(config['all_output_cols'])}[/]",
        title="Column Processing",
        style="blue",
    )

    # Extract data rows
    prompt_inputs = extract_data_rows(df, config)

    # Create a list of all columns that could be used as input variables.
    # Initially include all columns from the DataFrame.
    available_input_cols = list(df.columns)
    logger.debug(f"Initial available input columns (all columns): {available_input_cols}")

    # Dictionary to track which rows need updates for which columns
    rows_to_update = {col: [] for col in config["prompt_output_cols"]}

    # Dictionary to store direct updates
    direct_update_map = {}

    # Apply default values from model_config for any py-func columns with missing values
    if model_config:
        # Apply default values to Python function columns
        for col in config.get("py_func_models", {}):
            if config["py_func_models"][col] == "":
                config["py_func_models"][col] = model_config.get("default_model", "gpt-4o")
                logger.info(f"Using default model from config for py-func column {col}: {config['py_func_models'][col]}")

        for col in config.get("py_func_temperatures", {}):
            if config["py_func_temperatures"][col] == 0:
                config["py_func_temperatures"][col] = model_config.get("default_temperature", 0.7)
                logger.info(f"Using default temperature from config for py-func column {col}: {config['py_func_temperatures'][col]}")

        for col in config.get("py_func_max_output_tokens", {}):
            if config["py_func_max_output_tokens"][col] == 0:
                config["py_func_max_output_tokens"][col] = model_config.get("default_max_output_tokens", 1000)
                logger.info(f"Using default max tokens from config for py-func column {col}: {config['py_func_max_output_tokens'][col]}")

        # Apply default values to LLM columns
        for i, model in enumerate(config.get("models", [])):
            if model == "":
                config["models"][i] = model_config.get("default_model", "gpt-4o")
                logger.info(f"Using default model from config for LLM column {config['prompt_output_cols'][i]}: {config['models'][i]}")

        for i, temp in enumerate(config.get("temperatures", [])):
            if temp == 0:
                config["temperatures"][i] = model_config.get("default_temperature", 0.7)
                logger.info(f"Using default temperature from config for LLM column {config['prompt_output_cols'][i]}: {config['temperatures'][i]}")

        for i, tokens in enumerate(config.get("max_output_tokens", [])):
            if tokens == 0:
                config["max_output_tokens"][i] = model_config.get("default_max_output_tokens", 1000)
                logger.info(
                    f"Using default max tokens from config for LLM column {config['prompt_output_cols'][i]}: {config['max_output_tokens'][i]}"
                )

    # Display a panel showing which columns are being scanned
    display_column_scanning(len(config["all_output_cols"]))

    # Process all columns in their original left-to-right order
    for col_idx, output_col in enumerate(config["all_output_cols"]):
        col_type = config["column_type_map"].get(output_col)
        logger.debug(f"Processing column {col_idx + 1}/{len(config['all_output_cols'])}: {output_col} (type: {col_type})")

        if col_type == "py-func":
            # Process Python function column
            function_template = config["py_func_templates"][output_col]
            logger.debug(f"Python function template: {function_template}")

            # Get model, temperature, and max_tokens from config
            model = config["py_func_models"][output_col]
            temperature = config["py_func_temperatures"][output_col]
            max_tokens = config["py_func_max_output_tokens"][output_col]

            logger.debug(f"Using model for py-func column {output_col}: {model}, temperature: {temperature}, max_tokens: {max_tokens}")

            # Save original column values for reference and to check for generation tags
            # Convert 1-indexed row number to 0-indexed for iloc
            data_start_idx = DEFAULT_DATA_START_ROW - 1
            col_data = df[output_col].iloc[data_start_idx:].reset_index(drop=True).iloc[: len(prompt_inputs)]
            prompt_inputs[f"original_{output_col}"] = col_data

            # Determine which rows need processing
            if regenerate_all:
                # If regenerate_all is True, create a mask for all rows
                generate_mask = pd.Series([True] * len(prompt_inputs), index=prompt_inputs.index)
                logger.debug(f"Regenerate all flag is set: processing all {len(generate_mask)} rows for {output_col}")
            else:
                # Otherwise, only process rows with generation tags
                # Make sure we reset the index to match prompt_inputs
                generate_mask = prompt_inputs[f"original_{output_col}"].apply(is_generation_tag)
                logger.debug(f"Generate mask for {output_col} has {generate_mask.sum()} matches")

            # Get indices of rows to process
            generate_indices = generate_mask[generate_mask].index.tolist()

            if not generate_indices:
                logger.debug(f"No rows to process for Python function column {output_col}. Skipping.")
                # Add to skipped columns list
                skipped_columns.append(output_col)
                # Add this column to available input cols for subsequent columns
                if output_col not in available_input_cols and output_col in prompt_inputs.columns:
                    available_input_cols.append(output_col)
                continue

            logger.info(f"Processing {len(generate_indices)} rows for column {output_col}")

            # Process Python function column
            direct_update_map, available_input_cols = process_python_function_column(
                prompt_inputs=prompt_inputs,
                output_col=output_col,
                function_template=function_template,
                model=model,
                temperature=temperature,
                max_tokens=max_tokens,
                generate_indices=generate_indices,
                direct_update_map=direct_update_map,
                available_input_cols=available_input_cols,
            )

        elif col_type == "py-llm":
            # Process LLM column
            idx = config["prompt_output_cols"].index(output_col)
            prompt_template = config["prompt_templates"][idx]
            model = config["models"][idx]
            temperature = config["temperatures"][idx]
            max_tokens = config["max_output_tokens"][idx]
            # Flag is available but not currently used
            # flag = config["flags"][idx] if idx < len(config["flags"]) else None

            logger.debug(f"Processing LLM column {idx + 1}/{len(config['prompt_output_cols'])}: {output_col}")

            # Create filled_template column for this prompt template
            template_col = f"filled_template_{output_col}"

            # Include all previously processed output columns as potential input variables
            current_input_cols = available_input_cols.copy()

            logger.debug(f"Available input columns for {output_col}: {current_input_cols}")

            # Apply the fill_template function to create a new column with filled templates
            template_results = prompt_inputs.apply(
                lambda row: fill_template(row, prompt_template, current_input_cols),
                axis=1,
            )

            # Extract the filled template and missing variable information
            prompt_inputs[template_col] = template_results.apply(lambda x: x["filled_template"])

            # Find rows that have generation tags for this output column or process all rows if regenerate_all is True
            if regenerate_all:
                # If regenerate_all is True, create a mask for all rows
                generate_mask = pd.Series([True] * len(prompt_inputs), index=prompt_inputs.index)
                logger.debug(f"Regenerate all flag is set: processing all {len(generate_mask)} rows for {output_col}")

                # Make sure the column is empty so values will be regenerated
                if output_col in prompt_inputs.columns:
                    prompt_inputs[output_col] = ""
                    logger.debug(f"Clearing all values for column {output_col} due to --regenerate-all flag")
            else:
                # Otherwise, only process rows with generation tags
                # First make sure we have the original column data to check
                original_col = f"original_{output_col}"
                if original_col not in prompt_inputs.columns:
                    # If the original column doesn't exist, we can't check for generation tags
                    # Create it from the source DataFrame to get the actual values
                    logger.debug(f"Creating {original_col} from source data for {output_col}")
                    # Convert 1-indexed row number to 0-indexed for iloc
                    data_start_idx = DEFAULT_DATA_START_ROW - 1
                    col_data = df[output_col].iloc[data_start_idx:].reset_index(drop=True).iloc[: len(prompt_inputs)]
                    prompt_inputs[original_col] = col_data

                # Now check for generation tags using the local is_generation_tag function
                generate_mask = prompt_inputs[original_col].apply(is_generation_tag)

                # Log the number of matches that need processing
                match_count = generate_mask.sum()
                logger.debug(f"Generate mask for {output_col} has {match_count} matches")

                # Display a clear Rich format message about processing status only if there are rows to process
                display_processing_status(output_col, match_count)

            if not generate_mask.any():
                logger.debug(f"No rows to process for {output_col}. Skipping.")
                # Add to skipped columns list
                skipped_columns.append(output_col)
                # Still add the column to available_input_cols
                if output_col not in available_input_cols:
                    available_input_cols.append(output_col)
                continue

            generate_indices = generate_mask[generate_mask].index.tolist()
            rows_to_update[output_col] = generate_indices

            # Process LLM column
            direct_update_map, available_input_cols = process_llm_generation_column(
                prompt_inputs=prompt_inputs,
                output_col=output_col,
                template_col=template_col,
                model=model,
                temperature=temperature,
                max_tokens=max_tokens,
                generate_indices=generate_indices,
                direct_update_map=direct_update_map,
                available_input_cols=available_input_cols,
            )

    # Process updates to Google Sheets or local file
    if is_gsheet:
        sheet_id = extract_sheet_id(file_path)
    else:
        sheet_id = None

    update_result = process_updates(
        df=df,
        direct_update_map=direct_update_map,
        all_output_cols=config["all_output_cols"],
        is_gsheet=is_gsheet,
        local_file_path=local_file_path,
        sheet_id=sheet_id,
        sheet_name=sheet_name,
    )

    # Create a summary table for the process
    display_processing_summary(
        file_path=local_file_path,
        sheet_name=sheet_name,
        processed_columns=config["all_output_cols"],
        updates_made=update_result["updates_made"] if update_result["processed"] else None,
        gsheet_updated=update_result["gsheet_updated"],
        is_gsheet=is_gsheet,
    )

    # Display a summary of skipped columns if any
    display_skipped_columns(skipped_columns)


def main(config=None):
    """
    Main entry point for the application.
    """

    if config:
        # Create a mock args object using values from config
        class MockArgs:
            pass

        args = MockArgs()

        # Use Google Sheets URL or Excel path based on config flags
        if hasattr(config, "PROC_GSHEET") and config.PROC_GSHEET and hasattr(config, "GSHEET_URL"):
            args.file_path = config.GSHEET_URL
        elif hasattr(config, "PROC_EXCEL") and config.PROC_EXCEL and hasattr(config, "EXCEL_PATH"):
            args.file_path = config.EXCEL_PATH
        else:
            raise ValueError("Config must have either PROC_GSHEET=True with GSHEET_URL or PROC_EXCEL=True with EXCEL_PATH")

        # Set other parameters from config
        args.sheet_name = config.SHEET_NAME if hasattr(config, "SHEET_NAME") else "Sheet1"
        args.regenerate_all = config.REGENERATE_ALL if hasattr(config, "REGENERATE_ALL") else False
        args.debug = config.DEBUG if hasattr(config, "DEBUG") else False
    else:
        # Parse command-line arguments
        args = parse_arguments()

    # Configure logging based on debug flag
    if args.debug:
        log_level = "DEBUG"
        debug_msg = "Debug mode enabled - showing detailed logs"
    else:
        log_level = "INFO"  # Default to INFO level
        debug_msg = ""

    # Configure logger
    logger.remove()  # Remove default handler
    logger.add(
        sys.stderr,
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>",
        level=log_level,
    )

    # Display debug mode status if enabled
    if args.debug:
        display_panel(debug_msg, title="Debug Mode", style="yellow")

    start_time = time.time()


    file_path = args.file_path
    sheet_name = args.sheet_name
    regenerate_all = args.regenerate_all

    logger.debug(f"Arguments: file_path={file_path}, sheet_name={sheet_name}, regenerate_all={regenerate_all}, debug={args.debug}")

    # Set up a simple multiprocessing pool for concurrent processing
    if mp.get_start_method(allow_none=True) != "spawn":
        mp.set_start_method("spawn", force=True)

    # Load data from Google Sheets or local file
    # We only need the DataFrame here, other return values are used in process_data
    df, _, _, _ = load_data(file_path, sheet_name)

    # Process the data with model settings from config if available
    if config and hasattr(config, "DEFAULT_MODEL") and hasattr(config, "DEFAULT_TEMPERATURE") and hasattr(config, "DEFAULT_MAX_OUTPUT_TOKENS"):
        # Create a model config dictionary to pass default values from CONFIG
        model_config = {
            "default_model": config.DEFAULT_MODEL,
            "default_temperature": config.DEFAULT_TEMPERATURE,
            "default_max_output_tokens": config.DEFAULT_MAX_OUTPUT_TOKENS,
        }
        process_data(df, file_path, sheet_name, regenerate_all, model_config=model_config)
    else:
        # Use the centralized constants from demo/utils/constants.py
        model_config = {
            "default_model": DEFAULT_MODEL,
            "default_temperature": DEFAULT_TEMPERATURE,
            "default_max_output_tokens": DEFAULT_MAX_OUTPUT_TOKENS,
        }
        process_data(df, file_path, sheet_name, regenerate_all, model_config=model_config)

    end_time = time.time()
    logger.success(f"Total processing time: {end_time - start_time:.2f} seconds")


if __name__ == "__main__":
    main()
