"""
Utilities for updating Google Sheets or local files with processed data.
"""

import traceback
import pandas as pd
from .config import logger
from .file_utils import save_df_to_file
from .display import display_panel

from llm_processor.google_sheets_utils import update_to_sheet


def apply_updates_to_local_file(
    df, update_df, all_output_cols, local_file_path
):
    """
    Apply updates from the update_df to the original DataFrame and save to a local file.

    Args:
        df: Original DataFrame
        update_df: DataFrame with updates to apply
        all_output_cols: List of output columns to update
        local_file_path: Path to save the updated DataFrame

    Returns:
        int: Number of cells updated
    """
    try:
        logger.info(f"Updating local file: {local_file_path}")

        # Apply updates to the original DataFrame
        updated_cells = 0
        for idx, row in update_df.iterrows():
            col_ref_value = row["col_ref"]
            try:
                # Find the corresponding row index in the original DataFrame
                df_row_idx = (
                    int(col_ref_value) + 19
                )  # Convert to 0-indexed and add offset

                # Apply each column update
                for col in all_output_cols:
                    if (
                        col in row
                        and pd.notna(row[col])
                        and str(row[col]).strip() != ""
                    ):
                        if col in df.columns:
                            # Update the row at the calculated index
                            df.at[df_row_idx, col] = row[col]
                            updated_cells += 1
                        else:
                            logger.warning(
                                f"Column {col} not found in original DataFrame"
                            )
            except Exception as e:
                logger.error(
                    f"Error updating row with col_ref {col_ref_value}: {str(e)}"
                )

        # Save the updated DataFrame back to the original file path
        logger.info(f"Saving changes back to: {local_file_path}")
        save_df_to_file(df, local_file_path)

        # Create a success message with Rich
        display_panel(
            f"Local file updated successfully!\n"
            f"File: [blue]{local_file_path}[/]\n"
            f"Cells updated: {updated_cells}",
            title="Local File Update",
            style="green",
        )

        return updated_cells

    except Exception as e:
        logger.error(f"Error updating local file: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        display_panel(
            f"Failed to update local file\n{str(e)}",
            title="File Update Error",
            style="red",
        )
        return 0


def update_google_sheet(update_df, all_output_cols, sheet_id, sheet_name):
    """
    Update a Google Sheet with the processed data.

    Args:
        update_df: DataFrame with updates to apply
        all_output_cols: List of output columns to update
        sheet_id: Google Sheet ID
        sheet_name: Sheet name to update

    Returns:
        bool: True if the update was successful, False otherwise
    """
    try:
        logger.info("Updating remote Google Sheet")

        # Create a Rich panel to show updating status
        display_panel(
            f"Updating Google Sheet\n"
            f"Sheet ID: [blue]{sheet_id}[/]\n"
            f"Sheet name: [blue]{sheet_name}[/]\n"
            f"Columns to update: {', '.join(all_output_cols)}\n"
            f"Rows to update: {len(update_df)}",
            title="Google Sheets Update",
            style="blue",
        )

        # Use the imported update_to_sheet function directly
        update_to_sheet(update_df, all_output_cols, sheet_id, sheet_name)

        # Show success message
        display_panel(
            f"Successfully updated Google Sheet!\n"
            f"Sheet ID: [blue]{sheet_id}[/]\n"
            f"Sheet name: [blue]{sheet_name}[/]",
            title="Google Sheets Update Complete",
            style="green",
        )
        logger.success("Remote Google Sheet updated successfully")
        return True

    except Exception as e:
        logger.error(f"Error updating Google Sheet: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        display_panel(
            f"Failed to update Google Sheet\n{str(e)}",
            title="Google Sheets Error",
            style="red",
        )
        return False


def process_updates(
    df,
    direct_update_map,
    all_output_cols,
    is_gsheet,
    local_file_path,
    sheet_id=None,
    sheet_name=None,
):
    """
    Process updates to Google Sheets or local file based on the direct_update_map.

    Args:
        df: Original DataFrame
        direct_update_map: Dictionary mapping (row_idx, col_name) to (col_ref, value)
        all_output_cols: List of output columns
        is_gsheet: Boolean indicating if the source is a Google Sheet
        local_file_path: Path to the local file
        sheet_id: Google Sheet ID (if is_gsheet is True)
        sheet_name: Sheet name (if is_gsheet is True)

    Returns:
        dict: Summary of the update process
    """
    # Create result summary
    result = {
        "processed": False,
        "updates_made": 0,
        "file_updated": False,
        "gsheet_updated": False,
    }

    if not direct_update_map:
        logger.warning("No valid LLM results found to update.")
        return result

    # Create a clean DataFrame for updates
    update_rows = []
    for (row_idx, col_name), (col_ref, value) in direct_update_map.items():
        logger.debug(
            f"Adding to update: row {row_idx}, col_ref {col_ref}, col {col_name}, value: '{value[:50]}{'...' if len(value) > 50 else ''}'"
        )
        update_rows.append({"col_ref": col_ref, col_name: value})

    # Create DataFrame and make sure every row has entries for all columns
    update_df = pd.DataFrame(update_rows)

    # Ensure all output columns exist in the DataFrame
    for col in all_output_cols:
        if col not in update_df.columns:
            update_df[col] = ""

    # Group by col_ref to consolidate updates for the same row
    if len(update_df) > 1:
        update_df = update_df.groupby("col_ref").first().reset_index()

    # Update the local file
    cells_updated = apply_updates_to_local_file(
        df, update_df, all_output_cols, local_file_path
    )
    result["updates_made"] = cells_updated
    result["file_updated"] = cells_updated > 0
    result["processed"] = True

    # Directly update Google Sheets if the input was a Google Sheets URL
    if is_gsheet and sheet_id and sheet_name:
        result["gsheet_updated"] = update_google_sheet(
            update_df, all_output_cols, sheet_id, sheet_name
        )
    else:
        logger.info("Skipping Google Sheet update as input was a local file")

    return result
