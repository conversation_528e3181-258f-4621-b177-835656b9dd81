"""
Display utilities for rich console output.

This module contains functions for displaying formatted content using Rich.
"""

from rich.panel import Panel
from rich.table import Table
from rich import box
from rich.console import Console

# Create console instance
console = Console()


def display_panel(content, title=None, style="info"):
    """
    Display a Rich panel with the given content and styling.

    Args:
        content: The content to display inside the panel
        title: Optional title for the panel
        style: Style/color for the panel border
    """
    panel = Panel(content, title=title, border_style=style)
    console.print(panel)


def display_table(title=None, columns=None, rows=None, show_lines=False):
    """
    Display a Rich table with the given data.

    Args:
        title: Optional title for the table
        columns: List of column names or dict with name and style
        rows: List of rows, each a list of cell values
        show_lines: Whether to show lines between rows
    """
    table = Table(title=title, show_lines=show_lines, box=box.ROUNDED)

    # Add columns with optional styling
    if columns:
        for col in columns:
            if isinstance(col, dict) and "name" in col:
                name = col["name"]
                col_style = col.get("style", "default")
                table.add_column(name, style=col_style)
            else:
                table.add_column(str(col))

    # Add rows
    if rows:
        for row in rows:
            table.add_row(*[str(cell) for cell in row])

    console.print(table)


def display_skipped_columns(skipped_columns):
    """
    Display a panel with skipped columns in a simple alphabetical list.

    Args:
        skipped_columns: List of column names that were skipped
    """
    if not skipped_columns:
        return

    # Sort columns alphabetically
    sorted_columns = sorted(skipped_columns)

    # Format the columns into a comma-separated list with line breaks
    # for better readability (max 80 chars per line)
    formatted_columns = []
    current_line = ""

    for col in sorted_columns:
        if len(current_line) + len(col) + 2 > 80:  # +2 for ", "
            formatted_columns.append(current_line)
            current_line = col
        else:
            if current_line:
                current_line += ", " + col
            else:
                current_line = col

    if current_line:
        formatted_columns.append(current_line)

    # Join the lines with newlines
    skipped_text = "\n".join(formatted_columns)

    # Display the panel
    display_panel(
        f"The following columns were skipped because they had no rows with generation tags:\n\n{skipped_text}",
        title="Skipped Columns Summary",
        style="yellow",
    )


def display_column_scanning(num_columns):
    """
    Display a panel indicating that columns are being scanned.

    Args:
        num_columns: Number of columns being scanned
    """
    display_panel(
        f"Scanning [bold]{num_columns}[/] columns for generation tags...",
        title="Column Scanning",
        style="blue",
    )


def display_processing_status(column_name, num_rows):
    """
    Display a panel showing the processing status for a column.

    Args:
        column_name: Name of the column being processed
        num_rows: Number of rows to process
    """
    if num_rows > 0:
        display_panel(
            f"Found [bold green]{num_rows}[/] rows to process for column [bold blue]{column_name}[/]",
            title="Processing Status",
            style="blue",
        )


def display_llm_processing_info(column_name, model, temperature, max_tokens, num_rows):
    """
    Display a panel with LLM processing information.

    Args:
        column_name: Name of the column being processed
        model: LLM model being used
        temperature: Temperature setting
        max_tokens: Maximum tokens setting
        num_rows: Number of rows to process
    """
    display_panel(
        f"Processing column: [bold green]{column_name}[/]\n"
        f"Model: [green]{model}[/]\n"
        f"Temperature: [yellow]{temperature}[/]\n"
        f"Max tokens: [magenta]{max_tokens}[/]\n"
        f"Rows to process: [blue]{num_rows}[/]",
        title="LLM Processing Started",
        style="blue",
    )


def display_llm_processing_complete(column_name, processed_rows, total_rows, processing_time):
    """
    Display a panel showing LLM processing completion.

    Args:
        column_name: Name of the column that was processed
        processed_rows: Number of rows processed
        total_rows: Total number of rows that were to be processed
        processing_time: Time taken for processing in seconds
    """
    display_panel(
        f"Processing complete for column {column_name}!\n"
        f"Processed rows: [blue]{processed_rows}/{total_rows}[/]\n"
        f"Processing time: [blue]{processing_time:.2f} seconds[/]",
        title="LLM Processing Complete",
        style="green",
    )


def display_processing_summary(file_path, sheet_name, processed_columns, updates_made, gsheet_updated, is_gsheet):
    """
    Display a table with processing summary information.

    Args:
        file_path: Path to the processed file
        sheet_name: Name of the sheet that was processed
        processed_columns: List of columns that were processed
        updates_made: Number of updates made
        gsheet_updated: Whether Google Sheet was updated
        is_gsheet: Whether the file is a Google Sheet
    """
    columns = [
        {"name": "Category", "style": "cyan"},
        {"name": "Details", "style": "green"},
    ]

    rows = [
        ["File", str(file_path)],
        ["Sheet name", sheet_name],
        [
            "Processed columns",
            ", ".join(processed_columns) if processed_columns else "None",
        ],
        [
            "Updates made",
            str(updates_made) if updates_made else "None",
        ],
        [
            "Google Sheets sync",
            "Completed" if gsheet_updated else "Attempted" if is_gsheet else "Not applicable (local file)",
        ],
    ]

    display_table(title="Processing Summary", columns=columns, rows=rows, show_lines=True)
