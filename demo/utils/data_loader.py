"""
Utilities for loading data from Google Sheets or local files.
"""

import sys
from pathlib import Path
from typing import Tuple
import pandas as pd
from .config import logger, DATA_DIR
from .file_utils import (
    load_df_from_file,
    save_df_to_file,
    is_google_sheets_url,
)
from .sheet_utils import extract_sheet_id
from .display import display_panel

from llm_processor.google_sheets_utils import get_data_from_sheet


def load_data(file_path, sheet_name) -> Tuple[pd.DataFrame, bool, str, str]:
    """
    Load data from Google Sheets or a local file.

    Args:
        file_path: Path to the file or Google Sheets URL
        sheet_name: Sheet name to load

    Returns:
        tuple: (DataFrame, is_gsheet, local_file_path, sheet_id)
    """
    # Check if the input is a Google Sheets URL or a local file
    is_gsheet = is_google_sheets_url(file_path)
    sheet_id = None

    if is_gsheet:
        # When using Google Sheets, always fetch fresh data
        logger.info(f"Processing Google Sheet: {file_path}")
        sheet_id = extract_sheet_id(file_path)

        # Create a Rich panel to show loading status
        display_panel(
            f"Fetching data from Google Sheets\n"
            f"Sheet ID: [blue]{sheet_id}[/]\n"
            f"Sheet name: [blue]{sheet_name}[/]",
            title="Data Loading",
            style="blue",
        )

        # Get data directly from Google Sheets
        try:
            # Load fresh data directly from Google Sheets
            df = get_data_from_sheet(sheet_id, sheet_name)

            if df.empty:
                display_panel(
                    f"No data found in Google Sheet with ID {sheet_id}, sheet {sheet_name}",
                    title="Data Error",
                    style="red",
                )
                sys.exit(1)

            logger.info(
                f"Successfully loaded data from Google Sheet with {len(df)} rows"
            )

            # Cache a local copy of the sheet for reference
            local_file_path = DATA_DIR / f"{sheet_id}_{sheet_name}.xlsx"
            save_df_to_file(df, local_file_path)
            logger.info(f"Saved local backup copy to {local_file_path}")

        except Exception as e:
            display_panel(
                f"Failed to load data from Google Sheet\n{str(e)}",
                title="Google Sheets Error",
                style="red",
            )
            sys.exit(1)

    else:
        # If using a local file, load it directly
        logger.info(f"Processing local file: {file_path}")

        # Display loading status
        display_panel(
            f"Loading data from local file\n"
            f"File path: [blue]{file_path}[/]\n"
            f"Sheet name: [blue]{sheet_name}[/]",
            title="Data Loading",
            style="blue",
        )

        try:
            df = load_df_from_file(file_path, sheet_name=sheet_name)
            local_file_path = Path(file_path)

            if df.empty:
                display_panel(
                    f"No data found in local file {file_path}, sheet {sheet_name}",
                    title="Data Error",
                    style="red",
                )
                sys.exit(1)

            logger.info(
                f"Successfully loaded data from local file with {len(df)} rows"
            )
        except Exception as e:
            display_panel(
                f"Failed to load data from local file\n{str(e)}",
                title="File Error",
                style="red",
            )
            sys.exit(1)

    return df, is_gsheet, local_file_path, sheet_id
