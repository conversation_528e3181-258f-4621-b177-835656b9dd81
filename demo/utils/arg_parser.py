"""
Argument parsing utilities for command line interface.
"""

import argparse
import os
import importlib.util

# Try to import constants from run.py
try:
    # Get the path to run.py in the project root
    root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    main_path = os.path.join(root_dir, "run.py")

    if os.path.exists(main_path):
        # Load the module
        spec = importlib.util.spec_from_file_location("main_module", main_path)
        main_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(main_module)

        # Import constants from run.py
        DEFAULT_CONFIG_ROW = main_module.DEFAULT_CONFIG_ROW
        DEFAULT_PROMPT_ROW = main_module.DEFAULT_PROMPT_ROW
    else:
        # Fallback values if run.py doesn't exist
        DEFAULT_CONFIG_ROW = 3
        DEFAULT_PROMPT_ROW = 4
except Exception:
    # Fallback values
    DEFAULT_CONFIG_ROW = 3
    DEFAULT_PROMPT_ROW = 4


def parse_arguments():
    """
    Parse command line arguments.

    Note: Command line arguments take precedence over CONFIG settings in run.py.
    If command line arguments are provided, they will override any corresponding
    settings in the CONFIG class.

    Returns:
        Parsed arguments object
    """
    # Try to import CONFIG from run.py if it exists
    config_module = None
    try:
        # Get the path to run.py in the project root
        root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        main_path = os.path.join(root_dir, "run.py")

        if os.path.exists(main_path):
            # Load the module
            spec = importlib.util.spec_from_file_location("main_module", main_path)
            config_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(config_module)
    except Exception as e:
        # If there's any error, just continue without CONFIG values
        print(f"Note: Could not load CONFIG from run.py: {e}")

    # Default values
    sheet_name_default = "Sheet1"

    # Use CONFIG values if available
    if config_module and hasattr(config_module, "CONFIG"):
        config = config_module.CONFIG
        # Get sheet name from CONFIG if available
        if hasattr(config, "SHEET_NAME"):
            sheet_name_default = config.SHEET_NAME

    parser = argparse.ArgumentParser(description="Process data with LLM using Google Sheets or local files.")
    parser.add_argument(
        "--file-path",
        type=str,
        required=True,
        help="Path to input file. Can be a Google Sheets URL or a local Excel/CSV file path (required)",
    )
    parser.add_argument(
        "--sheet-name",
        type=str,
        default=sheet_name_default,
        help=f"Sheet name to process (default: {sheet_name_default})",
    )
    parser.add_argument(
        "--regenerate-all",
        action="store_true",
        help="Regenerate all values regardless of generation tags",
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug mode with detailed logging",
    )
    parser.add_argument(
        "--config-row",
        type=int,
        default=DEFAULT_CONFIG_ROW,
        help=f"Row number (1-indexed) where configuration is stored (default: {DEFAULT_CONFIG_ROW})",
    )
    parser.add_argument(
        "--prompt-row",
        type=int,
        default=DEFAULT_PROMPT_ROW,
        help=f"Row number (1-indexed) where prompt templates are stored (default: {DEFAULT_PROMPT_ROW})",
    )
    return parser.parse_args()
