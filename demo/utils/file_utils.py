"""
Utilities for file operations, including loading and saving data files.
"""

import os
import sys
import pandas as pd
from pathlib import Path
from loguru import logger
from colorama import Fore, Style
import traceback

# Make sure we have access to the parent modules
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(os.path.dirname(current_dir))
sys.path.append(parent_dir)

# Import config
from demo.utils.config import DATA_DIR


def is_google_sheets_url(path):
    """
    Check if the provided path is a Google Sheets URL.

    Args:
        path: String path to check

    Returns:
        Boolean indicating if the path is a Google Sheets URL
    """
    return (
        isinstance(path, str)
        and path.startswith("https://")
        and "docs.google.com/spreadsheets" in path
    )


def save_df_to_file(df, file_path):
    """
    Save DataFrame to file based on file extension.

    Args:
        df: DataFrame to save
        file_path: Path to save the file
    """
    try:
        # Convert to Path object
        file_path = Path(file_path)

        # Create parent directories if they don't exist
        os.makedirs(file_path.parent, exist_ok=True)

        # Get the file extension
        extension = file_path.suffix.lower()

        # Convert file path to string for pandas
        abs_path = str(file_path.absolute())

        # Save based on extension
        if extension == ".csv":
            df.to_csv(abs_path, index=False)
            logger.info(f"DataFrame saved to CSV: {abs_path}")
        elif extension in [".xlsx", ".xls"]:
            with pd.ExcelWriter(abs_path, engine="openpyxl") as writer:
                df.to_excel(writer, index=False)
            logger.info(f"DataFrame saved to Excel: {abs_path}")
        else:
            # Default to Excel if extension not recognized
            with pd.ExcelWriter(
                f"{abs_path}.xlsx", engine="openpyxl"
            ) as writer:
                df.to_excel(writer, index=False)
            logger.info(f"DataFrame saved to Excel: {abs_path}.xlsx")

        # Verify file exists
        if os.path.exists(file_path):
            logger.info("File save verified - file exists on disk")
        else:
            logger.warning(
                f"File save verification failed - file does not exist at {file_path}"
            )

    except Exception as e:
        logger.error(
            f"{Fore.RED}Error saving DataFrame to {file_path}: {str(e)}{Style.RESET_ALL}"
        )
        logger.error(
            f"{Fore.RED}Traceback: {traceback.format_exc()}{Style.RESET_ALL}"
        )
        raise


def load_df_from_file(file_path, sheet_name="Sheet1"):
    """
    Load DataFrame from file based on file extension or URL.

    Args:
        file_path: Path to load the file from (can be local file or Google Sheets URL)
        sheet_name: Name of the sheet to load (for Excel or Google Sheets)

    Returns:
        Loaded DataFrame
    """
    try:
        # Check if the path is a Google Sheets URL
        if is_google_sheets_url(file_path):
            try:
                # Extract sheet ID from URL
                import re

                match = re.search(r"/d/([a-zA-Z0-9-_]+)", file_path)
                if not match:
                    raise ValueError(f"Invalid Google Sheets URL: {file_path}")
                sheet_id = match.group(1)

                # Import the get_data_from_sheet function from Google Sheets utils
                try:
                    # Add parent directory to path to access llm_processor
                    sys.path.append(
                        os.path.dirname(
                            os.path.dirname(
                                os.path.dirname(os.path.abspath(__file__))
                            )
                        )
                    )

                    # Import the function directly
                    from llm_processor.google_sheets_utils import (
                        get_data_from_sheet,
                    )

                    # Get data from Google Sheets
                    logger.info(
                        f"Fetching data from Google Sheet: {sheet_id}, sheet: {sheet_name}"
                    )
                    df = get_data_from_sheet(sheet_id, sheet_name)

                    if df.empty:
                        logger.error(
                            f"Failed to retrieve data from Google Sheet: {sheet_id}, sheet: {sheet_name}"
                        )
                        raise ValueError(
                            "Retrieved empty DataFrame from Google Sheets"
                        )

                    # Save a backup copy for offline use
                    local_file_path = (
                        DATA_DIR / f"{sheet_id}_{sheet_name}.xlsx"
                    )
                    logger.info(f"Creating backup copy at {local_file_path}")
                    save_df_to_file(df, local_file_path)

                    return df

                except ImportError as e:
                    logger.error(
                        f"Required package missing for Google Sheets access: {e}"
                    )
                    logger.error(
                        "Please ensure llm_processor module is accessible"
                    )
                    raise
                except Exception as e:
                    logger.error(f"Error accessing Google Sheets: {str(e)}")
                    logger.error(traceback.format_exc())
                    raise

            except Exception as e:
                logger.error(f"Error loading Google Sheet: {str(e)}")
                logger.error(f"Traceback: {traceback.format_exc()}")
                raise

        # Handle local files
        file_path = Path(file_path)

        # Check if file exists
        if not file_path.exists():
            logger.error(f"File not found: {file_path}")
            raise FileNotFoundError(f"File not found: {file_path}")

        # Get the file extension
        extension = file_path.suffix.lower()

        # Convert file path to string for pandas
        abs_path = str(file_path.absolute())

        # Load based on extension
        if extension == ".csv":
            df = pd.read_csv(abs_path)
            logger.info(f"DataFrame loaded from CSV: {abs_path}")
        elif extension in [".xlsx", ".xls"]:
            df = pd.read_excel(abs_path, sheet_name=sheet_name)
            logger.info(
                f"DataFrame loaded from Excel: {abs_path}, sheet: {sheet_name}"
            )
        else:
            logger.error(f"Unsupported file format: {extension}")
            raise ValueError(
                f"Unsupported file format: {extension}. Must be .csv, .xlsx, or .xls"
            )

        return df

    except Exception as e:
        logger.error(
            f"{Fore.RED}Error loading file from {file_path}: {str(e)}{Style.RESET_ALL}"
        )
        logger.error(
            f"{Fore.RED}Traceback: {traceback.format_exc()}{Style.RESET_ALL}"
        )
        raise
