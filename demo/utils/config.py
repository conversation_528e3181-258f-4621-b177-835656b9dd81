"""
Configuration settings for the GSheet processor.
"""

import os
import sys
from pathlib import Path
from loguru import logger
from rich.console import Console
from rich.traceback import install
from colorama import Fore, Style, init  # type: ignore  # noqa: F401

# Initialize colorama
init(autoreset=True)

# Configure rich for better traceback display
install(show_locals=True)
console = Console()


class CONFIG:
    """Global configuration settings."""

    LOG_LEVEL = "INFO"
    LLM_MODEL = "gpt-4o"


# Configure Loguru
logger.remove()  # Remove default handler
logger.add(
    sys.stderr,
    format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>",
    level=CONFIG.LOG_LEVEL,
)

# Directory to store local data files - using Path object for proper path operations
DATA_DIR = Path(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))) / "data"
