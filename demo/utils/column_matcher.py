"""
Utility functions for matching column names with typos using Levenshtein distance.
This allows for more accurate fuzzy matching when users make typos in column names.
"""

from loguru import logger as log


def levenshtein_distance(s1, s2):
    """
    Calculate the Levenshtein distance between two strings.

    Args:
        s1 (str): First string
        s2 (str): Second string

    Returns:
        int: Levenshtein distance between s1 and s2
    """
    if len(s1) < len(s2):
        return levenshtein_distance(s2, s1)

    if len(s2) == 0:
        return len(s1)

    previous_row = list(range(len(s2) + 1))
    for i, c1 in enumerate(s1):
        current_row = [i + 1]
        for j, c2 in enumerate(s2):
            # Calculate insertions, deletions and substitutions
            insertions = previous_row[j + 1] + 1
            deletions = current_row[j] + 1
            substitutions = previous_row[j] + (c1 != c2)
            current_row.append(min(insertions, deletions, substitutions))
        previous_row = current_row

    return previous_row[-1]


def levenshtein_similarity(s1, s2):
    """
    Calculate the similarity score based on Levenshtein distance.
    Normalized to a value between 0 and 1, where 1 means identical strings.

    Args:
        s1 (str): First string
        s2 (str): Second string

    Returns:
        float: Similarity score between 0 and 1
    """
    if not s1 and not s2:
        return 1.0  # Both empty strings are identical
    if not s1 or not s2:
        return 0.0  # One empty string means no similarity

    distance = levenshtein_distance(s1, s2)
    max_len = max(len(s1), len(s2))
    return 1 - (distance / max_len)


def is_abbreviation(abbr, full_term):
    """
    Check if 'abbr' is a valid abbreviation of 'full_term'.
    Handles various abbreviation patterns like 'fname' for 'first_name'.

    Args:
        abbr (str): Potential abbreviation
        full_term (str): Full term to check against

    Returns:
        bool: True if abbr could be an abbreviation of full_term
    """
    # Abbreviation should be shorter
    if len(abbr) >= len(full_term):
        return False

    # Handle common abbreviation patterns

    # 1. First letter of each word or component
    # e.g., 'fn' for 'first_name', 'ln' for 'last_name'
    words = full_term.replace("_", " ").replace("-", " ").split()
    if len(abbr) == len(words):
        # Check if each letter matches first letter of each word
        if all(w[0].lower() == a.lower() for w, a in zip(words, abbr)):
            return True

    # 2. First few letters of a single word
    # e.g., 'dept' for 'department'
    if len(words) == 1 and abbr.lower() in full_term.lower():
        # Check if it's the beginning of the word or a significant part
        if (
            full_term.lower().startswith(abbr.lower())
            or abbr.lower() == full_term[: len(abbr)].lower()
        ):
            return True

    # 3. First letters of multi-word plus more letters from important words
    # e.g., 'fname' for 'first_name' (first + name)
    # e.g., 'lname' for 'last_name' (last + name)
    if len(words) > 1:
        # Try to match common patterns like 'fname', 'lname'
        if abbr.lower().startswith(words[0][0].lower()):
            rest_of_abbr = abbr.lower()[1:]

            # Check if the rest of the abbreviation matches the start of the last word
            # e.g., 'name' in 'lname' matches 'name' in 'last_name'
            if rest_of_abbr in words[-1].lower() and words[
                -1
            ].lower().startswith(rest_of_abbr):
                return True

            # Check if the rest matches the full last word
            if rest_of_abbr == words[-1].lower():
                return True

    # 4. First part of compound word
    # e.g., 'dept' for 'department', 'admin' for 'administrator'
    if full_term.lower().startswith(abbr.lower()) and len(abbr) >= 2:
        # At least 2 chars to avoid too many false positives
        return True

    # 5. Special common cases
    common_abbrs = {
        "dept": ["department"],
        "addr": ["address"],
        "num": ["number"],
        "tel": ["telephone", "phone"],
        "emp": ["employee"],
        "mgr": ["manager"],
        "admin": ["administrator"],
        "org": ["organization"],
        "corp": ["corporation"],
        "co": ["company"],
        "acct": ["account"],
        "amt": ["amount"],
        "yr": ["year"],
        "mo": ["month"],
        "wk": ["week"],
        "hr": ["hour"],
        "min": ["minute"],
        "sec": ["second"],
        "qty": ["quantity"],
        "ttl": ["total"],
        "desc": ["description"],
        "info": ["information"],
        "prof": ["professional", "professor"],
        "doc": ["document"],
        "img": ["image"],
        "pic": ["picture"],
        "fname": ["first_name", "firstname"],
        "lname": ["last_name", "lastname"],
        "fn": ["first_name"],
        "ln": ["last_name"],
    }

    # Check if the abbreviation is in our dictionary
    if abbr.lower() in common_abbrs:
        for term in common_abbrs[abbr.lower()]:
            # Check if full_term contains or matches the expanded form
            if term in full_term.lower() or full_term.lower() in term:
                return True

    # Not a recognized abbreviation pattern
    return False


def is_acronym(shorter, longer):
    """
    Check if shorter string could be an acronym of longer string
    E.g., "cn" might be an acronym for "company_name"

    Args:
        shorter (str): Potential acronym
        longer (str): Full string

    Returns:
        bool: True if shorter could be an acronym of longer
    """
    if len(shorter) > len(longer):
        return False

    # Split longer string into words
    words = longer.replace("_", " ").replace("-", " ").split()

    # Check if first letter of each word matches the acronym
    if len(shorter) == len(words):
        return all(w[0].lower() == s.lower() for w, s in zip(words, shorter))

    return False


def get_composite_field_score(param_name, available_cols):
    """
    Check if a parameter might represent a combination of fields.
    For example, 'employeeName' could be a combination of 'first_name' and 'last_name'.

    Args:
        param_name (str): Parameter name to check
        available_cols (list): Available column names

    Returns:
        tuple: (is_composite, matched_fields, score) or (False, [], 0) if not composite
    """
    # Check if the parameter might be a composite field (like 'fullName' for 'first_name'+'last_name')
    # Define specific parameter mappings for common composite fields
    param_lower = param_name.lower()

    # Special handling for 'lname' and 'lastname' - these should match 'last_name' directly
    if param_lower in ["lname", "lastname", "surname", "sname"]:
        for col in available_cols:
            if "last_name" in col.lower() or "lastname" in col.lower():
                return (
                    False,
                    [],
                    0,
                )  # Not composite, but will be handled by direct match

    # Special handling for 'fname' and 'firstname' - these should match 'first_name' directly
    if param_lower in ["fname", "firstname", "givenname"]:
        for col in available_cols:
            if "first_name" in col.lower() or "firstname" in col.lower():
                return (
                    False,
                    [],
                    0,
                )  # Not composite, but will be handled by direct match

    # Special handling for 'dept' - should match 'department'
    if param_lower in ["dept", "dpt"]:
        for col in available_cols:
            if "department" in col.lower():
                return (
                    False,
                    [],
                    0,
                )  # Not composite, but will be handled by direct match

    # Special handling for company name
    if param_lower in [
        "companyname",
        "company",
        "employer",
        "organization",
        "org",
    ]:
        for col in available_cols:
            if "company_name" in col.lower() or "company" in col.lower():
                return (
                    False,
                    [],
                    0,
                )  # Not composite, but will be handled by direct match

    # Special handling for years of experience
    if param_lower in [
        "yrsexp",
        "yoe",
        "yearsexp",
        "years_exp",
        "experience",
        "exp",
        "years",
    ]:
        for col in available_cols:
            if (
                "years_of_experience" in col.lower()
                or "experience" in col.lower()
            ):
                return (
                    False,
                    [],
                    0,
                )  # Not composite, but will be handled by direct match

    # Common patterns to check for composite fields
    composite_patterns = [
        # (param pattern, field1, field2, score if matched)
        ("employeename", "first_name", "last_name", 0.85),
        ("fullname", "first_name", "last_name", 0.85),
        ("name", "first_name", "last_name", 0.75),
        ("employee", "first_name", "last_name", 0.70),
        ("username", "first_name", "last_name", 0.70),
        ("contactname", "first_name", "last_name", 0.80),
        ("clientname", "first_name", "last_name", 0.75),
        ("personname", "first_name", "last_name", 0.85),
    ]

    # Check if param_name matches any of the patterns
    for pattern, field1, field2, score in composite_patterns:
        if pattern in param_lower or param_lower in pattern:
            # Check if both fields exist in available_cols
            field1_matches = [
                col for col in available_cols if field1.lower() in col.lower()
            ]
            field2_matches = [
                col for col in available_cols if field2.lower() in col.lower()
            ]

            if field1_matches and field2_matches:
                # Return both matched fields
                matched_fields = [field1_matches[0], field2_matches[0]]
                return True, matched_fields, score

    return False, [], 0


def find_best_column_match(param_name, available_cols, threshold=0.55):
    """
    Find the best matching column name using Levenshtein distance.

    Args:
        param_name (str): The parameter name to find a match for
        available_cols (list): List of available column names
        threshold (float): Minimum similarity score required (0.0 to 1.0)

    Returns:
        tuple: (best_match, similarity_score) or (None, 0) if no match found
    """
    if not available_cols or not param_name:
        return None, 0.0

    # Add special handling for common field types
    param_lower = param_name.lower()

    # Direct mapping for last name related fields
    if param_lower in ["lname", "lastname", "surname", "sname"]:
        for col in available_cols:
            if "last_name" in col.lower() or "lastname" in col.lower():
                log.info(
                    f"Parameter '{param_name}' directly mapped to '{col}' as last name field (score: 0.95)"
                )
                return col, 0.95

    # Direct mapping for first name related fields
    if param_lower in ["fname", "firstname", "givenname"]:
        for col in available_cols:
            if "first_name" in col.lower() or "firstname" in col.lower():
                log.info(
                    f"Parameter '{param_name}' directly mapped to '{col}' as first name field (score: 0.95)"
                )
                return col, 0.95

    # Direct mapping for company name related fields
    if param_lower in [
        "companyname",
        "company",
        "company_name",
        "employer",
        "organization",
        "org",
    ]:
        for col in available_cols:
            if "company_name" in col.lower() or "company" in col.lower():
                log.info(
                    f"Parameter '{param_name}' directly mapped to '{col}' as company name field (score: 0.95)"
                )
                return col, 0.95

    # Direct mapping for years of experience related fields
    if param_lower in [
        "yrsexp",
        "yoe",
        "yearsexp",
        "years_exp",
        "experience",
        "exp",
        "years",
    ]:
        for col in available_cols:
            if (
                "years_of_experience" in col.lower()
                or "experience" in col.lower()
            ):
                log.info(
                    f"Parameter '{param_name}' directly mapped to '{col}' as years of experience field (score: 0.95)"
                )
                return col, 0.95

    # Direct mapping for department related fields
    if param_lower in ["dept", "dpt", "department"]:
        for col in available_cols:
            if "department" in col.lower():
                log.info(
                    f"Parameter '{param_name}' directly mapped to '{col}' as department field (score: 0.95)"
                )
                return col, 0.95

    # Check for composite fields
    is_composite, matched_fields, composite_score = get_composite_field_score(
        param_name, available_cols
    )
    if is_composite and composite_score >= threshold:
        # For 'employeeName' and similar composite fields, we need to decide which field to return
        # If parameter contains 'last' or 'surname', return the last name field
        if "last" in param_lower or "surname" in param_lower:
            # Return last name field
            for field in matched_fields:
                if "last" in field.lower():
                    log.info(
                        f"Parameter '{param_name}' matched to '{field}' as last name component (score: {composite_score:.2f})"
                    )
                    return field, composite_score

        # If parameter contains 'first' or 'given', return the first name field
        if "first" in param_lower or "given" in param_lower:
            # Return first name field
            for field in matched_fields:
                if "first" in field.lower():
                    log.info(
                        f"Parameter '{param_name}' matched to '{field}' as first name component (score: {composite_score:.2f})"
                    )
                    return field, composite_score

        # Default: return the first match with a note
        log.info(
            f"Parameter '{param_name}' identified as composite field: {' + '.join(matched_fields)} (score: {composite_score:.2f})"
        )
        return matched_fields[0], composite_score

    # Initialize best match tracking
    best_match = None
    best_score = 0.0

    # First check for exact match
    for col in available_cols:
        if param_name == col:
            return col, 1.0

    # Then check for case-insensitive match
    for col in available_cols:
        if param_lower == col.lower():
            log.debug(
                f"Case-insensitive match found: '{param_name}' → '{col}' (score: 0.98)"
            )
            return col, 0.98

    # Handle common abbreviations with higher scores
    for col in available_cols:
        # Check for abbreviations and acronyms, which should get high scores
        if is_abbreviation(param_name, col):
            score = 0.85  # High score for abbreviations
            log.debug(
                f"Abbreviation match found: '{param_name}' → '{col}' (score: {score:.2f})"
            )
            if score > best_score:
                best_match = col
                best_score = score

        if is_acronym(param_name, col):
            score = 0.85  # High score for acronyms
            log.debug(
                f"Acronym match found: '{param_name}' → '{col}' (score: {score:.2f})"
            )
            if score > best_score:
                best_match = col
                best_score = score

    # Loop through available columns for more complex matching
    for col in available_cols:
        # Calculate standard Levenshtein similarity
        score = levenshtein_similarity(param_name, col)

        # If identical, return immediately
        if score == 1.0:
            return col, 1.0

        # Calculate lowercase similarity and give it a boost
        lower_score = levenshtein_similarity(param_lower, col.lower())
        if lower_score > score:
            # Boost score for case-insensitive matches but don't make it the full lowercase score
            score = score * 0.2 + lower_score * 0.8

        # Check for substring matches or if one is contained in the other
        # These could be good indicators of a match despite typos
        if param_name in col or col in param_name:
            subscore = min(len(param_name), len(col)) / max(
                len(param_name), len(col)
            )
            score = max(
                score, subscore * 0.9
            )  # Boost the score but don't make it 1.0

        # Check for lowercase substring matches too
        if param_lower in col.lower() or col.lower() in param_lower:
            subscore = min(len(param_name), len(col)) / max(
                len(param_name), len(col)
            )
            score = max(
                score, subscore * 0.85
            )  # Slightly lower boost than exact case matches

        # Handle camelCase to snake_case conversion (e.g., "firstName" → "first_name")
        if "_" in col and "_" not in param_name:
            # Convert camelCase to snake_case for comparison
            import re

            snake_param = re.sub(r"(?<!^)(?=[A-Z])", "_", param_name).lower()
            snake_score = levenshtein_similarity(snake_param, col.lower())
            score = max(
                score, snake_score * 0.9
            )  # Good boost for snake/camel matches

        # Handle snake_case to camelCase conversion (e.g., "first_name" → "firstName")
        if "_" in param_name and "_" not in col:
            # Try to convert to camelCase
            camel_param = "".join(
                word.title() if i > 0 else word.lower()
                for i, word in enumerate(param_name.split("_"))
            )
            camel_score = levenshtein_similarity(camel_param, col)
            score = max(score, camel_score * 0.9)

        # Track best match
        if score > best_score:
            best_match = col
            best_score = score

    # Return best match if it meets threshold
    if best_score >= threshold:
        log.debug(
            f"Found column match: '{param_name}' → '{best_match}' (score: {best_score:.2f})"
        )
        return best_match, best_score

    log.warning(
        f"No match found for '{param_name}' with threshold {threshold}"
    )
    return None, 0.0


def analyze_and_suggest_matches(param_name, available_cols, top_n=3):
    """
    Analyze possible matches for a parameter and return top suggestions.

    Args:
        param_name (str): The parameter name to find matches for
        available_cols (list): List of available column names
        top_n (int): Number of top suggestions to return

    Returns:
        list: List of tuples (column_name, similarity_score) sorted by score
    """
    if not available_cols or not param_name:
        return []

    param_lower = param_name.lower()

    # Add special handling for common field types to suggestions
    suggestions = []

    # Special suggestions for last name related fields
    if param_lower in ["lname", "lastname", "surname", "sname"]:
        for col in available_cols:
            if "last_name" in col.lower() or "lastname" in col.lower():
                suggestions.append((col, 0.95))

    # Special suggestions for first name related fields
    if param_lower in ["fname", "firstname", "givenname"]:
        for col in available_cols:
            if "first_name" in col.lower() or "firstname" in col.lower():
                suggestions.append((col, 0.95))

    # Special suggestions for department
    if param_lower in ["dept", "dpt", "department"]:
        for col in available_cols:
            if "department" in col.lower() or "dept" in col.lower():
                suggestions.append((col, 0.95))

    # Special suggestions for company name
    if param_lower in [
        "companyname",
        "company",
        "company_name",
        "employer",
        "organization",
        "org",
    ]:
        for col in available_cols:
            if "company_name" in col.lower() or "company" in col.lower():
                suggestions.append((col, 0.95))

    # Check for composite fields
    is_composite, matched_fields, composite_score = get_composite_field_score(
        param_name, available_cols
    )
    if is_composite:
        # Add the composite fields as top suggestions
        composite_suggestion = [
            (field, composite_score) for field in matched_fields
        ]

        # If we have special suggestions, combine them
        if suggestions:
            combined = suggestions + composite_suggestion
            # Remove duplicates (keep highest score for each column)
            seen = {}
            unique_suggestions = []
            for col, score in combined:
                if col not in seen or score > seen[col]:
                    seen[col] = score

            for col, score in seen.items():
                unique_suggestions.append((col, score))

            unique_suggestions.sort(key=lambda x: x[1], reverse=True)
            return unique_suggestions[:top_n]

        # If we need more, add other columns as well
        if len(composite_suggestion) < top_n:
            remaining_cols = [
                col for col in available_cols if col not in matched_fields
            ]
            other_suggestions = _calculate_similarities(
                param_name, remaining_cols
            )[: top_n - len(composite_suggestion)]
            return composite_suggestion + other_suggestions
        return composite_suggestion[:top_n]

    # If we have special suggestions, add them to the similarities
    if suggestions:
        similarities = _calculate_similarities(
            param_name,
            [col for col in available_cols if (col, 0.95) not in suggestions],
        )
        combined = suggestions + similarities
        combined.sort(key=lambda x: x[1], reverse=True)
        return combined[:top_n]

    # Otherwise, use standard similarity calculations
    similarities = _calculate_similarities(param_name, available_cols)

    # If very low scores, check for abbreviations and boost them
    if similarities and similarities[0][1] < 0.5:
        # Try to find abbreviation matches and add them with higher scores
        abbreviation_scores = []
        for col in available_cols:
            if is_abbreviation(param_name, col):
                abbreviation_scores.append((col, 0.85))
            elif is_acronym(param_name, col):
                abbreviation_scores.append((col, 0.85))

        # Add abbreviation scores to the list and resort
        if abbreviation_scores:
            similarities = abbreviation_scores + similarities
            similarities.sort(key=lambda x: x[1], reverse=True)

    # Return top N matches
    return similarities[:top_n]


def _calculate_similarities(param_name, available_cols):
    """
    Calculate similarity scores for parameter against all available columns.

    Args:
        param_name (str): Parameter name to compare
        available_cols (list): Available column names

    Returns:
        list: List of (column, score) tuples sorted by descending score
    """
    # Calculate similarity for all columns
    similarities = []
    param_lower = param_name.lower()

    for col in available_cols:
        # Get standard similarity
        standard_score = levenshtein_similarity(param_name, col)

        # Get case-insensitive similarity
        lower_score = levenshtein_similarity(param_lower, col.lower())

        # Use the better of the two scores
        score = max(standard_score, lower_score * 0.95)

        # Check for substring matches
        if param_lower in col.lower() or col.lower() in param_lower:
            subscore = min(len(param_name), len(col)) / max(
                len(param_name), len(col)
            )
            score = max(score, subscore * 0.85)

        # Check for abbreviations/acronyms
        if is_abbreviation(param_name, col) or is_acronym(param_name, col):
            score = max(score, 0.85)

        # Add to results
        similarities.append((col, score))

    # Sort by similarity (descending)
    similarities.sort(key=lambda x: x[1], reverse=True)

    return similarities
