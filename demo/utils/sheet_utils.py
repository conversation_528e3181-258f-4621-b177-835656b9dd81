"""
Utilities for working with Google Sheets.

This module contains functions for working with Google Sheets URLs and IDs.
"""

import sys
from demo.utils.config import logger
from demo.utils.display import display_panel
from llm_processor.google_sheets_utils import (
    get_sheet_id_from_url as _get_sheet_id_from_url,
)


def extract_sheet_id(url):
    """
    Extract the sheet ID from a Google Sheets URL.

    This is a wrapper around the core function from llm_processor.google_sheets_utils
    that adds user-friendly error handling with display panels.

    Args:
        url: Google Sheets URL

    Returns:
        Sheet ID string
    """
    try:
        sheet_id = _get_sheet_id_from_url(url)
        if not sheet_id:
            display_panel(
                "Invalid Google Sheets URL\n"
                "Please provide a valid Google Sheets URL in the format:\n"
                "https://docs.google.com/spreadsheets/d/{SHEET_ID}/edit",
                title="URL Error",
                style="red",
            )
            sys.exit(1)
        return sheet_id
    except Exception as e:
        logger.error(f"Error parsing Google Sheets URL: {str(e)}")
        display_panel(
            f"Failed to parse Google Sheets URL\n{str(e)}\n"
            "Please provide a valid Google Sheets URL",
            title="URL Error",
            style="red",
        )
        sys.exit(1)
