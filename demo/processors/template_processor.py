"""
Template processor for filling in templates with values from rows.
"""

import re
import sys
import os
from pathlib import Path
import pandas as pd
from loguru import logger
import difflib  # For fuzzy string matching

# Add parent dir to path
current_dir = Path(os.path.dirname(os.path.abspath(__file__)))
parent_dir = current_dir.parent.parent
sys.path.append(str(parent_dir))

# Import config
from demo.utils.config import console
from rich.panel import Panel


def find_closest_match(var_name, available_cols, threshold=0.75):
    """
    Find the closest matching column name using string similarity.

    Args:
        var_name: The variable name to find a match for
        available_cols: List of available column names
        threshold: Minimum similarity score required (0.0 to 1.0)

    Returns:
        Tuple of (closest_match, similarity_score) or (None, 0) if no match found
    """
    if not available_cols:
        return None, 0

    # Use difflib to find close matches
    matches = difflib.get_close_matches(
        var_name, available_cols, n=1, cutoff=threshold
    )

    if matches:
        # Calculate the actual similarity score for the best match
        match = matches[0]
        score = difflib.SequenceMatcher(None, var_name, match).ratio()
        return match, score

    return None, 0


def fill_template(row, template, input_cols):
    """
    Fill a template with values from a row, replacing {{variable}} with actual values.
    Now includes semantic matching for misspelled variable names.

    Args:
        row: DataFrame row with values to use for variables
        template: Template string with {{variable}} placeholders
        input_cols: List of column names to use for replacement

    Returns:
        Dictionary containing the filled template and metadata
    """
    try:
        if not template or not isinstance(template, str):
            logger.error(f"Invalid template: {template}")
            return {
                "filled_template": "ERROR: Invalid template",
                "missing_vars": [],
                "replaced_vars": [],
            }

        # Extract all variable names from the template using regex
        expected_vars = re.findall(r"\{\{([^}]+)\}\}", template)
        missing_vars = []
        replaced_vars = []
        fuzzy_matched_vars = []  # Track variables that were fuzzy matched

        # Create a copy of the template to work with
        filled_template = template

        # Process each variable in the template
        for var_name in expected_vars:
            # Check if the variable exists in input columns
            if var_name in input_cols and var_name in row:
                # Direct match found
                value = row[var_name]

                # Skip if the value is NaN or empty
                if pd.isna(value) or str(value).strip() == "":
                    missing_vars.append(var_name)
                    continue

                # Replace all occurrences in the template
                replaced_vars.append(var_name)
                filled_template = filled_template.replace(
                    "{{" + var_name + "}}", str(value)
                )
            else:
                # No direct match, try fuzzy matching
                closest_match, score = find_closest_match(var_name, input_cols)

                if closest_match and closest_match in row:
                    # Found a close match, use it
                    value = row[closest_match]

                    # Skip if the value is NaN or empty
                    if pd.isna(value) or str(value).strip() == "":
                        missing_vars.append(var_name)
                        continue

                    # Add to fuzzy matched list
                    fuzzy_matched_vars.append(
                        {
                            "original": var_name,
                            "matched": closest_match,
                            "score": score,
                        }
                    )

                    # Replace all occurrences in the template
                    filled_template = filled_template.replace(
                        "{{" + var_name + "}}", str(value)
                    )
                    replaced_vars.append(var_name)  # Count as replaced

                    # Log a warning about the fuzzy match
                    logger.warning(
                        f"Variable '{var_name}' not found exactly, using close match '{closest_match}' (similarity: {score:.2f})"
                    )
                else:
                    # No match found at all
                    missing_vars.append(var_name)
                    filled_template = filled_template.replace(
                        "{{" + var_name + "}}", ""
                    )

        logger.debug(
            f"Template filled successfully (length: {len(filled_template)})"
        )

        # If we did any fuzzy matching, show a summary
        if fuzzy_matched_vars:
            fuzzy_summary = "\n".join(
                [
                    f"- '{match['original']}' → '{match['matched']}' (similarity: {match['score']:.2f})"
                    for match in fuzzy_matched_vars
                ]
            )

            console.print(
                Panel(
                    f"[yellow]Warning:[/] Used fuzzy matching for variable names:\n{fuzzy_summary}",
                    title="Fuzzy Variable Matching",
                    border_style="yellow",
                )
            )

        # Return template and metadata about what was replaced
        return {
            "filled_template": filled_template,
            "expected_vars": expected_vars,
            "missing_vars": missing_vars,
            "replaced_vars": replaced_vars,
            "fuzzy_matched_vars": fuzzy_matched_vars,
        }

    except Exception as e:
        logger.error(f"Error filling template: {str(e)}")
        console.print(
            Panel(
                f"Template filling failed\n"
                f"Error: [red]{str(e)}[/]\n\n"
                f"Template: [dim]{template[:100]}{'...' if len(template) > 100 else ''}[/]",
                title="Template Error",
                border_style="red",
            )
        )
        return {
            "filled_template": f"ERROR: {str(e)}",
            "missing_vars": [],
            "replaced_vars": [],
            "expected_vars": [],
            "fuzzy_matched_vars": [],
        }
