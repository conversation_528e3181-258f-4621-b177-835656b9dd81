"""
Core LLM processing logic for handling py-llm columns.
"""

import os
import sys
import time
import pandas as pd
from loguru import logger as log  # noqa: F401
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from dotenv import load_dotenv


# Add parent directory to path to access llm_processor
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))  # TODO: make this unnecessary.

from llm_processor.processor import ParallelLLMDataFrameProcessor
from llm_processor.chain_step import ChainStep
from main_config import CFG

# Load environment variables
load_dotenv()  # TODO May not be used.
console = Console()
GENERATION_TAGS = CFG.GENERATION_TAGS


def is_generation_tag(x):
    """
    Check if a value is a generation tag like <generate> or //.

    Args:
        x: Value to check

    Returns:
        Boolean indicating if the value is a generation tag
    """
    if pd.isna(x):
        return False  # Changed from True to False - NaN values should not trigger generation
    x_str = str(x).strip()
    if not x_str:  # Empty string should not trigger generation
        return False
    # Use the centralized GENERATION_TAGS constant
    return (x_str in GENERATION_TAGS) or (x_str.lower() == "generate")


def process_llm_column(
    prompt_inputs,
    output_col,
    template_col,
    model,
    temperature,
    max_tokens,
    generate_indices,
    direct_update_map,
):
    """
    Process a single LLM column.

    Args:
        prompt_inputs: DataFrame with input data
        output_col: Name of the output column
        template_col: Name of the template column
        model: LLM model to use
        temperature: Temperature for generation
        max_tokens: Maximum tokens for generation
        generate_indices: Indices of rows to process
        direct_update_map: Dictionary to store updates for later

    Returns:
        Updated direct_update_map with new values
    """
    # Filter to only process rows with generation tags
    chain_input = prompt_inputs.loc[generate_indices].copy()

    if len(chain_input) == 0:
        log.warning(f"No rows to process for {output_col} after filtering.")
        return direct_update_map

    log.info(f"Processing {len(chain_input)} rows for {output_col} with generation tags.")

    # Clear the output column to ensure LLM processing happens
    # This is needed because the processor skips columns that already have values
    if output_col in chain_input.columns:
        chain_input[output_col] = ""
        log.debug(f"Cleared {output_col} column values to ensure LLM processing")

    # Debug some of the inputs
    if not chain_input.empty:
        log.debug(f"First row template for {output_col}: {chain_input[template_col].iloc[0]}")

    # Process data
    llm_proc = ParallelLLMDataFrameProcessor(
        def_model=model,
        def_temperature=temperature,
        def_max_tokens=max_tokens,
        def_async_rate_limit=10,
        def_thread_rate_limit=5,
    )

    # Set the template column as the source for the prompt
    chain = [
        ChainStep(
            pt=template_col,  # Use the template column as source
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            col=output_col,
        ),
    ]

    # Log processing start details instead of printing another panel
    log.info(
        f"Starting LLM processing for column {output_col}: model={model}, "
        f"temperature={temperature}, max_tokens={max_tokens}, rows={len(chain_input)}"
    )

    # Track failed rows and errors
    failed_indices = []
    error_messages = {}

    # Try to execute chain with error handling
    try:
        start_time = time.time()
        result_df = llm_proc.execute_chain(
            chain_input,
            chain,
            max_attempts=3,
        )
        processing_time = time.time() - start_time

        # Check for missing rows in the result
        if len(result_df) < len(chain_input):
            missing_indices = set(chain_input.index) - set(result_df.index)
            if missing_indices:
                failed_indices.extend(missing_indices)
                for idx in missing_indices:
                    error_messages[idx] = "Row failed to process - not found in results"
                log.warning(f"{len(missing_indices)} rows failed to process for {output_col}")

    except Exception as e:
        # Handle complete chain failure
        log.error(f"Chain execution failed for {output_col}: {str(e)}")
        console.print(
            Panel(
                f"[bold red]Error:[/] Chain execution failed for {output_col}\n{str(e)}",
                title="LLM Processing Error",
                border_style="red",
            )
        )

        # Mark all rows as failed
        failed_indices = list(chain_input.index)
        for idx in failed_indices:
            error_messages[idx] = f"Chain execution failed: {str(e)}"

        # Create empty result dataframe to avoid breaking the rest of the code
        result_df = pd.DataFrame(columns=chain_input.columns)
        processing_time = time.time() - start_time

    # Create results summary table
    results_table = Table(title=f"Processing Results: {output_col}")
    results_table.add_column("Metric", style="cyan")
    results_table.add_column("Value", style="green")

    results_table.add_row("Processed rows", str(len(result_df)))
    results_table.add_row("Failed rows", str(len(failed_indices)))
    results_table.add_row("Processing time", f"{processing_time:.2f} seconds")

    # Only calculate averages if there were successful rows
    if len(result_df) > 0:
        results_table.add_row(
            "Avg time per row",
            f"{processing_time / max(1, len(result_df)):.2f} seconds",
        )

    # Calculate success rate
    success_rate = len(result_df) / max(1, len(chain_input)) * 100
    rate_color = "green" if success_rate > 90 else "yellow" if success_rate > 70 else "red"
    results_table.add_row(
        "Success rate",
        f"[{rate_color}]{len(result_df)}/{len(chain_input)} ({success_rate:.1f}%)[/]",
    )

    console.print(results_table)

    # Show error details if any failures occurred
    if failed_indices:
        error_table = Table(title=f"Processing Errors: {output_col}")
        error_table.add_column("Row Index", style="cyan")
        error_table.add_column("Error", style="red")

        # Show up to 5 sample errors
        sample_errors = list(error_messages.items())[:5]
        for idx, error in sample_errors:
            try:
                row_info = f"{idx} (col_ref: {chain_input.loc[idx, 'col_ref']})"
            except:
                row_info = str(idx)
            error_table.add_row(row_info, error)

        if len(error_messages) > 5:
            error_table.add_row(
                "...",
                f"[yellow]{len(error_messages) - 5} more errors omitted[/]",
            )

        console.print(error_table)
        log.warning(f"Failed to process {len(failed_indices)} rows for {output_col}")

    if len(result_df) > 0:
        log.success(f"Processing complete for {output_col}: {len(result_df)} rows in {processing_time:.2f} seconds")
    else:
        log.error(f"Processing failed for all rows in {output_col}")

    # Results counter for summary
    successful_updates = 0

    # DIRECTLY STORE results in our update map
    for idx_in_result in range(len(result_df)):
        try:
            result_row_idx = result_df.index[idx_in_result]
            result_value = result_df.iloc[idx_in_result][output_col]

            # Get the col_ref value for this row
            col_ref_value = None

            # Try multiple ways to get the col_ref value
            try:
                # Method 1: Try direct .loc access
                col_ref_value = chain_input.loc[result_row_idx, "col_ref"]
            except Exception:
                # More informative warning message
                log.debug(f"Could not access col_ref for row {result_row_idx} via .loc - trying alternative methods")
                try:
                    # Method 2: Try .iloc if we have numeric indices
                    if isinstance(result_row_idx, int):
                        col_ref_value = chain_input.iloc[result_row_idx]["col_ref"]
                except Exception:
                    log.debug(f"Could not access col_ref for row {result_row_idx} via .iloc - trying another method")
                    try:
                        # Method 3: Try direct dictionary-style access
                        col_ref_value = chain_input.at[result_row_idx, "col_ref"]
                    except Exception:
                        log.debug(f"Could not access col_ref for row {result_row_idx} via .at - will try original DataFrame")

            # If we still don't have a col_ref, look it up in the original data
            if col_ref_value is None:
                try:
                    # Find the same index in the original prompt_inputs
                    if result_row_idx in prompt_inputs.index:
                        col_ref_value = prompt_inputs.loc[result_row_idx, "col_ref"]
                        log.debug(f"Found col_ref {col_ref_value} from prompt_inputs for row {result_row_idx}")
                except Exception:
                    log.debug(f"Could not find col_ref in original DataFrame for row {result_row_idx} - will use fallback")

            # As a last resort, if we still don't have a col_ref, use the row index + 1
            # This is risky but better than losing the result
            if col_ref_value is None:
                col_ref_value = int(result_row_idx) + 1  # Add 1 since col_ref is 1-based
                log.debug(f"Using fallback col_ref value for row {result_row_idx}: {col_ref_value} (row index + 1)")

            # Only store non-empty values
            log.debug(f"Checking result for row {result_row_idx}: value='{result_value}', type={type(result_value)}")
            if pd.notna(result_value) and str(result_value).strip() != "":
                log.debug(f"Direct storage: row {result_row_idx}, col_ref {col_ref_value}, col {output_col}: '{result_value}'")

                # Store in direct map for Google Sheets update
                # Use a string representation of row_idx to avoid any issues with indexes
                map_key = (str(result_row_idx), output_col)
                direct_update_map[map_key] = (col_ref_value, str(result_value))
                successful_updates += 1

                # Also try to store in DataFrames for template use
                try:
                    prompt_inputs.at[result_row_idx, output_col] = str(result_value)
                except:
                    pass
            else:
                log.warning(f"Empty result for row {result_row_idx}, not storing")
        except Exception as e:
            log.error(f"Error storing result at index {idx_in_result}: {str(e)}")
            import traceback

            log.error(f"Traceback: {traceback.format_exc()}")

    # Show a summary of the updates
    log.success(f"Successfully stored {successful_updates} results for column {output_col}")

    return direct_update_map
