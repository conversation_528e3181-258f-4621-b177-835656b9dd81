"""
Process Python functions using LLM for execution.
"""

import re
import pandas as pd
from loguru import logger as log
import sys
import os

sys.path.append(
    os.path.dirname(
        os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    )
)

from llm_processor.processor import ParallelLLMDataFrameProcessor
from llm_processor.chain_step import ChainStep
from rich.panel import Panel
from demo.utils.config import console
from demo.utils.column_matcher import (
    find_best_column_match,
    analyze_and_suggest_matches,
)


def process_py_func(function_text, input_params, model):
    """
    Process a Python function using an LLM.

    Args:
        function_text: Text of the Python function
        input_params: Dictionary of parameter values
        model: LLM model to use

    Returns:
        Output of the function execution
    """
    # Extract function from tags if present
    function_match = re.search(r"<f>(.*?)</f>", function_text, re.DOTALL)
    if function_match:
        function_text = function_match.group(1)

    # Format input parameters as string
    input_params_str = "\n".join(
        [f"{k}={repr(v)}" for k, v in input_params.items()]
    )

    # Create prompt for LLM
    prompt = f"""
    You are a Python function execution engine. Execute the following Python function with the provided inputs.
    Return ONLY the result of the function execution without explanation, commentary, or code.

    Python function:
    ```python
    {function_text}
    ```

    Input parameters:
    ```python
    {input_params_str}
    ```

    Execute the function with these inputs and return ONLY the result.
    """

    log.debug(
        f"Sending Python function to LLM for execution:\n{function_text}"
    )
    log.debug(f"With parameters:\n{input_params_str}")

    # Use the existing LLM processor framework
    llm_proc = ParallelLLMDataFrameProcessor(
        def_model=model,
        def_temperature=0,  # Use temperature 0 for deterministic results
        def_max_tokens=1000,
        def_async_rate_limit=1,
        def_thread_rate_limit=1,
    )

    # Create a simple DataFrame with our prompt
    df = pd.DataFrame({"prompt": [prompt]})

    # Define a simple chain to process the prompt
    chain = [
        ChainStep(
            pt="prompt",
            model=model,
            temperature=0,
            max_tokens=1000,
            col="result",
        ),
    ]

    try:
        # Execute the chain
        result_df = llm_proc.execute_chain(df, chain)
        result = result_df["result"].iloc[0]

        # Clean up the result - remove any code blocks, backticks, etc.
        result = re.sub(r"```.*?```", "", result, flags=re.DOTALL)
        result = re.sub(r"`(.*?)`", r"\1", result)
        result = result.strip()

        log.debug(f"Python function execution result: {result}")
        return result
    except Exception as e:
        log.error(f"Failed to execute Python function: {str(e)}")
        return f"Error executing function: {str(e)}"


def extract_function_params(
    function_text, row_data, available_input_cols=None
):
    """
    Extract parameter names from function definition and match with row data.
    Uses Levenshtein distance for improved matching of parameter names with typos.

    Args:
        function_text: Text of the Python function
        row_data: DataFrame row with input values
        available_input_cols: Optional list of all available input columns to use for matching.
                             If not provided, will use the keys from row_data.

    Returns:
        Dictionary of parameter name to parameter value
    """
    # Extract function signature using regex
    func_signature_match = re.search(
        r"<f>(.*?)\((.*?)\)", function_text, re.DOTALL
    )

    if not func_signature_match:
        # Try alternative pattern without the tags since they might be malformed
        func_signature_match = re.search(
            r"def\s+([a-zA-Z0-9_]+)\s*\((.*?)\)", function_text, re.DOTALL
        )

    if not func_signature_match:
        # Try just getting everything in between parentheses
        func_signature_match = re.search(
            r"\((.*?)\)", function_text, re.DOTALL
        )

    if not func_signature_match:
        log.error(f"Could not parse function signature: {function_text}")
        return {}

    # Extract parameter names from function signature
    param_string = (
        func_signature_match.group(2)
        if func_signature_match.lastindex >= 2
        else func_signature_match.group(1)
    )
    param_names = [
        p.strip()
        .split("=")[0]
        .strip()  # Handle default values by splitting at '='
        for p in param_string.split(",")
        if p.strip()
    ]

    # Available column names - use available_input_cols if provided, otherwise use row_data keys
    available_cols = (
        available_input_cols
        if available_input_cols is not None
        else list(row_data.keys())
    )

    # Log available columns for debugging
    log.debug(f"Available columns for matching: {available_cols}")
    log.debug(f"Looking for parameters: {param_names}")

    # Create parameter dictionary
    params = {}
    fuzzy_matched_params = []

    # Lower threshold for Levenshtein matching to capture more typos
    threshold = 0.6

    for param in param_names:
        # First try direct match
        if param in available_cols and param in row_data:
            # Direct match found and it exists in row_data
            params[param] = row_data[param]
            log.debug(f"Direct match found for parameter '{param}'")
        else:
            # Try fuzzy matching using Levenshtein distance
            closest_match, score = find_best_column_match(
                param, available_cols, threshold
            )

            if closest_match and closest_match in row_data:
                # Found a close match and it exists in row_data
                params[param] = row_data[closest_match]

                # Record the fuzzy match for reporting
                fuzzy_matched_params.append(
                    {
                        "original": param,
                        "matched": closest_match,
                        "score": score,
                    }
                )

                # Log the match
                log.info(
                    f"Parameter '{param}' not found exactly, using Levenshtein match '{closest_match}' (similarity: {score:.2f})"
                )
            else:
                # No match found or match doesn't exist in row_data
                # Get suggestions to help with debugging
                suggestions = analyze_and_suggest_matches(
                    param, available_cols, top_n=3
                )

                if suggestions:
                    suggestion_txt = ", ".join(
                        [f"'{s[0]}' ({s[1]:.2f})" for s in suggestions]
                    )
                    log.warning(
                        f"Parameter '{param}' not found in data. Top suggestions: {suggestion_txt}"
                    )
                else:
                    log.warning(
                        f"Parameter '{param}' not found in data and no suggestions available. Using None."
                    )

                params[param] = None

    # If we did any fuzzy matching, show a summary
    if fuzzy_matched_params:
        fuzzy_summary = "\n".join(
            [
                f"- '{match['original']}' → '{match['matched']}' (similarity: {match['score']:.2f})"
                for match in fuzzy_matched_params
            ]
        )

        console.print(
            Panel(
                f"[yellow]Used fuzzy matching for {len(fuzzy_matched_params)} parameters:[/]\n{fuzzy_summary}",
                title="Parameter Fuzzy Matching",
                border_style="yellow",
            )
        )

    return params
