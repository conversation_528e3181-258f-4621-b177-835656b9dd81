"""
Utilities for extracting column types and configurations from a DataFrame.
"""

import sys
import pandas as pd
from demo.utils.config import logger
from demo.utils.display import display_panel
from main_config import CFG

# Use configuration directly from CFG
CONFIG_NAMES = CFG.CONFIG_NAMES
CONFIG_ROW_MAPPING = CFG.CONFIG_ROW_MAPPING
DEFAULT_MODEL = CFG.DEFAULT_MODEL
DEFAULT_TEMPERATURE = CFG.DEFAULT_TEMPERATURE
DEFAULT_MAX_OUTPUT_TOKENS = CFG.DEFAULT_MAX_OUTPUT_TOKENS
DEFAULT_FLAGS = CFG.DEFAULT_FLAGS
DEFAULT_DATA_START_ROW = CFG.DEFAULT_DATA_START_ROW


def get_config_by_name(df, config_name, output_cols, default_value=None):
    """
    Extract configuration values by looking for config_name in the first column.

    Args:
        df: DataFrame with the data
        config_name: Name of the configuration to find in the first column
        output_cols: List of output column names to extract values for
        default_value: Default value to use if config_name is not found

    Returns:
        List of configuration values for each output column
    """
    # Get the first column name (regardless of what it's called)
    if len(df.columns) == 0:
        error_message = "Configuration error: DataFrame has no columns."
        logger.error(error_message)
        display_panel(
            error_message,
            title="Configuration Error",
            style="red",
        )
        sys.exit(1)

    first_col_name = df.columns[0]

    # Determine which row names to look for based on the config_name
    config_name_lower = config_name.lower()
    possible_row_names = []

    # Find which category this config_name belongs to using the mapping from CFG
    for key, values in CONFIG_ROW_MAPPING.items():
        if config_name_lower == key or config_name_lower in values:
            possible_row_names = values
            break

    if not possible_row_names:
        possible_row_names = [config_name.lower()]

    # Look for any of the possible row names in the first column (case-insensitive)
    config_rows = []
    for idx, value in enumerate(df[first_col_name]):
        if isinstance(value, str) and value.lower() in possible_row_names:
            config_rows.append(idx)
            logger.info(f"Found configuration '{config_name}' as '{value}' in row {idx + 1}")
            break

    if not config_rows:
        # Get the list of all config names for the error message
        config_names_list = ", ".join([f"'{name}'" for name in CONFIG_NAMES.values()])
        possible_names_list = ", ".join([f"'{name}'" for name in possible_row_names])

        error_message = (
            f"Configuration error: '{config_name}' not found in the first column ({first_col_name}).\n\n"
            f"The first column should contain one of these values for this configuration: {possible_names_list}\n\n"
            f"All required configuration values are: {config_names_list}\n"
            f"These values are defined in main_config.py as CONFIG_NAMES and are used to configure each column."
        )
        logger.error(error_message)
        display_panel(
            error_message,
            title="Configuration Error",
            style="red",
        )
        sys.exit(1)

    # Use the first matching row
    config_row = config_rows[0]

    # Extract values for output columns
    config_values = []
    for col in output_cols:
        if col in df.columns:
            value = df.loc[config_row, col]
            # Handle empty or NaN values
            if pd.isna(value) or (isinstance(value, str) and value.strip() == ""):
                # Only show warnings for required configuration items
                if config_name == CONFIG_NAMES["FLAGS"]:
                    # FLAGS are optional, so just use the default without a warning
                    config_values.append(default_value)
                else:
                    # For other configuration items (TEMPLATE, MODEL, etc.), show a warning
                    logger.warning(f"Missing required configuration: No {config_name} value for column {col}. Using default: {default_value}")
                    config_values.append(default_value)
            else:
                config_values.append(value)
        else:
            logger.warning(f"Column {col} not found. Using default {config_name}: {default_value}")
            config_values.append(default_value)

    return config_values


def extract_column_configurations(df):
    """
    Extract column type identifiers and configurations from a DataFrame.

    Args:
        df: DataFrame containing configuration information

    Returns:
        dict: Dictionary containing column configurations
    """
    try:
        # Dictionary to store all configuration details
        config = {
            "prompt_input_cols": [],
            "prompt_output_cols": [],
            "py_func_cols": [],
            "all_output_cols": [],
            "column_type_map": {},
            "py_func_templates": {},
            "prompt_templates": [],
            "models": [],
            "temperatures": [],
            "max_output_tokens": [],
            "flags": [],
            "py_func_models": {},
            "py_func_temperatures": {},
            "py_func_max_output_tokens": {},
        }

        # Filter out columns with empty names
        empty_cols = [i for i, col in enumerate(df.columns) if col == ""]
        logger.debug(f"Column names: {list(df.columns)}")
        logger.debug(f"Empty column indices: {empty_cols}")

        if empty_cols:
            logger.info(f"Skipping {len(empty_cols)} empty columns in the DataFrame")
            # Create a new DataFrame without the empty columns
            non_empty_cols = [col for col in df.columns if col != ""]
            df = df[non_empty_cols]

        # Check for duplicate non-empty column names - this should cause failure
        duplicate_cols = [col for col in df.columns[df.columns.duplicated()].tolist() if col != ""]
        if duplicate_cols:
            error_msg = f"Found duplicate non-empty column names in the DataFrame: {duplicate_cols}"
            logger.error(error_msg)
            display_panel(
                f"{error_msg}\nDuplicate column names are not allowed. Please ensure all column names are unique.",
                title="Configuration Error",
                style="red",
            )
            sys.exit(1)

        # Extract column references from first row using a method that avoids the duplicate column warning
        df_col_ref = df.iloc[0]

        # Find column references for py-func (we still check for this to skip these columns)
        config["py_func_cols"] = []
        for col in df.columns:
            if col in df_col_ref.index and isinstance(df_col_ref[col], str) and "py-func" in df_col_ref[col]:
                config["py_func_cols"].append(col)
                logger.info(f"Found Python function column (will be skipped): {col}")

        # NEW LOGIC: Find columns that have generation tags in their data (after row 19)
        # and have a template defined
        generation_tags = ["<generate>", "//", "<g>", "generate"]
        potential_output_cols = []
        
        # Skip first column (col_ref) and py-func columns
        for col in df.columns:
            if col == "col_ref" or col in config["py_func_cols"]:
                continue
                
            # Check if this column has any generation tags after row 19
            has_generation_tags = False
            if len(df) > 19:
                col_data = df[col].iloc[19:]
                for tag in generation_tags:
                    if col_data.astype(str).str.strip().eq(tag).any():
                        has_generation_tags = True
                        break
            
            if has_generation_tags:
                potential_output_cols.append(col)
                logger.debug(f"Column '{col}' has generation tags in data rows")
        
        # Use all columns with generation tags as output columns
        config["prompt_output_cols"] = potential_output_cols
        
        # For input columns, we'll use ALL columns as potential inputs (except col_ref)
        # The actual matching will happen based on template variable names
        config["prompt_input_cols"] = [col for col in df.columns if col != "col_ref"]
        
        if config["prompt_output_cols"]:
            logger.info(f"Found columns with generation tags: {config['prompt_output_cols']}")
        else:
            logger.warning("No columns found with generation tags in data rows (after row 20)")
            # Don't exit here - let the process continue and show what's missing
            
            # Provide detailed guidance to the user
            error_message = (
                "No columns found with generation tags in data rows.\n\n"
                "To process data, you need to:\n"
                "1. Add generation tags to cells in your data rows (after row 20)\n"
                "2. Supported tags are: " + ", ".join([f"'{tag}'" for tag in generation_tags]) + "\n"
                "3. Place these tags in cells where you want LLM generation to occur\n\n"
                "Example:\n"
                "  Row 21: [data] | [data] | // | [data]\n"
                "  Row 22: [data] | [data] | <generate> | [data]\n\n"
                "Without generation tags, no processing will occur."
            )
            
            # Display a prominent warning panel
            display_panel(
                error_message,
                title="⚠️  No Generation Tags Found",
                style="yellow",
            )
            
            # Check if this should be a hard error based on configuration
            # Allow continuing for now but make it very clear nothing will happen
            logger.error("No generation tags found - no processing will occur")
            
            # Analyze the sheet to provide more specific guidance
            non_empty_cols = [col for col in df.columns if col != "col_ref" and col != ""]
            if non_empty_cols:
                logger.info(f"Available columns that could be processed: {', '.join(non_empty_cols)}")
                
                # Check if any columns have templates defined
                cols_with_templates = []
                for col in non_empty_cols:
                    try:
                        template_values = get_config_by_name(df, CONFIG_NAMES["TEMPLATE"], [col], default_value="")
                        if template_values and template_values[0] and not pd.isna(template_values[0]):
                            cols_with_templates.append(col)
                    except:
                        pass
                
                if cols_with_templates:
                    display_panel(
                        f"Columns with templates defined: {', '.join(cols_with_templates)}\n"
                        f"Add generation tags to cells in these columns to enable processing.",
                        title="💡 Suggestion",
                        style="blue",
                    )
            
            # Check if we should exit based on configuration
            if CFG.FAIL_ON_NO_GENERATION_TAGS:
                logger.error("Exiting due to FAIL_ON_NO_GENERATION_TAGS=True in configuration")
                display_panel(
                    "Exiting because no generation tags were found.\n"
                    "To continue with just a warning, set FAIL_ON_NO_GENERATION_TAGS=False in main_config.py",
                    title="❌ Configuration Error",
                    style="red",
                )
                sys.exit(1)

        # Get the original column order from the dataframe
        original_column_order = list(df.columns)

        # Create list of output columns in their original order
        for col in original_column_order:
            if col in config["py_func_cols"] or col in config["prompt_output_cols"]:
                config["all_output_cols"].append(col)

        # Create a mapping of column type for easy reference
        for col in config["py_func_cols"]:
            config["column_type_map"][col] = "py-func"
        for col in config["prompt_output_cols"]:
            config["column_type_map"][col] = "py-llm"

        # Extract Python function templates and configurations
        if config["py_func_cols"]:
            for col in config["py_func_cols"]:
                # Extract function template using config name
                py_func_template_values = get_config_by_name(df, CONFIG_NAMES["TEMPLATE"], [col], default_value="")
                py_func_template = py_func_template_values[0]

                if pd.isna(py_func_template) or str(py_func_template).strip() == "":
                    logger.error(f"Empty Python function template for column: {col}")
                    display_panel(
                        f"Empty Python function template for column: {col}\nFunction template is required.",
                        title="Python Function Error",
                        style="red",
                    )
                    sys.exit(1)
                config["py_func_templates"][col] = py_func_template
                logger.debug(f"Python function template for {col}: {py_func_template}")

                # Extract model using config name
                model_values = get_config_by_name(df, CONFIG_NAMES["MODEL"], [col], default_value=DEFAULT_MODEL)
                model = model_values[0]

                if pd.isna(model) or str(model).strip() == "":
                    logger.warning(f"Missing model configuration for py-func column: {col}, using default {DEFAULT_MODEL}")
                    display_panel(
                        f"Missing model configuration for py-func column: {col}\nUsing default model: {DEFAULT_MODEL}",
                        title="Configuration Warning",
                        style="yellow",
                    )
                    model = DEFAULT_MODEL
                config["py_func_models"][col] = str(model).strip()
                logger.debug(f"Model for py-func column {col}: {config['py_func_models'][col]}")

                # Extract temperature using config name
                try:
                    temp_values = get_config_by_name(df, CONFIG_NAMES["TEMPERATURE"], [col], default_value=str(DEFAULT_TEMPERATURE))
                    temperature = temp_values[0]

                    if pd.isna(temperature) or str(temperature).strip() == "":
                        logger.warning(f"Missing temperature value for py-func column: {col}, using default {DEFAULT_TEMPERATURE}")
                        display_panel(
                            f"Missing temperature for py-func column: {col}\nUsing default temperature: {DEFAULT_TEMPERATURE}",
                            title="Configuration Warning",
                            style="yellow",
                        )
                        temperature = DEFAULT_TEMPERATURE

                    temperature_value = float(temperature)
                    config["py_func_temperatures"][col] = temperature_value
                    logger.debug(f"Temperature for py-func column {col}: {temperature_value}")
                except (ValueError, TypeError):
                    logger.error(f"Invalid temperature value for py-func column {col}: {temperature}, must be a number")
                    display_panel(
                        f"Invalid temperature for py-func column {col}: '{temperature}'\nMust be a valid number.",
                        title="Configuration Error",
                        style="red",
                    )
                    sys.exit(1)

                # Extract max tokens using config name
                try:
                    token_values = get_config_by_name(df, CONFIG_NAMES["MAX_TOKENS"], [col], default_value=str(DEFAULT_MAX_OUTPUT_TOKENS))
                    max_tokens = token_values[0]

                    if pd.isna(max_tokens) or str(max_tokens).strip() == "":
                        logger.warning(f"Missing max_tokens value for py-func column: {col}, using default {DEFAULT_MAX_OUTPUT_TOKENS}")
                        display_panel(
                            f"Missing max_tokens for py-func column: {col}\nUsing default max tokens: {DEFAULT_MAX_OUTPUT_TOKENS}",
                            title="Configuration Warning",
                            style="yellow",
                        )
                        max_tokens = DEFAULT_MAX_OUTPUT_TOKENS

                    max_tokens_value = int(max_tokens)
                    config["py_func_max_output_tokens"][col] = max_tokens_value
                    logger.debug(f"Max tokens for py-func column {col}: {max_tokens_value}")
                except (ValueError, TypeError):
                    logger.error(f"Invalid max_tokens value for py-func column {col}: {max_tokens}, must be an integer")
                    display_panel(
                        f"Invalid max_tokens for py-func column {col}: '{max_tokens}'\nMust be a valid integer.",
                        title="Configuration Error",
                        style="red",
                    )
                    sys.exit(1)

        # For regular prompt columns, validate all templates
        if config["prompt_output_cols"]:
            # Extract prompt templates using config name
            config["prompt_templates"] = get_config_by_name(df, CONFIG_NAMES["TEMPLATE"], config["prompt_output_cols"], default_value="")

            # Validate prompt templates - fail immediately if any are missing or empty
            if not all(config["prompt_templates"]):
                empty_templates = [i for i, t in enumerate(config["prompt_templates"]) if not t or pd.isna(t) or str(t).strip() == ""]
                logger.error(f"Empty prompt templates found for columns: {[config['prompt_output_cols'][i] for i in empty_templates]}")
                display_panel(
                    f"Empty prompt templates found for columns: {[config['prompt_output_cols'][i] for i in empty_templates]}\nConfiguration must be complete with no missing values.",
                    title="Configuration Error",
                    style="red",
                )
                sys.exit(1)

            # Extract models using config name
            config["models"] = get_config_by_name(df, CONFIG_NAMES["MODEL"], config["prompt_output_cols"], default_value=DEFAULT_MODEL)

            # Validate models - fail immediately if any are missing or empty
            if not all(config["models"]):
                empty_models = [i for i, m in enumerate(config["models"]) if not m or pd.isna(m) or str(m).strip() == ""]
                logger.error(f"Missing model configuration for columns: {[config['prompt_output_cols'][i] for i in empty_models]}")
                display_panel(
                    f"Missing model configuration for columns: {[config['prompt_output_cols'][i] for i in empty_models]}\nConfiguration must be complete with no missing values.",
                    title="Configuration Error",
                    style="red",
                )
                sys.exit(1)

            # Extract temperatures using config name
            try:
                temp_values = get_config_by_name(
                    df, CONFIG_NAMES["TEMPERATURE"], config["prompt_output_cols"], default_value=str(DEFAULT_TEMPERATURE)
                )
                for i, temp in enumerate(temp_values):
                    try:
                        if pd.isna(temp) or str(temp).strip() == "":
                            logger.error(f"Missing temperature value for {config['prompt_output_cols'][i]}")
                            display_panel(
                                f"Missing temperature for {config['prompt_output_cols'][i]}\nConfiguration must be complete with no missing values.",
                                title="Configuration Error",
                                style="red",
                            )
                            sys.exit(1)

                        temp_value = float(temp)
                        config["temperatures"].append(temp_value)
                    except (ValueError, TypeError):
                        logger.error(f"Invalid temperature value for {config['prompt_output_cols'][i]}: {temp}, must be a number")
                        display_panel(
                            f"Invalid temperature for {config['prompt_output_cols'][i]}: '{temp}'\nMust be a valid number with no missing values.",
                            title="Configuration Error",
                            style="red",
                        )
                        sys.exit(1)
            except Exception as e:
                logger.error(f"Failed to parse temperature values: {str(e)}")
                display_panel(
                    f"Failed to parse temperature values: {str(e)}\nConfiguration must be complete with no missing values.",
                    title="Configuration Error",
                    style="red",
                )
                sys.exit(1)

            # Extract max tokens using config name
            try:
                token_values = get_config_by_name(
                    df, CONFIG_NAMES["MAX_TOKENS"], config["prompt_output_cols"], default_value=str(DEFAULT_MAX_OUTPUT_TOKENS)
                )
                for i, tokens in enumerate(token_values):
                    try:
                        if pd.isna(tokens) or str(tokens).strip() == "":
                            logger.error(f"Missing max_tokens value for {config['prompt_output_cols'][i]}")
                            display_panel(
                                f"Missing max_tokens for {config['prompt_output_cols'][i]}\nConfiguration must be complete with no missing values.",
                                title="Configuration Error",
                                style="red",
                            )
                            sys.exit(1)

                        token_value = int(tokens)
                        config["max_output_tokens"].append(token_value)
                    except (ValueError, TypeError):
                        logger.error(f"Invalid max_tokens value for {config['prompt_output_cols'][i]}: {tokens}, must be an integer")
                        display_panel(
                            f"Invalid max_tokens for {config['prompt_output_cols'][i]}: '{tokens}'\nMust be a valid integer with no missing values.",
                            title="Configuration Error",
                            style="red",
                        )
                        sys.exit(1)
            except Exception as e:
                logger.error(f"Failed to parse max_tokens values: {str(e)}")
                display_panel(
                    f"Failed to parse max_tokens values: {str(e)}\nConfiguration must be complete with no missing values.",
                    title="Configuration Error",
                    style="red",
                )
                sys.exit(1)

            # Extract flags using config name
            config["flags"] = get_config_by_name(df, CONFIG_NAMES["FLAGS"], config["prompt_output_cols"], default_value=DEFAULT_FLAGS)

        # Validate data rows exist
        if len(df) <= 19:
            logger.error("No data rows found. Sheet should have data starting from row 20")
            display_panel(
                "No data rows found. Sheet should have data starting from row 20",
                title="Data Error",
                style="red",
            )
            sys.exit(1)

        return config

    except Exception as e:
        logger.error(f"Failed to extract column configurations: {str(e)}")
        display_panel(
            f"Failed to extract column configurations: {str(e)}\nCheck that sheet has the required configuration rows and no values are missing.",
            title="Configuration Error",
            style="red",
        )
        sys.exit(1)


def extract_data_rows(df, config):
    """
    Extract data rows from the DataFrame.

    Args:
        df: DataFrame containing data
        config: Dictionary containing column configurations

    Returns:
        DataFrame: DataFrame containing only the data rows
    """
    try:
        # NEW LOGIC: Use all columns except col_ref as potential inputs
        # Skip only the col_ref column and any empty columns
        input_cols = [col for col in df.columns if col != "col_ref" and col != ""]
        
        if not input_cols:
            logger.error("No valid columns found in the DataFrame")
            display_panel(
                "No valid columns found in the DataFrame.",
                title="Data Error",
                style="red",
            )
            sys.exit(1)
        
        # Extract data starting from row 20 (index 19)
        prompt_inputs = df[input_cols].iloc[19:].replace("", pd.NA).dropna(how="all", ignore_index=True)

        if len(prompt_inputs) == 0:
            logger.error("No valid data rows found after row 20")
            display_panel(
                "No valid data rows found after row 20.\nAll rows are empty or contain no data.",
                title="Data Error",
                style="red",
            )
            sys.exit(1)

        # Add col_ref column
        prompt_inputs["col_ref"] = df["col_ref"].iloc[19:].reset_index(drop=True).iloc[: len(prompt_inputs)]

        return prompt_inputs
    except Exception as e:
        logger.error(f"Failed to extract data rows: {str(e)}")
        display_panel(
            f"Failed to extract data rows: {str(e)}",
            title="Data Error",
            style="red",
        )
        sys.exit(1)
