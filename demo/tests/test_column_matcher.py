#!/usr/bin/env python3
"""
Unit tests for column_matcher.py module.

These tests verify the functionality of column matching utilities, including:
- Finding the best column match using Levenshtein distance
- Analyzing and suggesting matches for column names
"""

import os
import sys
import unittest
from typing import List, Tuple, Optional

# Add the parent directory to the path to access the demo module
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Import the module to test
try:
    from demo.utils.column_matcher import find_best_column_match, analyze_and_suggest_matches
except ImportError:
    # Mock for linting purposes
    def find_best_column_match(
        _query: str,
        _column_names: List[str],
        _threshold: float = 0.7
    ) -> Tuple[Optional[str], float]:
        """Mock function for linting."""
        return None, 0.0

    def analyze_and_suggest_matches(
        _query: str,
        _column_names: List[str]
    ) -> List[Tuple[str, float]]:
        """Mock function for linting."""
        return []


class TestColumnMatcher(unittest.TestCase):
    """Test cases for column_matcher.py module."""

    def setUp(self) -> None:
        """Set up test fixtures."""
        # Define sample column names
        self.column_names = [
            "customer_name",
            "customer_age",
            "customer_address",
            "product_name",
            "product_price",
            "order_date"
        ]

    def test_find_best_column_match_exact(self) -> None:
        """
        Test finding the best column match with an exact match.

        This test verifies that the function correctly finds an exact match.

        The test should pass because:
        1. The function should return the exact match with a high score
        2. The function should handle case sensitivity correctly
        """
        # Test with an exact match
        match, score = find_best_column_match("customer_name", self.column_names)
        self.assertEqual(match, "customer_name")
        # Score should be very high, but may not be exactly 1.0
        self.assertGreaterEqual(score, 0.95)

        # Test with an exact match but different case
        match, score = find_best_column_match("Customer_Name", self.column_names)
        self.assertEqual(match, "customer_name")
        # Score should be very high, but may not be exactly 1.0
        self.assertGreaterEqual(score, 0.95)

    def test_find_best_column_match_close(self) -> None:
        """
        Test finding the best column match with a close match.

        This test verifies that the function correctly finds a close match.

        The test should pass because:
        1. The function should return the closest match based on Levenshtein distance
        2. The function should return a score between 0 and 1
        """
        # Test with a close match (one character different)
        match, score = find_best_column_match("customer_nam", self.column_names)
        self.assertEqual(match, "customer_name")
        self.assertTrue(0.8 <= score < 1.0)

        # Test with a close match (one character added)
        match, score = find_best_column_match("customer_names", self.column_names)
        self.assertEqual(match, "customer_name")
        self.assertTrue(0.8 <= score < 1.0)

    def test_find_best_column_match_no_match(self) -> None:
        """
        Test finding the best column match with no good match.

        This test verifies that the function correctly handles the case where there is
        no good match.

        The test should pass because:
        1. The function should return None for the match if the score is below the threshold
        2. The function should return a low score
        """
        # Test with a string that has no good match
        match, score = find_best_column_match(
            "completely_different",
            self.column_names,
            threshold=0.7
        )
        self.assertIsNone(match)
        self.assertTrue(score < 0.7)

    def test_find_best_column_match_empty_input(self) -> None:
        """
        Test finding the best column match with empty inputs.

        This test verifies that the function correctly handles empty inputs.

        The test should pass because:
        1. The function should return None for the match and 0.0 for the score with empty inputs
        """
        # Test with an empty query string
        match, score = find_best_column_match("", self.column_names)
        self.assertIsNone(match)
        self.assertEqual(score, 0.0)

        # Test with empty column names
        match, score = find_best_column_match("customer_name", [])
        self.assertIsNone(match)
        self.assertEqual(score, 0.0)

    def test_find_best_column_match_custom_threshold(self) -> None:
        """
        Test finding the best column match with a custom threshold.

        This test verifies that the function correctly uses a custom threshold.

        The test should pass because:
        1. The function should return a match if the score is above the threshold
        2. The function should return None if the score is below the threshold
        """
        # Test with a high threshold (should not match)
        match, score = find_best_column_match(
            "customer_nam",
            self.column_names,
            threshold=0.95
        )
        self.assertIsNone(match)
        self.assertTrue(score < 0.95)

        # Test with a low threshold (should match)
        match, score = find_best_column_match(
            "customer_nam",
            self.column_names,
            threshold=0.5
        )
        self.assertEqual(match, "customer_name")
        self.assertTrue(score >= 0.5)

    def test_analyze_and_suggest_matches(self) -> None:
        """
        Test analyzing and suggesting matches for column names.

        This test verifies that the function correctly analyzes and suggests matches.

        The test should pass because:
        1. The function should find matches for each query string
        2. The function should return a list of matches with scores
        """
        # Define query string
        query_string = "customer_nam"

        # Analyze and suggest matches
        matches = analyze_and_suggest_matches(query_string, self.column_names)

        # Verify the matches
        self.assertTrue(len(matches) > 0)

        # Verify the first match is a tuple with column name and score
        self.assertEqual(matches[0][0], "customer_name")
        self.assertTrue(0.8 <= matches[0][1] < 1.0)

    def test_analyze_and_suggest_matches_no_matches(self) -> None:
        """
        Test analyzing and suggesting matches with no good matches.

        This test verifies that the function correctly handles the case where there are
        no good matches.

        The test should pass because:
        1. The function should return matches with low scores for very different strings
        """
        # Define query string that has no good match
        query_string = "completely_different"

        # Analyze and suggest matches
        matches = analyze_and_suggest_matches(query_string, self.column_names)

        # Verify that matches were found but with low scores
        self.assertTrue(len(matches) > 0)
        self.assertTrue(matches[0][1] < 0.7)


if __name__ == "__main__":
    unittest.main()
