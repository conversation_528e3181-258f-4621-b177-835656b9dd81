# Test Coverage Guide

This project uses coverage.py (via pytest-cov) to measure test coverage. This document explains how to use the coverage tools and interpret the results.

## Quick Start

Run test coverage and view the HTML report:

```bash
make coverage
```

This command:
1. Runs all tests with coverage measurement
2. Generates a terminal report showing coverage percentages
3. Creates an HTML report in `demo/tests/reports/htmlcov/`
4. Opens the HTML report in your default browser

## Alternative Ways to Run Coverage

### Using the Run Script Directly

```bash
python scripts/run_coverage.py
```

### Using pytest-cov Directly

```bash
source .venv/bin/activate
pytest demo/tests --cov=demo --cov-report=term --cov-report=html:demo/tests/reports/htmlcov
```

### Using Coverage.py Directly

For more control, you can use coverage.py commands directly:

```bash
# Clear previous coverage data
coverage erase

# Run tests with coverage
coverage run --branch -m pytest demo/tests

# Generate reports
coverage report
coverage html
```

## Understanding Coverage Reports

The coverage report shows several metrics:

- **Stmts**: Number of statements in the code
- **Miss**: Number of statements not executed during tests
- **Cover**: Percentage of statements covered (Stmts - Miss) / Stmts
- **Branch**: (When enabled) Number of decision points
- **BrPart**: (When enabled) Number of branches partially covered
- **BrMiss**: (When enabled) Number of branches not covered

## Configuration

Coverage settings are defined in `.coveragerc` at the project root. Key settings include:

- **source**: Specifies the package to measure (demo)
- **branch**: Enables branch coverage measurement (catches missed else branches, etc.)
- **omit**: Patterns for files to exclude from coverage
- **exclude_lines**: Patterns for lines to exclude from coverage
- **concurrency**: Supports multiprocessing and threaded code

## Git Hooks and CI Integration

The project includes Git hooks and CI integration for automatic coverage checks.

### Pre-commit Hooks

Install the pre-commit hooks with:

```bash
make setup-hooks
```

This sets up the following hooks:
- Standard code quality checks (trailing whitespace, YAML validity, etc.)
- Ruff linting with auto-fixes
- Pytest checks on commit
- Coverage checks (manual trigger only)

Run the coverage hook manually with:

```bash
pre-commit run coverage-check
```

### GitHub Actions

The project includes GitHub Actions workflows that:
- Run tests with coverage on pull requests and push to main branches
- Generate coverage reports in CI logs
- Upload coverage data to Codecov (if configured)

The workflow configuration is in `.github/workflows/python-tests.yml`.

## Interpreting Results

What constitutes "good" coverage?

- **90%+ coverage**: Excellent coverage
- **80-90% coverage**: Good coverage
- **60-80% coverage**: Moderate coverage
- **Below 60%**: Needs improvement

Focus on covering critical business logic thoroughly rather than chasing 100% coverage blindly.

## Best Practices

1. **Run coverage regularly** as part of your development workflow
2. **Focus on uncovered branches** in critical code paths
3. **Don't chase 100% blindly** - some code may not be practical to test
4. **Combine with other testing strategies** like integration tests for full confidence

## Common Pitfalls

- High line coverage doesn't guarantee all code paths are tested
- Branch coverage is needed to catch missed decision paths
- Using third-party code or complex I/O may have diminishing returns for test coverage

## Reference

For more advanced usage, see the [coverage.py documentation](https://coverage.readthedocs.io/). 