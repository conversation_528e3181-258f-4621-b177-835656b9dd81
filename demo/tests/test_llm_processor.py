#!/usr/bin/env python3
"""
Unit tests for llm_processor.py module.

These tests verify the functionality of LLM processing, including:
- Detecting generation tags
- Processing LLM columns
"""

import os
import sys
import unittest
import pandas as pd
from unittest.mock import patch, MagicMock

# Add the parent directory to the path to access the demo module
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Import the module to test
from demo.processors.llm_processor import is_generation_tag, process_llm_column
from main_config import CFG


class TestLLMProcessor(unittest.TestCase):
    """Test cases for llm_processor.py module."""

    def setUp(self) -> None:
        """Set up test fixtures."""
        # Define generation tags from config
        self.generation_tags = CFG.GENERATION_TAGS

    def test_is_generation_tag(self) -> None:
        """
        Test the is_generation_tag function.

        This test verifies that the function correctly identifies generation tags.

        The test should pass because:
        1. The function should return True for values that match any generation tag
        2. The function should return False for values that don't match any generation tag
        3. The function should handle None values and empty strings
        """
        # Test with valid generation tags
        for tag in self.generation_tags:
            self.assertTrue(is_generation_tag(tag))

        # Test with "generate" which is also a valid tag
        self.assertTrue(is_generation_tag("generate"))

        # Test with non-generation tags
        self.assertFalse(is_generation_tag("not a tag"))
        self.assertFalse(is_generation_tag("generated"))

        # Test with None and empty string
        self.assertFalse(is_generation_tag(None))
        self.assertFalse(is_generation_tag(""))

    @patch('demo.processors.llm_processor.ParallelLLMDataFrameProcessor')
    def test_process_llm_column(self, mock_processor_class: MagicMock) -> None:
        """
        Test the process_llm_column function.

        This test verifies that the function correctly processes LLM columns.

        The test should pass because:
        1. The function should filter rows based on generation tags
        2. The function should create a ParallelLLMDataFrameProcessor with the correct parameters
        3. The function should execute a chain with the correct configuration
        """
        # Mock the ParallelLLMDataFrameProcessor and its execute_chain method
        mock_processor = MagicMock()
        mock_processor_class.return_value = mock_processor

        # Create a sample DataFrame with col_ref column
        sample_df = pd.DataFrame({
            'input_col': ['Input 1', 'Input 2', 'Input 3'],
            'template_col': ['Template 1', 'Template 2', 'Template 3'],
            'output_col': ['<generate>', '//', 'Existing output'],
            'col_ref': ['A1', 'A2', 'A3']
        })

        # Mock the result of execute_chain
        result_df = sample_df.copy()
        result_df['output_col'] = ['Generated 1', 'Generated 2', 'Existing output']
        mock_processor.execute_chain.return_value = result_df.iloc[:2]  # Only return the first two rows (with generation tags)

        # Create a direct_update_map to store updates
        direct_update_map = {}

        # Define indices with generation tags
        generate_indices = [0, 1]  # First two rows have generation tags

        # Process the LLM column
        process_llm_column(
            prompt_inputs=sample_df,
            output_col='output_col',
            template_col='template_col',
            model='gpt-4o',
            temperature=0.7,
            max_tokens=1000,
            generate_indices=generate_indices,
            direct_update_map=direct_update_map
        )

        # Verify that ParallelLLMDataFrameProcessor was created with the correct parameters
        mock_processor_class.assert_called_once_with(
            def_model='gpt-4o',
            def_temperature=0.7,
            def_max_tokens=1000,
            def_async_rate_limit=10,
            def_thread_rate_limit=5
        )

        # Verify that execute_chain was called with the correct parameters
        mock_processor.execute_chain.assert_called_once()

    @patch('demo.processors.llm_processor.ParallelLLMDataFrameProcessor')
    def test_process_llm_column_no_rows(self, mock_processor_class: MagicMock) -> None:
        """
        Test the process_llm_column function with no rows to process.

        This test verifies that the function correctly handles the case where there are no rows to process.

        The test should pass because:
        1. The function should return the original direct_update_map unchanged
        2. The function should not create a ParallelLLMDataFrameProcessor
        """
        # Create a sample DataFrame
        sample_df = pd.DataFrame({
            'input_col': ['Input 1', 'Input 2', 'Input 3'],
            'template_col': ['Template 1', 'Template 2', 'Template 3'],
            'output_col': ['Existing 1', 'Existing 2', 'Existing 3'],
            'col_ref': ['A1', 'A2', 'A3']
        })

        # Create a direct_update_map to store updates
        direct_update_map = {}

        # Define empty indices with generation tags
        generate_indices = []  # No rows have generation tags

        # Process the LLM column
        updated_map = process_llm_column(
            prompt_inputs=sample_df,
            output_col='output_col',
            template_col='template_col',
            model='gpt-4o',
            temperature=0.7,
            max_tokens=1000,
            generate_indices=generate_indices,
            direct_update_map=direct_update_map
        )

        # Verify that ParallelLLMDataFrameProcessor was not created
        mock_processor_class.assert_not_called()

        # Verify that the direct_update_map was not updated
        self.assertEqual(updated_map, {})


if __name__ == "__main__":
    unittest.main()
