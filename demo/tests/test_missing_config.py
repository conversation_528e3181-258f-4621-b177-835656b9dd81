#!/usr/bin/env python3
"""
Test script to verify that the application fails when a configuration name is not found in the first column.
"""

import os
import pandas as pd
import sys
import unittest
from unittest.mock import patch

# Add the parent directory to the path to access the demo module
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from demo.processors.column_config_processor import get_config_by_name
from main_config import CFG

class TestMissingConfig(unittest.TestCase):
    """Test cases for error handling in column_config_processor.py when config is missing."""
    
    @patch('sys.exit')
    @patch('demo.processors.column_config_processor.display_panel')
    def test_missing_template_config(self, mock_display: unittest.mock.Mock, mock_exit: unittest.mock.Mock) -> None:
        """Test get_config_by_name with a DataFrame missing the TEMPLATE configuration."""
        print("Testing get_config_by_name with a DataFrame missing the TEMPLATE configuration in the first column...")
        print("This should fail with a clear error message.")
        
        # Create a test DataFrame with a first column but missing the TEMPLATE configuration
        df = pd.DataFrame(
            {
                "first_column": ["Header", "Row 1", "Row 2", "NOT_TEMPLATE", "MODEL", "TEMPERATURE", "MAX_TOKENS", "FLAGS"],
                "B": ["py-llm", "Value 1", "Value 2", "Template value", "gpt-4o", "0.7", "1000", ""],
                "C": ["py-llm", "Value 1", "Value 2", "Template value", "gpt-4o", "0.7", "1000", ""],
            }
        )
        
        try:
            # This should fail because TEMPLATE is not in the first column
            get_config_by_name(df, CFG.CONFIG_NAMES["TEMPLATE"], ["C"])
        except IndexError:
            # This is expected - when TEMPLATE is missing, it will cause an IndexError
            pass
        
        # Verify that display_panel was called with an error message
        mock_display.assert_called()
        
        # Test passes because we verified the error behavior
        print("SUCCESS: The function failed as expected when TEMPLATE was not found in the first column.")

if __name__ == "__main__":
    unittest.main()
