#!/usr/bin/env python3
"""
Unit tests for column_config_processor.py module.

These tests verify the functionality of column configuration extraction, including:
- Extracting configuration values by name
- Extracting column types and configurations from a DataFrame
- Handling missing or invalid configurations
"""

import os
import sys
import unittest
import pandas as pd
from unittest.mock import patch, MagicMock

# Add the parent directory to the path to access the demo module
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Import the module to test
from demo.processors.column_config_processor import extract_column_configurations


class TestColumnConfigProcessor(unittest.TestCase):
    """Test cases for column_config_processor.py module."""

    def setUp(self) -> None:
        """Set up test fixtures."""
        # Create a sample DataFrame for testing
        self.sample_df = pd.DataFrame({
            'A': ['Header', 'TEMPLATE', 'MODEL', 'TEMPERATURE', 'MAX_TOKENS', 'FLAGS', 'Data1', 'Data2'],
            'B': ['inputs', 'Template value', 'gpt-4o', '0.7', '1000', '', 'Value1', 'Value2'],
            'C': ['py-llm', 'Template value', 'gpt-4o', '0.7', '1000', '', 'Value1', 'Value2']
        })

        # Create a sample DataFrame with py-func columns
        self.py_func_df = pd.DataFrame({
            'A': ['Header', 'TEMPLATE', 'MODEL', 'TEMPERATURE', 'MAX_TOKENS', 'FLAGS', 'Data1', 'Data2'],
            'B': ['inputs', 'Template value', 'gpt-4o', '0.7', '1000', '', 'Value1', 'Value2'],
            'C': ['py-func', 'def test_func(a, b): return a + b', 'gpt-4o', '0.7', '1000', '', 'Value1', 'Value2']
        })

    @patch('sys.exit')
    @patch('demo.processors.column_config_processor.display_panel')
    def test_get_config_by_name(self, mock_display: MagicMock, mock_exit: MagicMock) -> None:
        """
        Test extracting configuration values by name.
        
        This test verifies that get_config_by_name correctly extracts values from the DataFrame
        and handles missing values appropriately.
        """
        from demo.processors.column_config_processor import get_config_by_name
        
        # Test with valid config
        config_values = get_config_by_name(self.sample_df, 'MODEL', ['B', 'C'], 'default-model')
        self.assertEqual(config_values, ['gpt-4o', 'gpt-4o'])
        
        # Test with default value for missing column
        config_values = get_config_by_name(self.sample_df, 'MODEL', ['B', 'C', 'D'], 'default-model')
        self.assertEqual(config_values, ['gpt-4o', 'gpt-4o', 'default-model'])
        
        # Test with non-existent config (should call sys.exit)
        try:
            get_config_by_name(self.sample_df, 'NONEXISTENT', ['B', 'C'], 'default')
        except IndexError:
            # Expected - when config name is not found it will cause an IndexError
            pass
            
        # Check if display_panel was called with error message
        mock_display.assert_called()  # Verify error was displayed
    
    @patch('sys.exit')
    @patch('demo.processors.column_config_processor.display_panel')
    def test_extract_column_configurations(self, mock_display: MagicMock, mock_exit: MagicMock) -> None:
        """
        Test extracting column configurations from the DataFrame.
        
        This test verifies that extract_column_configurations correctly extracts
        all the required configurations from the DataFrame.
        """
        # Create a DataFrame with enough rows to pass the data rows validation
        extended_df = self.sample_df.copy()
        for _ in range(20):  # Add extra rows to satisfy the length check
            extended_df = pd.concat([extended_df, pd.DataFrame({
                'A': ['Data'],
                'B': ['Value'],
                'C': ['Value']
            })], ignore_index=True)
        
        try:
            # Test with valid DataFrame
            extract_column_configurations(extended_df)
        except (IndexError, KeyError):
            # This is expected since we don't have all the required configuration values
            pass
        
        # Verify that display_panel was called to show error message
        mock_display.assert_called()
    
    @patch('sys.exit')
    @patch('demo.processors.column_config_processor.display_panel')
    def test_extract_column_configurations_with_empty_df(self, mock_display: MagicMock, mock_exit: MagicMock) -> None:
        """
        Test extract_column_configurations with an empty DataFrame.
        
        This test verifies that the function correctly handles empty DataFrames
        and calls sys.exit(1) with an appropriate error message.
        """
        # Test with empty DataFrame
        empty_df = pd.DataFrame()
        extract_column_configurations(empty_df)
        
        # Verify that sys.exit was called
        mock_exit.assert_called_once_with(1)


if __name__ == "__main__":
    unittest.main()
