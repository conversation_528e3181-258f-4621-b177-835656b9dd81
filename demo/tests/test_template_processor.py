#!/usr/bin/env python3
"""
Unit tests for template_processor.py module.

These tests verify the functionality of template filling, including:
- Filling templates with values from a row
- Handling missing variables
- Handling fuzzy matching for variable names
"""

import os
import sys
import unittest
from unittest.mock import patch
from typing import Dict, List, Any

import pandas as pd

# Add the parent directory to the path to access the demo module
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Import the module to test
try:
    from demo.processors.template_processor import fill_template
except ImportError:
    # Mock for linting purposes
    def fill_template(
        _row: pd.Series,
        _template: str | None | int,  # Using more specific types based on test usage
        _input_cols: List[str]
    ) -> Dict[str, Any]:
        """Mock function for linting."""
        return {
            "filled_template": "",
            "expected_vars": [],
            "missing_vars": [],
            "replaced_vars": [],
            "fuzzy_matched_vars": []
        }


class TestTemplateProcessor(unittest.TestCase):
    """Test cases for template_processor.py module."""

    def setUp(self) -> None:
        """Set up test fixtures."""
        # Create a sample row for testing
        self.sample_row = pd.Series({
            'name': '<PERSON> Doe',
            'age': 30,
            'occupation': 'Software Engineer',
            'location': 'New York'
        })

        # Define input columns
        self.input_cols = ['name', 'age', 'occupation', 'location']

    @patch('demo.processors.template_processor.console')
    def test_fill_template_basic(self, _mock_console) -> None:
        """
        Test basic template filling.

        This test verifies that the function correctly fills a template with values from a row.

        The test should pass because:
        1. The function should replace all variables in the template with their values
        2. The function should return the filled template and metadata about replaced variables
        """
        # Define a template with variables
        template = "{{name}} is a {{age}}-year-old {{occupation}} in {{location}}."

        # Fill the template
        result = fill_template(self.sample_row, template, self.input_cols)

        # Verify the filled template
        expected_filled = "John Doe is a 30-year-old Software Engineer in New York."
        self.assertEqual(result["filled_template"], expected_filled)

        # Verify the metadata
        self.assertEqual(set(result["expected_vars"]), {'name', 'age', 'occupation', 'location'})
        self.assertEqual(result["missing_vars"], [])
        self.assertEqual(set(result["replaced_vars"]), {'name', 'age', 'occupation', 'location'})
        self.assertEqual(result["fuzzy_matched_vars"], [])

    @patch('demo.processors.template_processor.console')
    def test_fill_template_missing_vars(self, _mock_console) -> None:
        """
        Test template filling with missing variables.

        This test verifies that the function correctly handles missing variables.

        The test should pass because:
        1. The function should replace variables that exist in the row
        2. The function should track missing variables
        3. The function should replace missing variables with empty strings
        """
        # Define a template with a variable that doesn't exist in the row
        template = (
            "{{name}} is a {{age}}-year-old {{occupation}} in {{location}} "
            "with {{missing_var}}."
        )

        # Fill the template
        result = fill_template(self.sample_row, template, self.input_cols)

        # Verify the filled template
        expected_filled = "John Doe is a 30-year-old Software Engineer in New York with ."
        self.assertEqual(result["filled_template"], expected_filled)

        # Verify the metadata
        expected_vars = {'name', 'age', 'occupation', 'location', 'missing_var'}
        self.assertEqual(set(result["expected_vars"]), expected_vars)
        self.assertEqual(result["missing_vars"], ['missing_var'])
        self.assertEqual(set(result["replaced_vars"]), {'name', 'age', 'occupation', 'location'})

    @patch('demo.processors.template_processor.console')
    def test_fill_template_fuzzy_matching(self, _mock_console) -> None:
        """
        Test template filling with fuzzy matching for variable names.

        This test verifies that the function correctly handles fuzzy matching for variable names.

        The test should pass because:
        1. The function should use fuzzy matching to find close matches for misspelled variables
        2. The function should replace misspelled variables with values from their close matches
        3. The function should track fuzzy matched variables
        """
        # Define a template with misspelled variables
        template = "{{nam}} is a {{aeg}}-year-old {{ocupation}} in {{locaton}}."

        # Fill the template
        result = fill_template(self.sample_row, template, self.input_cols)

        # Verify that fuzzy matching was used
        self.assertTrue(len(result["fuzzy_matched_vars"]) > 0)

        # Verify that variables were replaced
        self.assertTrue(len(result["replaced_vars"]) > 0)

        # Verify that the filled template contains the values
        self.assertIn("John Doe", result["filled_template"])
        self.assertIn("Software Engineer", result["filled_template"])
        self.assertIn("New York", result["filled_template"])

    @patch('demo.processors.template_processor.console')
    def test_fill_template_empty_values(self, _mock_console) -> None:
        """
        Test template filling with empty values.

        This test verifies that the function correctly handles empty values in the row.

        The test should pass because:
        1. The function should treat empty values as missing variables
        2. The function should track variables with empty values as missing
        """
        # Create a row with empty values
        row_with_empty = pd.Series({
            'name': 'John Doe',
            'age': '',  # Empty string
            'occupation': None,  # None value
            'location': 'New York'
        })

        # Define a template
        template = "{{name}} is a {{age}}-year-old {{occupation}} in {{location}}."

        # Fill the template
        result = fill_template(row_with_empty, template, self.input_cols)

        # Verify that the filled template contains the non-empty values
        self.assertIn("John Doe", result["filled_template"])
        self.assertIn("New York", result["filled_template"])

        # Verify the metadata
        self.assertEqual(set(result["expected_vars"]), {'name', 'age', 'occupation', 'location'})
        self.assertTrue('age' in result["missing_vars"])
        self.assertTrue('occupation' in result["missing_vars"])
        self.assertTrue('name' in result["replaced_vars"])
        self.assertTrue('location' in result["replaced_vars"])

    @patch('demo.processors.template_processor.console')
    def test_fill_template_invalid_template(self, _mock_console) -> None:
        """
        Test template filling with an invalid template.

        This test verifies that the function correctly handles invalid templates.

        The test should pass because:
        1. The function should handle None templates
        2. The function should handle non-string templates
        3. The function should return an error message in the filled template
        """
        # Test with None template
        result_none = fill_template(self.sample_row, None, self.input_cols)
        self.assertTrue("ERROR" in result_none["filled_template"])

        # Test with non-string template
        result_non_string = fill_template(self.sample_row, 123, self.input_cols)
        self.assertTrue("ERROR" in result_non_string["filled_template"])


if __name__ == "__main__":
    unittest.main()
