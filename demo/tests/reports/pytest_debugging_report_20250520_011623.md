# Pytest Debugging Report

## Summary of Issues

1. **Dependency Management Issues**:
   - Dependencies aren't properly installed in the Python environment being used for testing
   - Key packages missing: `pandas`, `rich`, `loguru`
   - Project uses `uv` for dependency management rather than pip

2. **Test Discovery Problems**:
   - Tests exist in multiple locations (`/tests/` and `/demo/tests/`)
   - Custom test runner in `demo/tests/run_tests.py` uses unittest, not pytest
   - Missing Python path configuration for test discovery

3. **Test Implementation Issues**:
   - Some tests potentially terminate the process with `sys.exit(1)` calls
   - Test failures related to configuration errors
   - Inconsistent test implementation between unittest and pytest styles

## Root Causes

1. **Environment Setup**:
   - The project requires Python 3.12+ (specified in pyproject.toml)
   - Dependencies need to be installed using `uv sync --dev` command
   - Virtual environment needs to be activated before running tests

2. **Dual Testing Frameworks**:
   - The project uses both unittest (in demo/tests) and pytest (in /tests)
   - The custom test runner in demo/tests/run_tests.py is designed for unittest
   - Pyte<PERSON> is trying to discover and run unittest tests, causing compatibility issues

3. **Test Implementation**:
   - Some tests intentionally test error conditions that call `sys.exit(1)`
   - Configuration validation errors are expected in some tests but cause pytest to fail

## Recommendations

1. **Environment Setup**:
   - Ensure Python 3.12+ is active when running tests
   - Run `uv sync --dev` to install all dependencies including dev dependencies
   - Activate the virtual environment before running tests: `source .venv/bin/activate`

2. **Running Tests**:
   - For demo module tests: Use the custom test runner: `python demo/tests/run_tests.py`
   - For pytest-style tests: Run `pytest tests/` to target only the pytest tests
   - Avoid running `pytest` from the root directory which causes issues with unittest tests

3. **Test Organization**:
   - Consider standardizing on one test framework (pytest or unittest)
   - Add proper pytest configuration (conftest.py, pytest.ini) if standardizing on pytest
   - Modify tests with `sys.exit(1)` calls to raise exceptions instead for better pytest compatibility

4. **Documentation Updates**:
   - Add clear instructions in CLAUDE.md for running both types of tests
   - Document the test framework choices and how to properly run each type


By addressing these issues, the test suite should run more reliably and provide better feedback on code quality.
