#!/usr/bin/env python3
"""
Test runner script for running all unit tests in the demo/tests directory.

This script discovers and runs all test files in the demo/tests directory.
It can also generate coverage reports when run with the --coverage flag.
"""

import os
import sys
import unittest
import argparse
import subprocess
from rich.console import Console
from rich.panel import Panel

# Add the parent directory to the path to access the demo module
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Initialize console for rich output
console = Console()


def run_tests(with_coverage: bool = False) -> bool:
    """
    Discover and run all tests in the demo/tests directory.

    Args:
        with_coverage: Whether to run with coverage reporting

    Returns:
        bool: True if all tests passed, False otherwise
    """
    # Get the directory containing this script
    test_dir = os.path.dirname(os.path.abspath(__file__))
    project_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

    # Display a header
    console.print(
        Panel(
            f"[bold blue]Running all unit tests in demo/tests{' with coverage' if with_coverage else ''}[/]", title="Test Runner", border_style="blue"
        )
    )

    if with_coverage:
        # Run tests through pytest with coverage
        cmd = [
            sys.executable,
            "-m",
            "pytest",
            test_dir,
            "--cov=demo",
            "--cov-config=.coveragerc",  # Explicitly use the config file
            "--cov-report=term",
            "--cov-report=html:demo/tests/reports/htmlcov",
            "-v",
        ]
        result = subprocess.run(cmd, cwd=project_dir)
        success = result.returncode == 0

        if success:
            console.print(
                Panel(
                    "[bold green]All tests passed![/]\nCoverage report generated in demo/tests/reports/htmlcov",
                    title="Test Results",
                    border_style="green",
                )
            )
        else:
            console.print(Panel("[bold red]Tests failed![/]", title="Test Results", border_style="red"))

        return success
    else:
        # Run using unittest (original method)
        loader = unittest.TestLoader()
        suite = loader.discover(test_dir, pattern="test_*.py")

        # Run the tests
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)

        # Display a summary
        if result.wasSuccessful():
            console.print(Panel(f"[bold green]All tests passed![/]\nRan {result.testsRun} tests", title="Test Results", border_style="green"))
            return True
        else:
            console.print(
                Panel(
                    f"[bold red]Tests failed![/]\nRan {result.testsRun} tests\nFailures: {len(result.failures)}\nErrors: {len(result.errors)}",
                    title="Test Results",
                    border_style="red",
                )
            )
            return False


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Run unit tests with optional coverage reporting")
    parser.add_argument("--coverage", "-c", action="store_true", help="Generate coverage reports")
    args = parser.parse_args()

    success = run_tests(with_coverage=args.coverage)
    sys.exit(0 if success else 1)
