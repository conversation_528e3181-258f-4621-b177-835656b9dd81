#!/usr/bin/env python3
"""
Unit tests for display.py module.

These tests verify the functionality of display utilities, including:
- Displaying panels
- Displaying tables
- Displaying skipped columns
- Displaying processing status
"""

import os
import sys
import unittest
from unittest.mock import patch, MagicMock

# Add the parent directory to the path to access the demo module
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Import the module to test
from demo.utils.display import (
    display_panel,
    display_table,
    display_skipped_columns,
    display_column_scanning,
    display_processing_status,
    display_llm_processing_info,
    display_llm_processing_complete,
    display_processing_summary
)


class TestDisplay(unittest.TestCase):
    """Test cases for display.py module."""

    @patch('demo.utils.display.console')
    def test_display_panel(self, mock_console: MagicMock) -> None:
        """
        Test displaying a panel.

        This test verifies that the function correctly displays a panel.

        The test should pass because:
        1. The function should create a Panel with the correct content and style
        2. The function should call console.print with the Panel
        """
        # Display a panel
        display_panel("Test content", title="Test Title", style="blue")

        # Verify that console.print was called
        mock_console.print.assert_called_once()

        # Get the Panel object that was passed to console.print
        panel = mock_console.print.call_args[0][0]

        # Verify the Panel properties
        self.assertEqual(panel.title, "Test Title")
        self.assertEqual(panel.border_style, "blue")

    @patch('demo.utils.display.console')
    @patch('demo.utils.display.Table')
    def test_display_table(self, mock_table_class: MagicMock, mock_console: MagicMock) -> None:
        """
        Test displaying a table.

        This test verifies that the function correctly displays a table.

        The test should pass because:
        1. The function should create a Table with the correct columns and rows
        2. The function should call console.print with the Table
        """
        # Mock the Table class and its methods
        mock_table = MagicMock()
        mock_table_class.return_value = mock_table

        # Define columns and rows
        columns = ["Column 1", "Column 2"]
        rows = [["Value 1", "Value 2"], ["Value 3", "Value 4"]]

        # Display a table
        display_table(title="Test Table", columns=columns, rows=rows)

        # Verify that Table was created with the correct title
        mock_table_class.assert_called_once()

        # Verify that add_column was called for each column
        self.assertEqual(mock_table.add_column.call_count, 2)

        # Verify that add_row was called for each row
        self.assertEqual(mock_table.add_row.call_count, 2)

        # Verify that console.print was called with the Table
        mock_console.print.assert_called_once_with(mock_table)

    @patch('demo.utils.display.console')
    @patch('demo.utils.display.Panel')
    def test_display_skipped_columns_empty(self, mock_panel_class: MagicMock, mock_console: MagicMock) -> None:
        """
        Test displaying skipped columns when there are none.

        This test verifies that the function correctly handles the case where there are no skipped columns.

        The test should pass because:
        1. The function should not create a Panel or call console.print
        """
        # Display skipped columns with an empty list
        display_skipped_columns([])

        # Verify that Panel was not created
        mock_panel_class.assert_not_called()

        # Verify that console.print was not called
        mock_console.print.assert_not_called()

    @patch('demo.utils.display.console')
    @patch('demo.utils.display.Panel')
    def test_display_skipped_columns_with_data(self, mock_panel_class: MagicMock, mock_console: MagicMock) -> None:
        """
        Test displaying skipped columns with data.

        This test verifies that the function correctly displays skipped columns.

        The test should pass because:
        1. The function should create a Panel with the skipped columns information
        2. The function should call console.print with the Panel
        """
        # Mock the Panel class
        mock_panel = MagicMock()
        mock_panel_class.return_value = mock_panel

        # Define skipped columns as a list of strings
        skipped_columns = ["Column1", "Column2"]

        # Display skipped columns
        display_skipped_columns(skipped_columns)

        # Verify that Panel was created
        mock_panel_class.assert_called_once()

        # Verify that console.print was called with the Panel
        mock_console.print.assert_called_once_with(mock_panel)

    @patch('demo.utils.display.console')
    @patch('demo.utils.display.Panel')
    def test_display_column_scanning(self, mock_panel_class: MagicMock, mock_console: MagicMock) -> None:
        """
        Test displaying column scanning information.

        This test verifies that the function correctly displays column scanning information.

        The test should pass because:
        1. The function should create a Panel with the column scanning information
        2. The function should call console.print with the Panel
        """
        # Mock the Panel class
        mock_panel = MagicMock()
        mock_panel_class.return_value = mock_panel

        # Display column scanning information
        display_column_scanning(num_columns=5)

        # Verify that Panel was created
        mock_panel_class.assert_called_once()

        # Verify that console.print was called with the Panel
        mock_console.print.assert_called_once_with(mock_panel)

    @patch('demo.utils.display.console')
    @patch('demo.utils.display.Panel')
    def test_display_processing_status(self, mock_panel_class: MagicMock, mock_console: MagicMock) -> None:
        """
        Test displaying processing status.

        This test verifies that the function correctly displays processing status.

        The test should pass because:
        1. The function should create a Panel with the processing status information
        2. The function should call console.print with the Panel
        """
        # Mock the Panel class
        mock_panel = MagicMock()
        mock_panel_class.return_value = mock_panel

        # Display processing status
        display_processing_status(column_name="TestColumn", num_rows=10)

        # Verify that Panel was created
        mock_panel_class.assert_called_once()

        # Verify that console.print was called with the Panel
        mock_console.print.assert_called_once_with(mock_panel)

    @patch('demo.utils.display.console')
    @patch('demo.utils.display.Panel')
    def test_display_llm_processing_info(self, mock_panel_class: MagicMock, mock_console: MagicMock) -> None:
        """
        Test displaying LLM processing information.

        This test verifies that the function correctly displays LLM processing information.

        The test should pass because:
        1. The function should create a Panel with the LLM processing information
        2. The function should call console.print with the Panel
        """
        # Mock the Panel class
        mock_panel = MagicMock()
        mock_panel_class.return_value = mock_panel

        # Display LLM processing information
        display_llm_processing_info(
            column_name="Column1",
            model="gpt-4o",
            temperature=0.7,
            max_tokens=1000,
            num_rows=10
        )

        # Verify that Panel was created
        mock_panel_class.assert_called_once()

        # Verify that console.print was called with the Panel
        mock_console.print.assert_called_once_with(mock_panel)

    @patch('demo.utils.display.console')
    @patch('demo.utils.display.Panel')
    def test_display_llm_processing_complete(self, mock_panel_class: MagicMock, mock_console: MagicMock) -> None:
        """
        Test displaying LLM processing completion.

        This test verifies that the function correctly displays LLM processing completion.

        The test should pass because:
        1. The function should create a Panel with the LLM processing completion information
        2. The function should call console.print with the Panel
        """
        # Mock the Panel class
        mock_panel = MagicMock()
        mock_panel_class.return_value = mock_panel

        # Display LLM processing completion
        display_llm_processing_complete(
            column_name="Column1",
            processed_rows=10,
            total_rows=10,
            processing_time=5.0
        )

        # Verify that Panel was created
        mock_panel_class.assert_called_once()

        # Verify that console.print was called with the Panel
        mock_console.print.assert_called_once_with(mock_panel)

    @patch('demo.utils.display.console')
    @patch('demo.utils.display.Table')
    def test_display_processing_summary(self, mock_table_class: MagicMock, mock_console: MagicMock) -> None:
        """
        Test displaying processing summary.

        This test verifies that the function correctly displays processing summary.

        The test should pass because:
        1. The function should create a Table with the processing summary information
        2. The function should call console.print with the Table
        """
        # Mock the Table class and its methods
        mock_table = MagicMock()
        mock_table_class.return_value = mock_table

        # Display processing summary
        display_processing_summary(
            file_path="test.xlsx",
            sheet_name="Sheet1",
            processed_columns=["Column1", "Column2"],
            updates_made=10,
            gsheet_updated=True,
            is_gsheet=True
        )

        # Verify that Table was created
        mock_table_class.assert_called_once()

        # Verify that console.print was called with the Table
        mock_console.print.assert_called_once_with(mock_table)


if __name__ == "__main__":
    unittest.main()
