#!/usr/bin/env python3
"""
Unit tests for sheet_utils.py module.

These tests verify the functionality of sheet utilities, including:
- Extracting sheet IDs from Google Sheets URLs
"""

import os
import sys
import unittest
from unittest.mock import patch
from typing import Optional

# Add the parent directory to the path to access the demo module
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Import the module to test
try:
    from demo.utils.sheet_utils import extract_sheet_id
except ImportError:
    # Mock for linting purposes
    def extract_sheet_id(_: str) -> Optional[str]:
        """Mock function for linting."""
        return None


class TestSheetUtils(unittest.TestCase):
    """Test cases for sheet_utils.py module."""

    @patch('demo.utils.sheet_utils._get_sheet_id_from_url')
    @patch('demo.utils.sheet_utils.display_panel')
    def test_extract_sheet_id_valid(self, mock_display, mock_get_id) -> None:
        """
        Test extracting a sheet ID from a valid Google Sheets URL.

        This test verifies that the function correctly extracts a sheet ID from a valid URL.

        The test should pass because:
        1. The function should call _get_sheet_id_from_url with the URL
        2. The function should return the sheet ID
        3. The function should not display an error panel
        """
        # Mock _get_sheet_id_from_url to return a valid sheet ID
        mock_get_id.return_value = "1abc123"

        # Extract the sheet ID
        sheet_id = extract_sheet_id("https://docs.google.com/spreadsheets/d/1abc123/edit")

        # Verify that _get_sheet_id_from_url was called with the URL
        mock_get_id.assert_called_once_with("https://docs.google.com/spreadsheets/d/1abc123/edit")

        # Verify that the sheet ID was returned
        self.assertEqual(sheet_id, "1abc123")

        # Verify that display_panel was not called
        mock_display.assert_not_called()

    @patch('demo.utils.sheet_utils._get_sheet_id_from_url')
    @patch('demo.utils.sheet_utils.display_panel')
    @patch('demo.utils.sheet_utils.sys.exit')
    def test_extract_sheet_id_invalid(self, mock_exit, mock_display, mock_get_id) -> None:
        """
        Test extracting a sheet ID from an invalid Google Sheets URL.

        This test verifies that the function correctly handles invalid URLs.

        The test should pass because:
        1. The function should call _get_sheet_id_from_url with the URL
        2. The function should display an error panel
        3. The function should exit the program
        """
        # Mock _get_sheet_id_from_url to return None (invalid URL)
        mock_get_id.return_value = None

        # Extract the sheet ID
        extract_sheet_id("https://invalid-url.com")

        # Verify that _get_sheet_id_from_url was called with the URL
        mock_get_id.assert_called_once_with("https://invalid-url.com")

        # Verify that display_panel was called with an error message
        mock_display.assert_called_once()

        # Verify that sys.exit was called
        mock_exit.assert_called_once_with(1)

    @patch('demo.utils.sheet_utils._get_sheet_id_from_url')
    @patch('demo.utils.sheet_utils.display_panel')
    @patch('demo.utils.sheet_utils.sys.exit')
    def test_extract_sheet_id_exception(self, mock_exit, mock_display, mock_get_id) -> None:
        """
        Test extracting a sheet ID when an exception occurs.

        This test verifies that the function correctly handles exceptions.

        The test should pass because:
        1. The function should call _get_sheet_id_from_url with the URL
        2. The function should catch the exception
        3. The function should display an error panel
        4. The function should exit the program
        """
        # Mock _get_sheet_id_from_url to raise an exception
        mock_get_id.side_effect = Exception("Test exception")

        # Extract the sheet ID
        extract_sheet_id("https://docs.google.com/spreadsheets/d/1abc123/edit")

        # Verify that _get_sheet_id_from_url was called with the URL
        mock_get_id.assert_called_once_with("https://docs.google.com/spreadsheets/d/1abc123/edit")

        # Verify that display_panel was called with an error message
        mock_display.assert_called_once()

        # Verify that sys.exit was called
        mock_exit.assert_called_once_with(1)


if __name__ == "__main__":
    unittest.main()
