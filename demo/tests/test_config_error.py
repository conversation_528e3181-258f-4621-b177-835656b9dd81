#!/usr/bin/env python3
"""
Test script to verify that the application fails when the DataFrame is empty.
"""

import os
import pandas as pd
import sys
import unittest
from unittest.mock import patch

# Add the parent directory to the path to access the demo module
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from demo.processors.column_config_processor import get_config_by_name
from main_config import CFG

class TestConfigError(unittest.TestCase):
    """Test cases for error handling in column_config_processor.py."""
    
    @patch('sys.exit')
    @patch('demo.processors.column_config_processor.display_panel')
    def test_empty_dataframe(self, mock_display: unittest.mock.Mock, mock_exit: unittest.mock.Mock) -> None:
        """Test get_config_by_name with an empty DataFrame."""
        print("Testing get_config_by_name with an empty DataFrame...")
        print("This should fail with a clear error message.")
        
        # Create an empty DataFrame
        df = pd.DataFrame()
        
        try:
            # Try to get config from an empty DataFrame - this will fail but exception will be caught
            get_config_by_name(df, CFG.CONFIG_NAMES["TEMPLATE"], ["C"])
        except IndexError:
            # This is expected - the empty DataFrame will cause an IndexError
            pass
            
        # Display function should have been called with error message 
        mock_display.assert_called()
        
        # Test passes because we verified error handling behavior
        print("SUCCESS: The function failed as expected when the DataFrame had no columns.")

if __name__ == "__main__":
    unittest.main()
