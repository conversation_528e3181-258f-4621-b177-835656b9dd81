#!/usr/bin/env python3
"""
Unit tests for file_utils.py module.

These tests verify the functionality of file operations, including:
- Detecting Google Sheets URLs
- Saving DataFrames to files
- Loading DataFrames from files
"""

import os
import sys
import unittest
import pandas as pd
from pathlib import Path
import tempfile
import shutil

# Add the parent directory to the path to access the demo module
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Import the module to test
from demo.utils.file_utils import is_google_sheets_url, save_df_to_file, load_df_from_file


class TestFileUtils(unittest.TestCase):
    """Test cases for file_utils.py module."""

    def setUp(self) -> None:
        """Set up test fixtures."""
        # Create a temporary directory for test files
        self.test_dir = tempfile.mkdtemp()
        
        # Create a sample DataFrame for testing
        self.sample_df = pd.DataFrame({
            'A': [1, 2, 3],
            'B': ['a', 'b', 'c']
        })

    def tearDown(self) -> None:
        """Tear down test fixtures."""
        # Remove the temporary directory and its contents
        shutil.rmtree(self.test_dir)

    def test_is_google_sheets_url(self) -> None:
        """
        Test the is_google_sheets_url function.
        
        This test verifies that the function correctly identifies Google Sheets URLs
        and returns False for other URLs or non-URL strings.
        
        The test should pass because:
        1. The function should return True for valid Google Sheets URLs
        2. The function should return False for non-Google Sheets URLs
        3. The function should return False for non-URL strings
        4. The function should handle None values gracefully
        """
        # Valid Google Sheets URLs
        self.assertTrue(is_google_sheets_url("https://docs.google.com/spreadsheets/d/1abc123/edit"))
        self.assertTrue(is_google_sheets_url("https://docs.google.com/spreadsheets/d/1abc123/edit#gid=0"))
        
        # Non-Google Sheets URLs
        self.assertFalse(is_google_sheets_url("https://docs.google.com/document/d/1abc123/edit"))
        self.assertFalse(is_google_sheets_url("https://example.com"))
        
        # Non-URL strings
        self.assertFalse(is_google_sheets_url("not a url"))
        self.assertFalse(is_google_sheets_url(""))
        
        # None value
        self.assertFalse(is_google_sheets_url(None))

    def test_save_df_to_file_csv(self) -> None:
        """
        Test saving a DataFrame to a CSV file.
        
        This test verifies that the save_df_to_file function correctly saves
        a DataFrame to a CSV file and that the file can be read back correctly.
        
        The test should pass because:
        1. The function should create a CSV file at the specified path
        2. The created file should contain the correct data
        3. The function should handle creating parent directories if they don't exist
        """
        # Define a path for the test CSV file
        csv_path = Path(self.test_dir) / "test_data" / "test.csv"
        
        # Save the DataFrame to the CSV file
        save_df_to_file(self.sample_df, csv_path)
        
        # Verify the file exists
        self.assertTrue(csv_path.exists())
        
        # Read the file back and verify the contents
        df_read = pd.read_csv(csv_path)
        pd.testing.assert_frame_equal(df_read, self.sample_df)

    def test_save_df_to_file_excel(self) -> None:
        """
        Test saving a DataFrame to an Excel file.
        
        This test verifies that the save_df_to_file function correctly saves
        a DataFrame to an Excel file and that the file can be read back correctly.
        
        The test should pass because:
        1. The function should create an Excel file at the specified path
        2. The created file should contain the correct data
        3. The function should handle creating parent directories if they don't exist
        """
        # Define a path for the test Excel file
        excel_path = Path(self.test_dir) / "test_data" / "test.xlsx"
        
        # Save the DataFrame to the Excel file
        save_df_to_file(self.sample_df, excel_path)
        
        # Verify the file exists
        self.assertTrue(excel_path.exists())
        
        # Read the file back and verify the contents
        df_read = pd.read_excel(excel_path)
        pd.testing.assert_frame_equal(df_read, self.sample_df)

    def test_load_df_from_file_csv(self) -> None:
        """
        Test loading a DataFrame from a CSV file.
        
        This test verifies that the load_df_from_file function correctly loads
        a DataFrame from a CSV file.
        
        The test should pass because:
        1. The function should read the CSV file correctly
        2. The loaded DataFrame should match the original data
        """
        # Define a path for the test CSV file
        csv_path = Path(self.test_dir) / "test.csv"
        
        # Save the DataFrame to the CSV file directly using pandas
        self.sample_df.to_csv(csv_path, index=False)
        
        # Load the DataFrame using the function
        df_loaded = load_df_from_file(csv_path)
        
        # Verify the loaded DataFrame matches the original
        pd.testing.assert_frame_equal(df_loaded, self.sample_df)

    def test_load_df_from_file_excel(self) -> None:
        """
        Test loading a DataFrame from an Excel file.
        
        This test verifies that the load_df_from_file function correctly loads
        a DataFrame from an Excel file with the specified sheet name.
        
        The test should pass because:
        1. The function should read the Excel file correctly
        2. The loaded DataFrame should match the original data
        3. The function should use the specified sheet name
        """
        # Define a path for the test Excel file
        excel_path = Path(self.test_dir) / "test.xlsx"
        
        # Save the DataFrame to the Excel file directly using pandas
        with pd.ExcelWriter(excel_path, engine="openpyxl") as writer:
            self.sample_df.to_excel(writer, sheet_name="TestSheet", index=False)
        
        # Load the DataFrame using the function
        df_loaded = load_df_from_file(excel_path, sheet_name="TestSheet")
        
        # Verify the loaded DataFrame matches the original
        pd.testing.assert_frame_equal(df_loaded, self.sample_df)


if __name__ == "__main__":
    unittest.main()
