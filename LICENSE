PROPRIETARY SOFTWARE LICENSE AGREEMENT (LICENSE FILE)

Licensor: Invisible Technologies Inc. ("Licensor")

IMPORTANT: Read this Agreement carefully before installing, copying, or using the accompanying software. By installing or using the Software (defined below), you ("Licensee") agree to be bound by the terms of this Agreement. If you do not agree, do not install or use the Software and delete all copies immediately.

---

1. PURPOSE AND GOOD‑FAITH INTENT
   This License File is included solely to document ownership, permitted uses, and allocation of risk in an informal engagement. It is not intended to hinder Licensee’s operations or to impose unreasonable restrictions. If License<PERSON> believes additional rights or different terms are necessary, <PERSON>e is encouraged to contact Licensor to negotiate a mutually agreeable written amendment before deploying the Software.

---

2. DEFINITIONS

2.1 "Software" means the object‑code form of the Python package delivered by Licensor (including the corresponding wheel (WHL) file) and any related documentation, updates, or enhancements supplied by Licensor.

2.2 "Authorized Users" means employees and individual contractors of Licensee who (i) require access to the Software solely for Licensee’s internal business purposes and (ii) are bound by confidentiality obligations at least as protective as those in this Agreement.

---

3. LICENSE GRANT
   Subject to continuous compliance with this Agreement, Licensor grants Licensee a limited, non‑exclusive, non‑transferable, non‑sublicensable, worldwide, revocable license to:
   (a) install and run the Software, in object‑code form only, on hardware systems owned or controlled by Licensee; and
   (b) allow Authorized Users to use the Software solely for Licensee’s internal business operations.

---

4. DISTRIBUTION AND TRANSFER RESTRICTIONS
5. No Redistribution by Licensee. Licensee shall not distribute, publish, sublicense, lend, sell, rent, share, disclose, or otherwise make the Software (or any portion thereof) available to any third party, except to Authorized Users as defined above.
6. Licensor‑Only Distribution. The sole right to distribute the Software rests with Licensor and individuals acting on Licensor’s behalf (for example, Licensor’s employees and contractors within the scope of their duties). Any other distribution is strictly prohibited.
7. No Modification or Derivative Works. Notwithstanding that the Software source code may be visible within the distributed Python files, Licensee shall not modify, adapt, translate, create derivative works of, reverse engineer, decompile, or disassemble the Software, except to the limited extent expressly permitted by applicable law.

---

5. OWNERSHIP AND FEEDBACK
   The Software is licensed, not sold. Licensor retains all right, title, and interest in and to the Software and all intellectual‑property rights therein. All feedback, suggestions, or improvements furnished by Licensee relating to the Software are the exclusive property of Licensor; Licensee hereby assigns all such rights to Licensor.

---

6. CONFIDENTIALITY AND NON‑DISCLOSURE
   The Software, its structure, organization, and code constitute confidential and proprietary information of Licensor ("Confidential Information"). Licensee shall:
   (a) maintain the Confidential Information in strict confidence;
   (b) use it solely as permitted in Section 3;
   (c) safeguard it with at least the same degree of care it uses to protect its own confidential information (and not less than reasonable care);
   (d) not disclose, publish, or otherwise reveal any part of the Confidential Information to any person or entity other than Authorized Users; and
   (e) upon termination of this Agreement or upon Licensor’s written request, promptly destroy all copies of the Software and certify such destruction to Licensor.

---

7. NO SUPPORT OR MAINTENANCE
   Licensor has no obligation to provide support, maintenance, updates, or enhancements. Any such services provided voluntarily shall not create an ongoing obligation.

---

8. WARRANTY DISCLAIMER
   THE SOFTWARE IS PROVIDED "AS IS" WITHOUT WARRANTY OF ANY KIND. LICENSOR EXPRESSLY DISCLAIMS ALL WARRANTIES, WHETHER EXPRESS, IMPLIED, STATUTORY, OR OTHERWISE, INCLUDING WITHOUT LIMITATION ANY IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, TITLE, AND NON‑INFRINGEMENT.

---

9. LIMITATION OF LIABILITY
   IN NO EVENT SHALL LICENSOR BE LIABLE FOR ANY INDIRECT, INCIDENTAL, SPECIAL, CONSEQUENTIAL, OR PUNITIVE DAMAGES ARISING OUT OF OR RELATING TO THIS AGREEMENT OR THE USE OR INABILITY TO USE THE SOFTWARE, EVEN IF LICENSOR HAS BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGES. LICENSOR’S TOTAL CUMULATIVE LIABILITY FOR ALL CLAIMS SHALL NOT EXCEED (A) THE TOTAL AMOUNT PAID BY LICENSEE TO LICENSOR FOR THE SOFTWARE UNDER THIS AGREEMENT DURING THE TWELVE (12) MONTHS PRECEDING THE EVENT GIVING RISE TO LIABILITY OR (B) TEN THOUSAND U.S. DOLLARS (USD 10,000) IF NO SUCH FEES HAVE BEEN PAID, WHICHEVER IS GREATER.

---

10. TERM AND TERMINATION
    This Agreement is effective upon Licensee’s first installation or use of the Software and continues until terminated. Licensor may terminate this Agreement immediately upon notice (including e‑mail) if Licensee breaches any provision. Upon termination, Licensee shall cease all use of the Software and destroy all copies. Sections 4 through 6 and 8 through 14 shall survive termination.

---

11. AUDIT RIGHTS
    Upon at least ten (10) business days’ written notice, Licensor may audit Licensee’s use of the Software to verify compliance. Audits shall occur no more than once per calendar year and during normal business hours. If an audit reveals unauthorized use or disclosure, Licensee shall promptly pay Licensor any applicable fees and reimburse reasonable audit costs.

---

12. GOVERNING LAW AND VENUE
    This Agreement is governed by the laws of the State of California, United States of America, without regard to conflict‑of‑laws principles. The parties consent to the exclusive jurisdiction and venue of the state and federal courts located in San Francisco, California, for any dispute arising under this Agreement.

---

13. EXPORT COMPLIANCE
    Licensee shall comply with all applicable export laws and regulations of the United States and other jurisdictions.

---

14. ENTIRE AGREEMENT; AMENDMENT; WAIVER
    This Agreement constitutes the entire agreement between the parties regarding the Software and supersedes all prior or contemporaneous understandings. No amendment or waiver is effective unless in writing signed by Licensor. Failure to enforce any provision is not a waiver.

---

15. RUBBER DUCK PROVISION
    In the unlikely event that the Software misbehaves, Licensee agrees to first explain the issue—clearly and audibly—to a rubber duck (or functionally equivalent bath toy). If, after reasonable quacking, the problem remains unresolved, Licensee may proceed with conventional debugging methods. This clause is included solely to encourage reflective problem‑solving and imposes no legal obligations on either party (or on any aquatic fowl).

---
