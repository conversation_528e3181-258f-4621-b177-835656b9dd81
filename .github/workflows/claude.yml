name: Claude <PERSON> Review

on:
  pull_request:
    types: [opened, synchronize]  # Runs on new PRs and updates
  issue_comment:
    types: [created]  # Triggers when someone comments on an issue or PR

permissions:
  contents: read
  id-token: write
  pull-requests: write

jobs:
  code-review:
    if: github.event_name == 'pull_request'
    runs-on: ubuntu-latest
    steps:
      # Check out the code to allow git diff operations
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Fetch full history for accurate diffs

      - name: Run Code Review with Claude
        id: code-review
        uses: anthropics/claude-code-action@beta
        with:
          direct_prompt: |
            Review the PR changes. Focus on code quality, potential bugs, and performance issues. 
            Suggest improvements where appropriate.
            ${{ github.event.action == 'synchronize' && 'Focus the analysis on the changes in the last commit while considering the full context of the PR.' || '' }}

          allowed_tools: |
            ${{ github.event.action == 'synchronize' && 'Bash(git fetch origin ${{ github.event.pull_request.base.ref }} && git diff HEAD~1...HEAD --name-only),<PERSON>sh(git fetch origin ${{ github.event.pull_request.base.ref }} && git diff HEAD~1...HEAD),<PERSON><PERSON>(git show HEAD~1:{}),View,GlobTool,GrepTool' || 'Bash(git fetch origin ${{ github.event.pull_request.base.ref }} && git diff origin/${{ github.event.pull_request.base.ref }}...HEAD --name-only),Bash(git fetch origin ${{ github.event.pull_request.base.ref }} && git diff origin/${{ github.event.pull_request.base.ref }}...HEAD),View,GlobTool,GrepTool' }}

          anthropic_api_key: ${{ secrets.ANTHROPIC_API_KEY }}

  comment-review:
    if: github.event_name == 'issue_comment' && contains(github.event.comment.body, '@claude')
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Fetch full history for accurate diffs

      - name: Run Comment Review with Claude
        id: comment-review
        uses: anthropics/claude-code-action@beta
        with:
          direct_prompt: "${{ github.event.comment.body }}"
          
          allowed_tools: |
            ${{ github.event.action == 'synchronize' && 'Bash(git fetch origin ${{ github.event.pull_request.base.ref }} && git diff HEAD~1...HEAD --name-only),Bash(git fetch origin ${{ github.event.pull_request.base.ref }} && git diff HEAD~1...HEAD),Bash(git show HEAD~1:{}),View,GlobTool,GrepTool' || 'Bash(git fetch origin ${{ github.event.pull_request.base.ref }} && git diff origin/${{ github.event.pull_request.base.ref }}...HEAD --name-only),Bash(git fetch origin ${{ github.event.pull_request.base.ref }} && git diff origin/${{ github.event.pull_request.base.ref }}...HEAD),View,GlobTool,GrepTool' }}

          anthropic_api_key: ${{ secrets.ANTHROPIC_API_KEY }}
