name: Python Tests & Coverage

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.12]

    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
        
    - name: Install uv
      run: |
        pip install uv
        
    - name: Install dependencies
      run: |
        uv venv .venv
        source .venv/bin/activate
        uv pip install hatchling
        uv pip install -e .
        uv pip install -r dev-requirements.txt
        
    - name: Run tests with coverage
      run: |
        source .venv/bin/activate
        coverage erase
        coverage run --branch -m pytest demo/tests
        coverage report
        coverage xml
        
    - name: Upload coverage report to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        fail_ci_if_error: false 