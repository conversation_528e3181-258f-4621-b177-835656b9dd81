repos:
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
    -   id: trailing-whitespace
    -   id: end-of-file-fixer
    -   id: check-yaml
    -   id: check-added-large-files

-   repo: https://github.com/charliermarsh/ruff-pre-commit
    rev: 'v0.3.0'
    hooks:
    -   id: ruff
        args: [--fix, --exit-non-zero-on-fix]

-   repo: local
    hooks:
    -   id: pytest-check
        name: pytest-check
        entry: bash -c 'source .venv/bin/activate && pytest demo/tests'
        language: system
        pass_filenames: false
        always_run: true
        
    -   id: coverage-check
        name: coverage-check
        entry: bash -c 'source .venv/bin/activate && python scripts/run_coverage.py'
        language: system
        pass_filenames: false
        stages: [manual]
        always_run: true 