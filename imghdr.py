"""
imghdr shim - replacement for the removed imghdr module in Python 3.13
This tiny shim is a quick workaround for imghdr not being available in Python 3.13. (<PERSON>)

This module provides minimal functionality to satisfy dependencies.
"""


def what(file, h=None):
    """
    Tests the image data contained in the file named by file, and returns a
    string describing the image type.

    This is a simplified version of the original function with support for the
    most common image formats.
    """
    f = None
    try:
        if h is None:
            if isinstance(file, str):
                f = open(file, "rb")
                h = f.read(32)
            else:
                location = file.tell()
                h = file.read(32)
                file.seek(location)

        # PNG
        if h.startswith(b"\x89PNG\r\n\x1a\n"):
            return "png"

        # JPEG
        if h[0:2] == b"\xff\xd8":
            return "jpeg"

        # GIF
        if h[0:6] in (b"GIF87a", b"GIF89a"):
            return "gif"

        # BMP
        if h[0:2] == b"BM":
            return "bmp"

        # WEBP
        if h[0:4] == b"RIFF" and h[8:12] == b"WEBP":
            return "webp"

        return None
    finally:
        if f:
            f.close()


tests = []
