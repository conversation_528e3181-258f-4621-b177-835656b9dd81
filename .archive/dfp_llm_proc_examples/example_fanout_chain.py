"""
Example demonstrating a fan-out processing chain using the LLMProcessor.

This script shows how to use the ParallelLLMDataFrameProcessor to process
a DataFrame with a chain that includes a fan-out step, where one input
generates multiple outputs.

Usage:
    Run this script directly to see the fan-out chain in action.

Example function demonstrating a fanout prompt chain using ParallelLLMDataFrameProcessor.

In a fanout chain, one or more steps can generate multiple outputs, creating new rows in the DataFrame.
This is useful when you want to explore multiple possibilities or scenarios for each input row.
* You can use it to generate huge lists or have an LLM explore multiple routes to a solution.
* If combined with an eval_chain, you can automatically iterate.
* e.g., you can automatically optimize a prompt string. Edit it, run it, eval it, edit it, run it, eval it, etc.
* This Class is built to very efficiently handle 2 dimensional data (rows and columns), so not ideal for every use case.
* Async should work both down the rows and across the columns without batching, but haven't tested it recently.
* I can't recall what happens if the LLM output is not a list. Probably nothing good.

How fanout works:
1. A step with explode=True generates a list of items.
2. The processor creates a new row for each item in the list.
3. Each new row is a copy of the original row, but with the exploded column containing a single item from the list.
4. Subsequent steps in the chain process each of these new rows separately.

Note: Fanout chains can significantly increase the number of rows and API calls, especially
if multiple steps use explode=True. Use with caution, particularly with large datasets or long chains.

Parameters:
- def_model: Default LLM model for steps that don't specify a model.
- def_temperature: Default temperature for LLM responses.
- def_max_tokens: Default maximum number of tokens in LLM responses.
- def_async_rate_limit: Default rate limit for async API calls.
- def_thread_rate_limit: Default maximum number of concurrent threads for Grok models.
- n_rows: Number of rows to process (None = all rows).
- max_attempts: Maximum number of retry attempts for failed API calls.
"""

from dotenv import load_dotenv
from common import (
    pd,
    ParallelLLMDataFrameProcessor,
    ChainStep,
    LLM,
    save_dataframe,
    Fore,
    Style,
    run_example,
)

load_dotenv()


def example_fanout_chain():
    # 1. Create sample data
    data = {
        "name": ["Alice", "Bob", "Charlie"],
        "age": [25, 30, 35],
        "occupation": ["Engineer", "Teacher", "Doctor"],
        "location": ["New York", "London", "Tokyo"],
    }
    df = pd.DataFrame(data)

    # 2. Initialize processor
    llm_proc = ParallelLLMDataFrameProcessor()

    # 3. Define chain steps
    chain = [
        ChainStep(
            pt="""
            List 3 potential career paths for {name}, a {age}-year-old {occupation} in {location}.
            Output as a comma-separated list.
            Keep each career path suggestion concise, using only a few words.
            """,
            model=LLM.gpt_4o,
            temperature=0.8,
            max_tokens=150,
            col="career_paths",
            fanout=True,
        ),
        ChainStep(
            pt="""
            For {name} in {location}, compare their current job as a {occupation} with the potential career as a {career_paths}.
            List pros and cons.
            Keep your response concise, focusing on the most significant points.
            """,
            model=LLM.claude_3_5_sonnet,
            temperature=0.6,
            max_tokens=200,
            col="career_comparison",
        ),
        ChainStep(
            pt="""
            Suggest a specific next step {name} could take to transition from being a {occupation} to a {career_paths} in {location}.
            Provide a single, actionable step.
            Keep your response concise and focused.
            """,
            # model=LLM.fire_llama_v31_70b_instruct,
            model=LLM.gpt_4o_mini,
            temperature=0.7,
            max_tokens=150,
            col="next_step",
        ),
    ]

    # 4. Execute chain
    result_df = llm_proc.execute_chain(df, chain)

    # 5. Display results and save
    print(f"{Fore.CYAN}Number of rows: {len(result_df)}{Style.RESET_ALL}")
    for _, row in result_df.iterrows():
        print(
            f"{Fore.YELLOW}Name: {row['name']}{Fore.RED}, Age: {row['age']}{Fore.BLUE}, Location: {row['location']}{Style.RESET_ALL}"
        )
        print(
            f"{Fore.GREEN}Current Occupation: {row['occupation']}{Style.RESET_ALL}"
        )
        print(
            f"{Fore.BLUE}Potential Career: {row['career_paths']}{Style.RESET_ALL}"
        )
        print(
            f"{Fore.MAGENTA}Career Comparison: {row['career_comparison'][:150]}...{Style.RESET_ALL}"
        )
        print(
            f"{Fore.CYAN}Next Step: {row['next_step'][:150]}{Style.RESET_ALL}"
        )
        print("-" * 50)

    save_dataframe(result_df, "fanout_chain_results")


if __name__ == "__main__":
    run_example(example_fanout_chain)
