"""
Common setup for example scripts.

This module provides common imports and configurations used across example scripts.
It should be imported at the beginning of each example script.
"""

import sys
import os

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from loguru import logger

# Configure Loguru
logger.remove()  # Remove default handler
logger.add(
    sys.stderr,
    format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <dim><normal>{name}</normal>:<normal>{function}</normal>:<normal>{line}</normal></dim>: \n ----> <level>{message}</level>",
    level="INFO",
)


def run_example(example_func):
    """
    Wrapper function to run examples with proper setup and error handling.
    """
    try:
        example_func()
    except Exception as e:
        logger.error(f"Error running example: {str(e)}")
        raise
