"""
Example demonstrating how to process a single list of prompts using the LLMProcessor.

This script shows how to use the ParallelLLMDataFrameProcessor to process
a DataFrame containing a list of prompts, generating explanations for each.

Usage:
    Run this script directly to see the single list of prompts processing in action.


"""
# basic setup
from common import (pd, ParallelLLMDataFrameProcessor, ChainStep, LLM, Fore, Style)
import litellm

# Set pandas display options
pd.set_option('display.max_columns', None)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.max_colwidth', 20)  # Adjust this value as needed

# Set litellm to verbose mode
import os
os.environ['LITELLM_LOG'] = 'DEBUG'
litellm.set_verbose = False

# Create a single instance of ParallelLLMDataFrameProcessor
proc = ParallelLLMDataFrameProcessor()  # re-use the same instance for all examples

# -------------------------------------------------------------------------------------------------
# basic chain with no dependencies and minimum setup
def example_single_list_of_prompts(prompts, new_col_name):
    df = pd.DataFrame({"prompt": prompts})
    result_df = proc.execute_chain(df, [ChainStep(pt="{prompt}", model=LLM.gpt_4o_mini, temperature=0.1, max_tokens=1000, col=new_col_name)])
    print(result_df[["prompt", new_col_name]])


print("\n\n\nRunning single list of prompts Example:")
example_single_list_of_prompts(["Name a color:", "List a fruit:", "Suggest a hobby:", "Mention a country:"], new_col_name="quickie")
# -------------------------------------------------------------------------------------------------

# -------------------------------------------------------------------------------------------------
# simplest possible prompt chain with dependencies (templates)
def example_chain_with_dependencies(prompts):
    df = pd.DataFrame({"initial_prompt": prompts})
    chain = [
        ChainStep(pt="{initial_prompt}", model=LLM.claude_3_5_sonnet, temperature=0.7, max_tokens=50, col="response1"),
        ChainStep(pt="Elaborate on this: {response1}", model=LLM.gpt_4o_mini, temperature=0.5, max_tokens=100, col="response2")
    ]
    result_df = proc.execute_chain(df, chain)
    print(result_df[["initial_prompt", "response1", "response2"]])


print("\n\n\nRunning chain with dependencies Example:")
example_chain_with_dependencies(["Tell me a joke", "Give me a writing prompt", "Suggest a recipe"])
# -------------------------------------------------------------------------------------------------


# # -------------------------------------------------------------------------------------------------
# same list of prompts (rows), 3 different models, one per column output)
def example_different_models(prompts):
    df = pd.DataFrame({"prompt": prompts})
    chain = [
        ChainStep(pt="{prompt}", model=LLM.gpt_4o_mini, temperature=0.3, max_tokens=100, col="gpt4_response"),
        ChainStep(pt="{prompt}", model=LLM.claude_3_5_sonnet, temperature=0.3, max_tokens=100, col="claude_response"),
        ChainStep(pt="{prompt}", model=LLM.gpt_3_5_turbo, temperature=0.3, max_tokens=100, col="gpt3_response")
    ]
    result_df = proc.execute_chain(df, chain)
    print(result_df[["prompt", "gpt4_response", "claude_response", "gpt3_response"]])

print("\n\n\nRunning different models Example:")
example_different_models([
    "Explain quantum computing",
    "Write a haiku about spring",
    "Describe the taste of umami"
])
# -------------------------------------------------------------------------------------------------

# # -------------------------------------------------------------------------------------------------
def example_simple_fanout(prompts):
    df = pd.DataFrame({"prompt": prompts})
    chain = [
        ChainStep(pt="List 3 related words for: {prompt}", model=LLM.gpt_3_5_turbo, temperature=0.5, max_tokens=50, col="related_words", fanout=True),
        ChainStep(pt="Define this word: {related_words}", model=LLM.gpt_4o_mini, temperature=0.2, max_tokens=100, col="definition")
    ]
    result_df = ParallelLLMDataFrameProcessor().execute_chain(df, chain)
    print(result_df[["prompt", "related_words", "definition"]])

print("\n\n\nRunning simple fanout Example:")
example_simple_fanout(["ocean", "mountain", "forest"])
# # -------------------------------------------------------------------------------------------------

# # -------------------------------------------------------------------------------------------------
# simple fanout example how to make a big list of something:
def example_big_list_fanout(topics):
    df = pd.DataFrame({"topic": topics})
    chain = [
        ChainStep(pt="List 5 {topic}:", model=LLM.gpt_3_5_turbo, temperature=0.7, max_tokens=100, col="items", fanout=True),
        ChainStep(pt="Describe this in one sentence: {items}", model=LLM.gpt_4o_mini, temperature=0.5, max_tokens=50, col="description")
    ]
    result_df = ParallelLLMDataFrameProcessor().execute_chain(df, chain)
    print(result_df[["topic", "items", "description"]])

print("\n\n\nRunning big list fanout Example:")
example_big_list_fanout(["fruits", "countries", "programming languages"])


# # -------------------------------------------------------------------------------------------------

# -------------------------------------------------------------------------------------------------
# fanout example for completing several steps in parallel, then merging again:
def example_parallel_steps_fanout(prompts):
    df = pd.DataFrame({"prompt": prompts})
    chain = [
        ChainStep(pt="1. Summarize {prompt} in one sentence.", model=LLM.gpt_4o_mini, temperature=0.5, max_tokens=50, col="summary", fanout=True),
        ChainStep(pt="2. List three key points about {prompt}.", model=LLM.gpt_4o_mini, temperature=0.5, max_tokens=100, col="key_points", fanout=True),
        ChainStep(pt="3. Provide an analogy for {prompt}.", model=LLM.gpt_4o_mini, temperature=0.7, max_tokens=50, col="analogy", fanout=True),
        ChainStep(pt="Combine the following into a coherent paragraph:\nSummary: {summary}\nKey Points: {key_points}\nAnalogy: {analogy}",
                  model=LLM.gpt_4o_mini, temperature=0.5, max_tokens=200, col="final_output")
    ]
    result_df = ParallelLLMDataFrameProcessor().execute_chain(df, chain)
    print(result_df[["prompt", "summary", "key_points", "analogy", "final_output"]])
    for output in result_df["final_output"]:
        print(output)

# print("Running parallel steps fanout Example:")
# example_parallel_steps_fanout(["artificial intelligence", "climate change", "space exploration"])
# # -------------------------------------------------------------------------------------------------
# # fanout example for completing several *instructional* steps from a prompt in parallel, then merging again. First prompt gives instructions, second executes them in parallel, third merges them.
# # -------------------------------------------------------------------------------------------------


def example_goal_achievement_fanout(goals):
    df = pd.DataFrame({"goal": goals})
    chain = [
        ChainStep(pt="List 5 specific tasks to achieve this goal: '{goal}'",
                  model=LLM.gpt_3_5_turbo, temperature=0.5, max_tokens=150, col="tasks"),
        ChainStep(pt="Complete this task to achieve the goal '{goal}': {{tasks}}",
                  model=LLM.gpt_4o_mini, temperature=0.3, max_tokens=200, col="task_results", fanout=True),
        ChainStep(pt="Synthesize these results into a coherent plan to achieve the goal '{goal}':\n{{task_results}}",
                  model=LLM.claude_3_5_sonnet, temperature=0.4, max_tokens=300, col="final_plan")
    ]
    result_df = ParallelLLMDataFrameProcessor().execute_chain(df, chain)
    print(result_df[["goal", "tasks", "task_results", "final_plan"]])
    for goal, plan in zip(result_df["goal"], result_df["final_plan"]):
        print(f"{Fore.CYAN}Goal: {goal}{Style.RESET_ALL}")
        print(f"{Fore.GREEN}{plan}{Style.RESET_ALL}\n")


print("\n\nRunning goal achievement fanout Example:")
example_goal_achievement_fanout([
    "Write a compelling short story in the mystery genre",
    # "Create a comprehensive study plan for a difficult exam",
])
# -------------------------------------------------------------------------------------------------

# -------------------------------------------------------------------------------------------------

# -------------------------------------------------------------------------------------------------



# -------------------------------------------------------------------------------------------------
# Ensemble Approach. (varied on temp and model), resolved via simple scorer. Notes on various ensemble methods at the end of script.
"""
This is interesting because Sonnet one-shot this. "OK - let's try this one, read through the other examples above it carefully in thi interaction to reason about how to achieve it then we'll execute the code in the next interaction"
Then: "OK - let's try this one, read through the other examples above it carefully in thi interaction to reason about how to achieve it then we'll execute the code in the next interaction"
... and it works. But it's too much eh, we just want to vary on temperature most of the time.
There's obviously a lot of potential for constructors / loops below but let's keep it as-is for comparison to the above examples.
NOTE: we could quite easily make this a parametised flag in the main Class (@processor.py) - right?
      i.e., if ensemble = true then any chain step will run the same chain step 5 times with varied temp and model, scoring and selecting the best one.
      We would ideally be able to configure it but there's too many params so a sub-class that we can 'push' config to.
"""
def example_ensemble_approach(prompts):
    df = pd.DataFrame({"prompt": prompts})
    chain = [
        # Generate responses with different models and temperatures
        ChainStep(pt="{prompt}", model=LLM.gpt_4o_mini, temperature=0.2, max_tokens=150, col="gpt4_low_temp"),
        ChainStep(pt="{prompt}", model=LLM.gpt_4o_mini, temperature=0.8, max_tokens=150, col="gpt4_high_temp"),
        ChainStep(pt="{prompt}", model=LLM.claude_3_5_sonnet, temperature=0.2, max_tokens=150, col="claude_low_temp"),
        ChainStep(pt="{prompt}", model=LLM.claude_3_5_sonnet, temperature=0.8, max_tokens=150, col="claude_high_temp"),
        ChainStep(pt="{prompt}", model=LLM.gpt_3_5_turbo, temperature=0.5, max_tokens=150, col="gpt3_mid_temp"),
        
        # Score each response
        ChainStep(pt="Rate the following response to the prompt '{prompt}' on a scale of 1-10 for relevance, coherence, and creativity. Only output the numeric score.\nResponse: {gpt4_low_temp}", 
                  model=LLM.gpt_4o_mini, temperature=0.1, max_tokens=10, col="score_gpt4_low"),
        ChainStep(pt="Rate the following response to the prompt '{prompt}' on a scale of 1-10 for relevance, coherence, and creativity. Only output the numeric score.\nResponse: {gpt4_high_temp}", 
                  model=LLM.gpt_4o_mini, temperature=0.1, max_tokens=10, col="score_gpt4_high"),
        ChainStep(pt="Rate the following response to the prompt '{prompt}' on a scale of 1-10 for relevance, coherence, and creativity. Only output the numeric score.\nResponse: {claude_low_temp}", 
                  model=LLM.gpt_4o_mini, temperature=0.1, max_tokens=10, col="score_claude_low"),
        ChainStep(pt="Rate the following response to the prompt '{prompt}' on a scale of 1-10 for relevance, coherence, and creativity. Only output the numeric score.\nResponse: {claude_high_temp}", 
                  model=LLM.gpt_4o_mini, temperature=0.1, max_tokens=10, col="score_claude_high"),
        ChainStep(pt="Rate the following response to the prompt '{prompt}' on a scale of 1-10 for relevance, coherence, and creativity. Only output the numeric score.\nResponse: {gpt3_mid_temp}", 
                  model=LLM.gpt_4o_mini, temperature=0.1, max_tokens=10, col="score_gpt3_mid"),
        
        # Select the best response
        ChainStep(pt="""
            Select the highest-scoring response from the following:
            1. (Score: {score_gpt4_low}) {gpt4_low_temp}
            2. (Score: {score_gpt4_high}) {gpt4_high_temp}
            3. (Score: {score_claude_low}) {claude_low_temp}
            4. (Score: {score_claude_high}) {claude_high_temp}
            5. (Score: {score_gpt3_mid}) {gpt3_mid_temp}
            Output only the number of the highest-scoring response.""", model=LLM.gpt_4o_mini, temperature=0.1, max_tokens=10, col="best_response_index"),
    ]
    
    result_df = ParallelLLMDataFrameProcessor().execute_chain(df, chain)
    # Map the best response index to the actual response
    response_columns = ["gpt4_low_temp", "gpt4_high_temp", "claude_low_temp", "claude_high_temp", "gpt3_mid_temp"]
    result_df["best_response"] = result_df.apply(lambda row: row[response_columns[int(row["best_response_index"]) - 1]], axis=1)
    
    print(result_df[["prompt", "best_response", "best_response_index"] + response_columns])

print("\n\nRunning Ensemble Approach Example:")
example_ensemble_approach([
    "Explain the concept of quantum entanglement to a 10-year-old",
    "Write a short poem about the beauty of mathematics",
    "Describe the taste of a food that doesn't exist"
])

# -------------------------------------------------------------------------------------------------
# Simpler Ensemble Approach. (varied on just temp), resolved via un-fanned disagreement lister. 
# Count(disagreements) as confidence score for whole row.




# TODO: have a retry mechanism that can be called from outside the Class. This would be more straight forward with the DependencyExecutor / DependencyGraph.
#       as dependendants that already started/finished can be put back into the queue. (infra for that needs to be built into Dependency Executor from the start.)
# TODO: use the N param that most LLMs have to allow multiple outputs (for e.g., ensembles!). 
# N would need to be set at the level of the call, but then we need an elegant way of handling multiple outputs from that point onwards without breaking everything. 
# Ideal behaviour would probably be to have a default N=1 but allow the option to expand to N outputs in a clean way, utilising the fanout method?
"""
class ChainStep(NamedTuple):
    pt: str
    mapping: Optional[Dict[str, str]] = None
    [other params]
    n: int = 1  # New parameter for number of outputs, default = 1. <-----------------------
    combine_outputs: bool = False  # Whether to combine multiple outputs into a single column <----------------------- INTERESTING. Ensemble outputs don't actually need to each have their own column. Could just be uniquely delimited list in a single cell. Not 100% though.. less inspectable for comparison. 
    
async def _async_get_response(
        self,
        message: str,
        temperature: float,
        max_tokens: int,
        model: str,
        returns_list: bool,
        n: int = 1,    # <-----------------------
    ) -> Union[str, List[str], List[List[str]]]:
        async with self.throttler:
            # ... existing code ...
            response = await litellm.acompletion(
                model=model,
                messages=[{"role": "user", "content": message}],
                temperature=temperature,
                max_tokens=max_tokens,
                n=n,  # Use the n parameter  # <-----------------------
            )


chain = [
    ChainStep(pt="Generate an idea for {topic}", n=3, col="ideas", fanout=True),
    ChainStep(pt="Elaborate on idea: {ideas}", col="elaborations"),
    ChainStep(pt="Summarize elaborations: {elaborations}", unfan_col="topic", col="summary", n=2, combine_outputs=True) # bit dodge having unfan AND combine_outputs, no?
]
result_df = processor.execute_chain(df, chain)
In this example:
1. The first step generates 3 ideas, creating 3 separate rows (fanout).
2. The second step elaborates on each idea individually.
3. The third step summarizes the elaborations twice for each topic, combining the two summaries into a list in a single column.

NOTE: the second n=2 is good explanation of what a wierd choice would do here.
NOTE: still not 100% on combine_outputs.

# next solution used fanout and unfan, nice and elegant but...
# let's actually default to ensembles expanding into new columns with _A _B _C etc as I have a feeling unfan isn't that thread safe (because groupby)



"""



# 1. **Best-of-N:** Run the model N times and select the output based on the best predefined metric. "Best" can just be defined by the LLM itself. 
# / Majority Voting is a simple way to do this that just asks "which 2/3 or 4/5 agree?". Re-run if not enough consensus. 

# 2. **Disagreement Counting :** Run the model multiple times and select the output that appears most frequently or has the least disagreement.
# Count(disagreements) / Count(total) > 0.5, re-run. -> essentially a proxy for confidence.

# 3. **Weighted Voting with Confidence Scores:** Assign weights to outputs based on metrics and select the one with the highest cumulative weight.

# 4. **CoT Consistency Checks:** Validate the logical consistency of CoT reasoning steps and discard inconsistent outputs.

# 5. **K-Nearest Neighbor (K-NN) on CoT Chains:** Use edit distance or similar + old verified results. Compare new reasoning chains to past successful ones and select based on similarity scores.

# 6. **Self-Critique Step:** Have the thread (include prev message history) critique its own outputs and select the one with the fewest issues.

# 7. **Majority Voting with Reranking:** Use majority voting, then rerank tied outputs using a secondary heuristic like reasoning complexity.

# 8. **Cross-Model Verification:** Generate outputs from different models or settings and have one verify the reasoning of another.

# 9. **Reranking via CoT Reasoning with Verifier Models:** Generate CoT outputs, then run a verifier model to ensure correctness and select verified outputs.

# 10. **Stacking (if training were feasible):** Train a meta-classifier to weigh and select outputs based on reasoning patterns.