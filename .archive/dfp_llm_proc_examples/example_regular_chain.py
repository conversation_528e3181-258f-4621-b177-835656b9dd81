"""
Example function demonstrating a regular prompt chain using ParallelLLMDataFrameProcessor.

In a regular chain, each step processes the output of the previous step without creating new rows.
This is useful for sequential processing where each step builds upon the previous one.
* This Class is built to very efficiently handle 2 dimensional data (rows and columns), so not ideal for every use case.
* Async should work both down the rows and across the columns without batching, but haven't tested it recently.

Parameters:
- def_model: Default LLM model for steps that don't specify a model.
- def_temperature: Default temperature for LLM responses.
- def_max_tokens: Default maximum number of tokens in LLM responses.
- def_async_rate_limit: Default rate limit for async API calls.
- def_thread_rate_limit: Default maximum number of concurrent threads for Grok models.
- n_rows: Number of rows to process (None = all rows).
- max_attempts: Maximum number of retry attempts for failed API calls.
- prompt_chain: List of ChainStep objects defining the prompt chain.
    - pt: Prompt template string. {col_name} pulls the full value for that col name from the curent dataframe row.
    - mapping: Optional dictionary for prompt keyword mapping
    - temperature: Optional temperature for this step
    - max_tokens: Optional max tokens for this step
    - model: Which LLM to use for this step. See the LLM Class for options. Should auto-complete.
    - col: Column name for this step's LLM Output (Response). (default: "response")
    - explode: Whether to explode response as list (default: False) see below for example.
    - overwrite: Whether to overwrite existing column (default: False)
- output_folder: Folder to save output files.
- output_name: Base name for output files.
- save_intermediates: Whether to save intermediate results after each step.
"""

from dotenv import load_dotenv
from common import (
    pd,
    ParallelLLMDataFrameProcessor,
    ChainStep,
    LLM,
    save_dataframe,
    Fore,
    Style,
    run_example,
)

load_dotenv()


def example_regular_chain():
    # 1. Create sample data
    data = {
        "name": ["Alice", "Bob", "Charlie"],
        "age": [25, 30, 35],
        "occupation": ["Engineer", "Teacher", "Doctor"],
        "location": ["New York", "London", "Tokyo"],
    }
    df = pd.DataFrame(data)

    # 2. Initialize processors
    llm_proc = ParallelLLMDataFrameProcessor()

    # 3. Define chain steps
    chain = [
        ChainStep(
            pt="""
            Describe a typical day for {name}, a {age}-year-old {occupation} living in {location}.
            Keep your response concise, focusing on key activities and routines.
            """,
            col="typical_day",
        ),
        ChainStep(
            pt="""
            Based on {name}'s typical day as a {occupation} in {location}, suggest a hobby that would complement their lifestyle.
            Provide a brief explanation for your suggestion. Keep your response concise.
            """,
            model=LLM.claude_3_5_sonnet,
            temperature=0.6,
            max_tokens=100,
            col="suggested_hobby",
        ),
        ChainStep(
            pt="""
            Describe how {name}'s life in {location} might change if they took up {suggested_hobby}.
            Consider their job as a {occupation} and their typical day.
            Focus on the most significant impacts and keep your response concise.
            """,
            # model=LLM.fire_llama_v31_70b_instruct,
            model=LLM.gpt_4o_mini,
            temperature=0.7,
            max_tokens=250,
            col="life_changes",
        ),
    ]

    # 4. Execute chain
    result_df = llm_proc.execute_chain(df, chain)

    # 5. Display results and save
    print(f"{Fore.CYAN}Number of rows: {len(result_df)}{Style.RESET_ALL}")
    for _, row in result_df.iterrows():
        print(
            f"{Fore.YELLOW}Name: {row['name']}{Style.RESET_ALL}{Fore.GREEN} Occupation: {row['occupation']}{Style.RESET_ALL}{Fore.BLUE} Age: {row['age']}{Style.RESET_ALL}{Fore.MAGENTA} Location: {row['location']}{Style.RESET_ALL}"
        )
        print(
            f"{Fore.GREEN}Typical Day: {row['typical_day'][:150]}...{Style.RESET_ALL}"
        )
        print(
            f"{Fore.BLUE}Suggested Hobby: {row['suggested_hobby'][:150]}{Style.RESET_ALL}"
        )
        print(
            f"{Fore.MAGENTA}Life Changes: {row['life_changes'][:150]}...{Style.RESET_ALL}"
        )
        print("-" * 50)

    save_dataframe(result_df, "regular_chain_results")


if __name__ == "__main__":
    run_example(example_regular_chain)
