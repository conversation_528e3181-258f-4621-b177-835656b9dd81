import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import time
import re
from concurrent.futures import ThreadPoolExecutor, as_completed
from queue import Queue
from tqdm import tqdm


def get_domain(url):
    return urlparse(url).netloc


def is_internal_url(url, domain):
    return get_domain(url) == domain


def extract_content(soup, sidebar_items):
    for item, include in sidebar_items:
        for element in soup.find_all(string=re.compile(re.escape(item))):
            if element.parent.name != "title":
                if not include:
                    element.parent.decompose()
                else:
                    element.parent["style"] = "background-color: yellow;"

    main_content = soup.find("main")
    if main_content:
        # Preserve hyperlinks
        for a in main_content.find_all("a", href=True):
            a.replace_with(f"{a.get_text()} [{a['href']}]")
        text = main_content.get_text(separator="\n", strip=True)
    else:
        text = soup.get_text(separator="\n", strip=True)

    images = []
    for img in soup.find_all("img"):
        src = img.get("src")
        alt = img.get("alt", "")
        if src:
            images.append((src, alt))

    return text, images


def process_url(url, domain, sidebar_items):
    try:
        print(f"Crawling: {url}")
        response = requests.get(url)

        if response.status_code == 200:
            soup = BeautifulSoup(response.text, "html.parser")

            text, images = extract_content(soup, sidebar_items)

            content = f"URL: {url}\n\n{text}\n\nImages:\n"
            for img_src, img_alt in images:
                content += f"[{img_src}] - {img_alt}\n"
            content += f"\n{'='*50}\n"

            new_links = []
            for link in soup.find_all("a", href=True):
                new_url = urljoin(url, link["href"])
                if is_internal_url(new_url, domain):
                    new_links.append(new_url)

            return content, new_links
    except Exception as e:
        print(f"Error crawling {url}: {e}")
        return None, []


def crawl(start_url, max_pages=100, sidebar_items=None, max_workers=10):
    domain = get_domain(start_url)
    visited = set()
    to_visit = Queue()
    to_visit.put(start_url)
    extracted_content = []

    print(f"Starting crawl from {start_url}")
    print(f"Target domain: {domain}")

    progress_bar = tqdm(total=max_pages, desc="Pages crawled")

    while len(visited) < max_pages and not to_visit.empty():
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_url = {}
            while len(visited) < max_pages and not to_visit.empty():
                url = to_visit.get()
                if url not in visited:
                    visited.add(url)
                    future = executor.submit(
                        process_url, url, domain, sidebar_items
                    )
                    future_to_url[future] = url
                if len(future_to_url) >= max_workers or to_visit.empty():
                    break

            for future in as_completed(future_to_url):
                url = future_to_url[future]
                try:
                    content, new_links = future.result()
                    if content:
                        extracted_content.append(content)
                        progress_bar.update(1)
                    for new_url in new_links:
                        if new_url not in visited and len(visited) < max_pages:
                            to_visit.put(new_url)
                except Exception as e:
                    print(f"Error processing result from {url}: {e}")

        time.sleep(0.1)

    progress_bar.close()
    print("Crawling complete!")
    return extracted_content


sidebar_items = [
    ("Prompt Engineering", True),
    ("Introduction", True),
    ("LLM Settings", True),
    ("Basics of Prompting", True),
    ("Prompt Elements", True),
    ("General Tips for Designing Prompts", True),
    ("Examples of Prompts", True),
    ("Techniques", True),
    ("Zero-shot Prompting", True),
    ("Few-shot Prompting", True),
    ("Chain-of-Thought Prompting", True),
    ("Self-Consistency", True),
    ("Generate Knowledge Prompting", True),
    ("Prompt Chaining", True),
    ("Tree of Thoughts", True),
    ("Retrieval Augmented Generation", True),
    ("Automatic Reasoning and Tool-use", True),
    ("Automatic Prompt Engineer", True),
    ("Active-Prompt", True),
    ("Directional Stimulus Prompting", True),
    ("Program-Aided Language Models", True),
    ("ReAct", True),
    ("Reflexion", True),
    ("Multimodal CoT", True),
    ("Graph Prompting", True),
    ("Applications", False),
    ("Function Calling", False),
    ("Context Caching with LLMs", False),
    ("Generating Data", False),
    ("Generating Synthetic Dataset for RAG", False),
    ("Tackling Generated Datasets Diversity", False),
    ("Generating Code", False),
    ("Graduate Job Classification Case Study", False),
    ("Prompt Function", False),
    ("Prompt Hub", False),
    ("Models", False),
    ("Risks & Misuses", False),
    ("Papers", False),
    ("Tools", False),
    ("Notebooks", False),
    ("Datasets", False),
    ("Additional Readings", False),
]


def main():
    start_url = "https://www.promptingguide.ai"
    max_pages = 100
    max_workers = 10

    print(f"Starting crawl from {start_url}")
    print(f"Max pages: {max_pages}")
    print(f"Max workers: {max_workers}")

    results = crawl(
        start_url,
        max_pages=max_pages,
        sidebar_items=sidebar_items,
        max_workers=max_workers,
    )

    with open(
        "/Users/<USER>/Documents/Code2/df_chain_processor_v3/prompt_constructors/pe_guide_content.txt",
        "w",
        encoding="utf-8",
    ) as f:
        for content in results:
            f.write(content)

    print(
        f"Crawled {len(results)} pages. Content saved to 'crawled_content.txt'"
    )


if __name__ == "__main__":
    main()
