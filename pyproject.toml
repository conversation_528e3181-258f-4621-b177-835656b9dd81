# ------------------------------ PROJECT CONFIG ------------------------------ #
[project]
name = "llm-agent-excel"
version = "0.1.0"
description = "LLM Workflow Tools, 'Delegates', Agentic etc for Excel & Gsheets"
readme = "README.md"
requires-python = ">=3.12"
# ------------------ RUNTIME / PRODUCTION DEPENDENCIES ---------------------- #
dependencies = [
    "openai>=1.61.0",
    "pandas>=2.2.2",
    "loguru>=0.7.2", # structured logging
    "litellm>=1.61.16",
    "tenacity>=8.3.0", # retry utilities
    "asyncio-throttle>=1.0.2",
    "python-dotenv>=1.0.1", # .env loader for secrets
    "httpx>=0.27.0", # async HTTP client
    "gspread>=6.2.0", # Core Google Sheets API wrapper
    "gspread-dataframe>=4.0.0", # DataFrame support for Gspread
    "openpyxl>=3.1.5", # Excel file reader/writer
    "google-api-python-client>=2.169.0", # Google API client
    "google-auth-oauthlib>=1.2.2", # Google auth for OAuth
    "google-auth>=2.40.1", # Google auth library
    "questionary>=2.0.1", # interactive CLI dropdowns
]

# ------------------ DEVELOPMENT ONLY DEPENDENCIES (handled by uv with `--dev`) -------- #
[tool.uv]
dev-dependencies = [
    "pytest>=7.4.0",
    "pytest-cov>=6.0.0",
    "ruff>=0.3.0",                     # Primary linter when not using Trunk.io
    "pylint>=3.3.7",                   # secondary linter - cross file checks
    "colorama>=0.4.6",                 # coloured terminal output
    "rich>=14.0.0",                    # enhanced console prints, colors, panels, progress bars etc
    "tqdm>=4.66.4",                    # progress bars – CLI feedback
    "pyperclip>=1.9.0",                # clipboard helpers
    "pre-commit>=3.5.0",               # pre-commit hooks
]

# ------------------ BUILD CONFIG ------------------------------ #
[tool.hatch.build.targets.wheel]
packages = ["demo/main.py"]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

# ------------------ RUFF LINTING CONFIG ----------------------------- #
[tool.ruff]
line-length = 150                      # AF: Stick to 150 please

[tool.ruff.lint]
select = ["E", "F", "ANN"]             # Added ANN for type annotation enforcement

# Type annotation (ANN) specific settings
[tool.ruff.lint.pydocstyle]
convention = "google"

[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["ANN001", "ANN002", "ANN003"]  # Don't require annotations in __init__.py files
"test_*.py" = ["ANN001", "ANN201"]              # Test files don't need return annotations

[tool.ruff.lint.isort]
known-first-party = ["demo"]

[tool.ruff.lint.flake8-annotations]
allow-star-arg-any = true
suppress-none-returning = true
mypy-init-return = true

# ------------------ PYLINT CONFIG ----------------------------- #
[tool.pylint.main]
max-line-length = 150                  # Match Ruff's line length setting
ignore-patterns = ["^\\.#"]

[tool.pylint."messages control"]
# Disable certain messages to avoid overlap with Ruff
disable = [
    "missing-docstring",               # If using Ruff for docstring checks
    "invalid-name",                    # For short variable names
    "too-many-arguments",              # Often false positive
    "too-many-locals",                 # Often false positive
    "no-name-in-module",               # Can give false positives with imports
    "fixme",                           # Allow TODOs
    "line-too-long",                   # Handled by Ruff
]

[tool.pylint.typecheck]
# Configure type checking behavior
generated-members = ["numpy.*", "torch.*"]  # For libraries that dynamically generate members

[tool.pylint.format]
# Format configuration
max-module-lines = 1500                # Set maximum lines per module

[tool.pylint.design]
# Design checks
max-args = 8                           # Max number of arguments for functions
max-attributes = 15                    # Max number of attributes for classes
min-public-methods = 0                 # Allow classes with no public methods

[tool.pylint.similarities]
# Duplicate code detection
min-similarity-lines = 6               # Minimum lines to consider as duplicate
ignore-comments = true                 # Don't consider comments
ignore-docstrings = true               # Don't consider docstrings
ignore-imports = true                  # Don't consider imports
